﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("满即送")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("活动列表")</span></a></li>
                <li><a href="@Url.Action("FullGiftPackage")"><span>@T("套餐管理")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("FullGiftSetting")', '@T("设置")')"><span>@T("设置")</span></a></li>
            </ul>
        </div>
    </div>

    <form method="get" name="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("活动名称")</dt>
                <dd><input type="text" value="@Model.name" name="name" id="name" class="txt" style="width:100px;"></dd>
            </dl>
            <dl>
                <dt>@T("店铺名称")</dt>
                <dd>
                    <select id="storeId" name="storeId" class="form-control" style="width:200px;">
                        <!option value="">@T("全部")</!option>
                        @foreach (var store in Model.storelist)
                        {
                            <!option value="@store.Id" @(Model.storeId == store.Id ? "selected" : "")>@store.Name</!option>
                        }
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("状态")</dt>
                <dd>
                    <select name="status">
                        <!option value="0" @(Model.status == 0 ? "selected" : "")>@T("全部")</!option>
                        <!option value="1" @(Model.status == 1 ? "selected" : "")>@T("正常")</!option>
                        <!option value="2" @(Model.status == 2 ? "selected" : "")>@T("已结束")</!option>
                        <!option value="3" @(Model.status == 3 ? "selected" : "")>@T("管理员关闭")</!option>
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <a href="javascript:document.formSearch.submit();" class="btn" title="@T("查询")">@T("查询")</a>
            </div>
        </div>
    </form>
    <!-- 帮助 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="提示相关设置操作时应注意的要点">@T("操作提示")</h4>
            <span id="explanationZoom" title="收起提示" class="arrow"></span>
        </div>
        <ul>
            <li>@T("卖家发布的满即送活动列表")</li>
            <li>@T("取消操作后的活动不可恢复，请慎重操作")</li>
            <li>@T("点击详细按钮，查看活动详细信息")</li>
        </ul>
    </div>


    <!-- 列表 -->
    <table class="ds-default-table">
        <thead>
            <tr class="thead">
                <th class="w24"></th>
                <th class="align-left"><span>@T("活动名称")</span></th>
                <th class="align-left" width="200"><span>@T("店铺名称")</span></th>
                <th class="align-center" width="100"><span>@T("开始时间")</span></th>
                <th class="align-center" width="100"><span>@T("结束时间")</span></th>
                <th class="align-center" width="80"><span>@T("状态")</span></th>
                <th class="align-center" width="200"><span>@T("操作")</span></th>
            </tr>
        </thead>
        <tbody id="treet1">
            @foreach (FullGift item in Model.list)
            {
                <tr class="hover" id="ds_row_1">
                    <td><input type="checkbox" class="checkitem" name="fullgiftid[]" value="@item.Id" /></td>
                    <td class="align-left"><span>@item.Name</span></td>
                    <td class="align-left">
                        <a href="@Url.Action("ProviderDetail", "Supplier", new { Area = "", sid = item.StoreId })"><span>@item.StoreName</span></a>
                    </td>
                    <td class="align-center"><span>@(UnixTime.ToDateTime(item.StartTime))</span></td>
                    <td class="align-center"><span>@(UnixTime.ToDateTime(item.EndTime))</span></td>
                    @* 1:未发布 2:正常 3:取消 4:失效 5:结束" *@
                    <td class="align-center">
                        <span>
                            @if (UnixTime.ToTimestamp() > item.EndTime)
                            {
                                @T("已结束")
                            }
                            else
                            {
                               @(item.State switch{
                                    1 => T("正常"),
                                    2 => T("已结束"),
                                    3 => T("管理员关闭"),
                                    _ => T("未知")
                                })
                            }
                        </span>
                    </td>

                    <td class="nowrap align-center">
                        <!-- 详细按钮 -->
                        <a href="javascript:dsLayerOpen('@Url.Action("FullGiftDetail", new { id = item.Id })','@T("测试详细")')" class="dsui-btn-view"><i class="iconfont"></i>@T("详细")</a>
                        @if (item.State == 2 && UnixTime.ToTimestamp() < item.EndTime)
                        {
                            <a href="javascript:dsLayerConfirm('@Url.Action("CancelFullGift", new { id = item.Id })','@T("您确定要取消吗?")',@item.Id)" class="dsui-btn-edit"><i class="iconfont"></i>取消</a>
                        }
                        <a href="javascript:dsLayerConfirm('@Url.Action("DelFullGift", new { ids = item.Id })','@T("您确定要删除吗?")',@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>

                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>
<script type="text/javascript">
    $(function() {
        $('#storeId').select2({
            placeholder: '@T("请选择店铺")',
            allowClear: true,
        });
    });
</script>
<script>
    // 批量/单条删除入口，items 为逗号分隔的 id 列表
    function submit_delete(items) {
        var _uri = '@Url.Action("DelFullGift")?ids=' + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
    }
</script>