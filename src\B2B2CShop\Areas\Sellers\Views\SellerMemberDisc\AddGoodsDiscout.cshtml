@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "MemberDisc";
    PekHtml.AppendPageCssClassParts("html-sellermemberdisc-page");
    PekHtml.AppendTitleParts(T("新增商品会员等级折扣").Text);
}
@await Html.PartialAsync("_Left")
<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li><a href="@Url.Action("MemberDiscout")">@T("设置会员等级折扣")</a></li>
                <li><a href="@Url.Action("GoodsDiscout")">@T("设置商品会员等级折扣")</a></li>
            </ul>
        </div>
        <div class="p20">

            <div class="dssc-form-default">
                <form id="add_form" method="post" action="@Url.Action("SaveGoodsDiscout")">
                    <input type="hidden" name="goodsId" id="goodsId" />
                    <dl>
                        <dt><i class="required">*</i>@T("设置商品会员等级折扣：")</dt>
                        <dd>
                            <div dstype="mgdiscount_goods_info" class="selected-group-goods " style="display:none;">
                                <div class="goods-thumb"><img id="mgdiscount_goods_image" src="" /></div>
                                <div class="goods-name">
                                    <a dstype="mgdiscount_goods_href" id="mgdiscount_goods_name" href=""
                                        target="_blank"></a>
                                </div>
                                <div class="goods-price">@T("商城价：")<span dstype="mgdiscount_goods_price"></span></div>
                            </div>
                            <a href="javascript:void(0);" id="btn_show_search_goods"
                                class="dssc-btn dssc-btn-acidblue">@T("选择商品")</a>
                            <input id="mgdiscount_goods_id" name="mgdiscount_goods_id" type="hidden" value="" />
                            <span></span>
                            <div id="div_search_goods" class="div-goods-select mt10" style="display: none;">
                                <table class="search-form">
                                    <tr>
                                        <th class="w150">
                                            <strong>@T("第一步：搜索店内商品")</strong>
                                        </th>
                                        <td class="w160">
                                            <input id="search_goods_name" type="text w150" class="text"
                                                name="goods_name" value="" />
                                        </td>
                                        <td class="w70 tc">
                                            <a href="javascript:void(0);" id="btn_search_goods" class="dssc-btn" /><i
                                                class="iconfont">&#xe718;</i>@T("搜索")</a>
                                        </td>
                                        <td class="w10"></td>
                                        <td>
                                            <p class="hint">@T("不输入名称直接搜索将显示店内所有普通商品，特殊商品不能参加。")</p>
                                        </td>
                                    </tr>
                                </table>
                                <div id="div_goods_search_result" class="search-result" style="width:739px;"></div>
                                <a id="btn_hide_search_goods" class="close" href="javascript:void(0);">X</a>
                            </div>
                            <p class="hint">@T("折扣商品解释")</br><span class="red">@T("该商品的所有规格SKU都将执行统一的会员等级折扣")</span></p>
                        </dd>
                    </dl>
                    @foreach (GradeModel item in Model.membergrades)
                    {
                        <dl>
                            <dt><i class="required">*</i>@item.level_name</dt>
                            <dd>
                                <input class="w60 text" name="[@item.level].discount" type="text" value="" maxlength="30" />
                                <span>@T("折")</span>
                                <p class="hint">@T("数值为0.1至10之间,设置为10表示不享受折扣")</p>
                            </dd>
                        </dl>
                    }
                    <div class="bottom">
                        <input id="submit_button" type="submit" class="submit" value='@T("提交")'>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(function () {
        // 默认隐藏搜索框
        $("#div_search_goods").hide();

        // 展示搜索框
        $("#btn_show_search_goods").on('click', function () {
            $("#div_search_goods").show();
        })

        // 隐藏搜索框
        $("#btn_hide_search_goods").on('click', function () {
            $("#div_search_goods").hide();
        })

        // 点击搜索按钮进行搜索并展示商品内容
        $("#btn_search_goods").on('click', function (e) {
            e.preventDefault();
            var goodsName = $("#search_goods_name").val();
            console.log('goodsName', goodsName)

            // 通过AJAX加载SearchGoodsList视图
            $.ajax({
                url: '@Url.Action("SearchGoodsList")',
                type: 'POST',
                data: { keyword: goodsName },
                success: function (result) {
                    // 直接将返回的HTML内容显示在搜索结果区域
                    $("#div_goods_search_result").html(result);

                    // 重新绑定商品选择按钮的点击事件
                    $(document).on('click', 'a[dstype="btn_add_xianshi_goods"]', function () {
                        var goodsId = $(this).data('goods-id');
                        var goodsName = $(this).closest('li').find('.goods-info dt').text();
                        var goodsPrice = $(this).closest('li').find('.goods-info dd').text().replace(/[^0-9.]/g, '');
                        var goodsImage = $(this).closest('li').find('.goods-thumb img').attr('src');

                        // 填充商品信息到表单
                        $('#goodsId').val(goodsId);
                        $('#mgdiscount_goods_name').text(goodsName);
                        $('[dstype="mgdiscount_goods_price"]').text(goodsPrice);
                        $('#mgdiscount_goods_image').attr('src', goodsImage);

                        // 显示已选择商品区域，隐藏搜索区域
                        $('[dstype="mgdiscount_goods_info"]').show();
                        $('#div_search_goods').hide();
                        // console.log('已选择商品：' + goodsName + '，ID：' + goodsId + '，价格：' + goodsPrice);
                    });
                },
                error: function () {
                    $("#div_goods_search_result").html('<div class="error-result">搜索出错，请重试</div>');
                }
            });
        })

        //页面输入内容验证
        $("#add_form").validate({
            errorPlacement: function (error, element) {
                var error_td = element.parent('dd').children('span');
                error_td.append(error);
            },
            submitHandler: function (form) {
                var $form = $("#add_form");
                var $btn = $("#submit_button");
                $btn.prop('disabled', true);
                $.ajax({
                    url: $form.attr('action'),
                    type: 'POST',
                    data: $form.serialize(),
                    success: function (res) {
                        if (res && res.success) {
                            var ok = res.msg || '@T("设置成功")';
                            layer.msg(ok, { icon: 1, time: 800 }, function () {
                                window.location.href = '@Url.Action("GoodsDiscout")';
                            });
                        } else {
                            var em = (res && res.msg) || '@T("设置失败")';
                            if (window.layer) { layer.msg(em, { icon: 2 }); } else { alert(em); }
                        }
                    },
                    error: function () {
                        if (window.layer) { layer.msg('@T("网络错误")', { icon: 2 }); } else { alert('@T("网络错误")'); }
                    },
                    complete: function () { $btn.prop('disabled', false); }
                });
            }
        });
    })
</script>