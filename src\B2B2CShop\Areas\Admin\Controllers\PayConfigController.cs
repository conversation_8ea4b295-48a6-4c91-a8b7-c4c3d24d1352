﻿using B2B2CShop.Common;
using Microsoft.AspNetCore.Mvc;
using Pek.DsMallUI;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using System.ComponentModel;

namespace B2B2CShop.Areas.Admin.Controllers
{
    /// <summary>支付参数设置</summary>
    [DisplayName("支付参数设置")]
    [Description("用于支付配置参数设置")]
    [AdminArea]
    [DHMenu(99, ParentMenuName = "Settings", ParentMenuDisplayName = "设置", ParentMenuUrl = "~/{area}/Config", ParentMenuOrder = 95, ParentVisible = true, CurrentMenuUrl = "~/{area}/PayConfig", CurrentMenuName = "PayConfigSetting", CurrentIcon = "&#xe6e0;", LastUpdate = "20250507")]
    public class PayConfigController : PekCubeAdminControllerX
    {
        public IActionResult Index()
        {
            return View();
        }

        [HttpPost]
        public IActionResult UpdateBase(String SandboxClientId, String SandboxClientSecret, String ProductionClientId, String ProductionClientSecret, String Environment, String AliPrivateKey, String AliPublicKey, String AliServerUrl, String AliAppId, String AliSellerId, String WxMerchantId, String WxMerchantV3Secret, String WxMerchantCertificateSerialNumber, String WxMerchantCertificatePrivateKey, String NotifyUrl)
        {

            var pay = Settings.Current;

            if(!SandboxClientId.IsNullOrWhiteSpace()) pay.SandboxClientId = SandboxClientId;
            if(!SandboxClientSecret.IsNullOrWhiteSpace()) pay.SandboxClientSecret = SandboxClientSecret;
            if(!ProductionClientId.IsNullOrWhiteSpace()) pay.ProductionClientId = ProductionClientId;
            if(!ProductionClientSecret.IsNullOrWhiteSpace()) pay.ProductionClientSecret = ProductionClientSecret;
            if(!Environment.IsNullOrWhiteSpace()) pay.Environment = Environment;
            if(!AliPrivateKey.IsNullOrWhiteSpace()) pay.AliPrivateKey = AliPrivateKey;
            if(!AliPublicKey.IsNullOrWhiteSpace()) pay.AliPublicKey = AliPublicKey;
            if(!AliServerUrl.IsNullOrWhiteSpace()) pay.AliServerUrl = AliServerUrl;
            if(!AliAppId.IsNullOrWhiteSpace()) pay.AliAppId = AliAppId;
            if(!AliSellerId.IsNullOrWhiteSpace()) pay.AliSellerId = AliSellerId;
            if(!WxMerchantId.IsNullOrWhiteSpace()) pay.WxMerchantId = WxMerchantId;
            if(!WxMerchantV3Secret.IsNullOrWhiteSpace()) pay.WxMerchantV3Secret = WxMerchantV3Secret;
            if(!WxMerchantCertificateSerialNumber.IsNullOrWhiteSpace()) pay.WxMerchantCertificateSerialNumber = WxMerchantCertificateSerialNumber;
            if(!WxMerchantCertificatePrivateKey.IsNullOrWhiteSpace()) pay.WxMerchantCertificatePrivateKey = WxMerchantCertificatePrivateKey;
            if(!NotifyUrl.IsNullOrWhiteSpace()) pay.NotifyUrl = NotifyUrl;

            pay.Save();

            return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }
    }
}
