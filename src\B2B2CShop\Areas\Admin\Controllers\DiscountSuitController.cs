﻿using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("优惠套装")]
    [Description("优惠套装管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/DiscountSuit", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/DiscountSuit", CurrentMenuName = "DiscountSuitList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class DiscountSuitController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("优惠套装列表")]
        public IActionResult Index(string name, long storeId, int status = -1, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };

            viewModel.list = Bundling.Search(name, storeId, status, pages);
            viewModel.storelist = Store.FindAll();
            viewModel.name = name;
            viewModel.storeId = storeId;
            viewModel.status = status;
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {
                { "name", name },
                { "storeId", storeId.SafeString() },
                { "status", status.SafeString() },
            });
            return View(viewModel);
        }


        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐管理")]
        public IActionResult DiscountSuitPackage(long storeId,int status, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "UpdateTime",
                Desc = true
            };
            viewModel.storelist = Store.FindAll();
            viewModel.storeId = storeId;
            viewModel.status = status;
            viewModel.list = BundlingQuota.Search(storeId,status, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("DiscountSuitPackage"), new Dictionary<string, string> {
                { "storeId", storeId.SafeString() },
                { "status", status.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        public IActionResult DiscountSuitSetting()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.price = OperationConfig.GetValueByCode("BundlingPackagePrice").ToDecimal();//优惠套装套餐价格
            viewModel.releasenum = OperationConfig.GetValueByCode("BundlingReleaseNum").ToInt();//优惠套装套餐发布数量
            viewModel.combinationnum = OperationConfig.GetValueByCode("BundlingCombinationNum").ToInt();//每个组合加入商品数量
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        [HttpPost]
        public IActionResult PackageSet(decimal price,int releasenum,int combinationnum)
        {
            // 参数校验
            if (price < 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("价格不能为负数") });
            }
            if (releasenum < 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("发布数量不能为负数") });
            }
            if (combinationnum < 0 || combinationnum > 5)
            {
                return Json(new DResult() { success = false, msg = GetResource("组合加入商品数量不合规") });
            }

            var flag = OperationConfig.UpdateValueByCode("BundlingPackagePrice", price.SafeString(), "优惠套装套餐价格");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到优惠套装套餐价格配置") });
            }

            flag = OperationConfig.UpdateValueByCode("BundlingReleaseNum", releasenum.SafeString(), "优惠套装套餐发布数量");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到优惠套装套餐发布数量配置") });
            }

            flag = OperationConfig.UpdateValueByCode("BundlingCombinationNum", combinationnum.SafeString(), "优惠套装每个组合加入商品数量");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到优惠套装每个组合加入商品数量配置") });
            }

            OperationConfig.Meta.Cache.Clear("", true);

            return Json(new DResult() { success = true, msg = GetResource("设置成功") });
        }


        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除活动")]
        public IActionResult DelBundling(long id)
        {
            var bundling = Bundling.FindById(id);
            if (bundling == null)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到该活动") });
            }
            using (var tran = Bundling.Meta.CreateTrans())
            {
                try
                {
                    var goodslist = BundlingGoods.FindAllByBundlingId(bundling.Id);
                    foreach (var item in goodslist)
                    {
                        Goods.GoodsLocked(item.GoodsId, 0);
                        item.Delete();
                    }
                    bundling.Delete();
                    tran.Commit();
                }
                catch (Exception ex)
                {
                    return Json(new DResult() { success = false, msg = GetResource("异常：")+ex.Message });
                }
            }

            return Json(new DResult() { success = true, msg = GetResource("删除成功") });
        }
    }
}
