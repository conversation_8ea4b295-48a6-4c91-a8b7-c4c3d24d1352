﻿using B2B2CShop.Entity;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using NewLife.Data;
using Newtonsoft.Json.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Timing;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;
using static SKIT.FlurlHttpClient.Wechat.TenpayV3.Models.CreatePayDevicePrinterPrintOrderRequest.Types.Table.Types;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("平台充值卡")]
    [Description("平台充值卡管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/RechargeCard", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/RechargeCard", CurrentMenuName = "RechargeCardList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class RechargeCardController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("充值卡列表")]
        public IActionResult Index(string sn, string batchflag, int state = -1, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.list = RechargeCard.Search(sn, batchflag, state, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> {
                { "sn", sn },
                { "batchflag", batchflag },
                { "state", state.ToString() }
            });
            viewModel.sn = sn;
            viewModel.batchflag = batchflag;
            viewModel.state = state;
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增充值卡")]
        public IActionResult AddCart()
        {
            return View();
        }

        [HttpPost]
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增充值卡提交")]
        public IActionResult AddCart(int type = 0, int total = 0, string prefix = "", IFormFile? _textfile = null, string manual = "", decimal denomination = 0, string batchflag = "")
        {
            // type: 0 自动生成, 1 文件导入, 2 手动输入
            int inserted = 0;
            int skipped = 0;
            var errors = new List<string>();

            // 方便的 helper 验证单个卡号
            bool IsValidSn(string sn)
            {
                if (string.IsNullOrWhiteSpace(sn)) return false;
                sn = sn.Trim();
                if (sn.Length > 50) return false;
                // 允许字母数字组合
                foreach (var c in sn)
                {
                    if (!(char.IsLetterOrDigit(c))) return false;
                }
                return true;
            }

            // 插入单个卡号（若已存在则返回 false）
            bool InsertSn(string sn)
            {
                sn = sn.Trim();
                if (!IsValidSn(sn)) return false;
                var exist = RechargeCard.FindBySn(sn);
                if (exist != null) return false;

                var rc = new RechargeCard();
                rc.Sn = sn;
                rc.Denomination = denomination;
                rc.BatchFlag = batchflag ?? "";
                rc.AdminName = ManageProvider.User?.Name ?? "";
                rc.TsCreated = UnixTime.ToTimestamp();
                rc.State = 0;
                try
                {
                    rc.Insert();
                    return true;
                }
                catch (Exception ex)
                {
                    // 如果插入报唯一索引错误或其他问题，视为跳过并记录错误
                    errors.Add(ex.Message);
                    return false;
                }
            }

            if (type == 0)
            {
                // 自动生成
                if (total <= 0) return Json(new DResult { success = false, msg = "总数必须大于0" });
                for (int i = 0; i < total; i++)
                {
                    // 生成 32 位随机（Guid N）并加前缀，整体长度受限 50
                    var rand = Guid.NewGuid().ToString("N");
                    var sn = (prefix ?? "") + rand;
                    if (sn.Length > 50) sn = sn.Substring(0, 50);
                    if (InsertSn(sn)) inserted++; else skipped++;
                }
            }
            else if (type == 1)
            {
                // 文件导入
                if (_textfile == null || _textfile.Length == 0) return Json(new DResult { success = false, msg = "没有上传文件" });
                try
                {
                    var rows = MiniExcel.Query(_textfile.OpenReadStream()).ToList();

                    foreach (var row in rows)
                    {
                        if (row == null) continue;
                        var rowDict = (IDictionary<string, object>)row;
                        var rowValues = rowDict.Values.ToList();
                        var value = rowValues.FirstOrDefault()?.ToString()?.Trim();
                        if (value.IsNullOrEmpty()) continue;
                        if (string.IsNullOrEmpty(value)) { skipped++; continue; }
                        if (InsertSn(value)) inserted++; else skipped++;
                    }

                }
                catch (Exception ex)
                {
                    return Json(new DResult { success = false, msg = ex.Message });
                }
            }
            else if (type == 2)
            {
                // 手动输入
                if (string.IsNullOrWhiteSpace(manual)) return Json(new DResult { success = false, msg = "请输入卡号" });
                var lines = manual.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var l in lines)
                {
                    var snRaw = l.Trim();
                    if (InsertSn(snRaw)) inserted++; else skipped++;
                }
            }
            else
            {
                return Json(new DResult { success = false, msg = "未知的发布方式" });
            }

            var msg = $"{inserted} {(inserted == 1 ? "条" : "条")} 插入，{skipped} 跳过";
            if (errors.Count > 0) msg += "; 错误：" + string.Join(";", errors.Take(5));
            if (inserted > 0)
                return Json(new DResult { success = true, msg = msg });
            else
                return Json(new DResult { success = false, msg = msg });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除充值卡")]
        public IActionResult Delete(string ids)
        {
            var count = RechargeCard.DeleteByIds(ids);
            if (count == 0)
            {
                return Json(new DResult() { success = false, msg = "删除失败" });
            }
            return Json(new DResult() { success = true, msg = $"成功删除{count}条记录" });
        }
    }
}
