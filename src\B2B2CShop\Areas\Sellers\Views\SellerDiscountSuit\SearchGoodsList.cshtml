﻿@using B2B2CShop.Dto
@using B2B2CShop.Entity
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<ul class="goods-list" style="width:760px;">
    @foreach (GoodsDto item in Model.list)
    {
        <li>
            <div class="goods-thumb"><img src="@item.GoodsImage" /></div>
            <dl class="goods-info">
                <dt>@item.Name</dt>
                <dd>@T("销售价")：@item.GoodsPrice</dd>
            </dl>
            <a dstype="btn_add_xianshi_goods" id="div_goods_search_result" data-goods-id="@item.Id" data-goods-skuid="@item.SkuId" href="javascript:void(0);" class="dssc-btn-mini dssc-btn-green" 
                onclick="toggleGoodsSelection(this, '@item.Id', '@item.SkuId', '@item.Name', '@item.GoodsPrice','@item.GoodsImage')">
                <i class="iconfont ">&#xe6db;</i><span class="btn-text">@T("添加到优惠套装")</span>
            </a>
        </li>
    }
</ul>
<ul class="pagination">
    @Html.Raw(Model.PageHtml)
</ul>
<script src="/public/modules/jq.js"></script>
<script src="/static/plugins/common.js"></script>
<script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
<script>
        // 切换商品选择状态的函数
        // 计算所有商品的总价
        function calculateTotalPrice() {
            var totalPrice = 0;
            $('tbody[dstype="bundling_data"] .bundling_goods_list').each(function() {
                var $row = $(this);
                // 优先使用行内的折后价输入框值（如果存在并且为数字），否则回退到显示的价格文本
                var inputVal = $row.find('input[name="discount_price[]"]').val();
                var price;
                if (typeof inputVal !== 'undefined' && inputVal !== null && inputVal !== '') {
                    // 过滤非数字字符，支持小数点和负号（若有）
                    var cleaned = String(inputVal).replace(/[^0-9.\-]/g, '');
                    price = parseFloat(cleaned);
                }
                if (isNaN(price)) {
                    var priceText = $row.find('td:nth-child(4)').text().trim();
                    price = parseFloat(String(priceText).replace(/[^0-9.\-]/g, ''));
                }
                if (!isNaN(price)) {
                    totalPrice += price;
                }
            });

            // 更新总价显示到 discountprice 输入框
            $('#discountprice').val(totalPrice.toFixed(2));
            // 同时更新原价显示
            $('span[dstype="storecost_price"]').html(totalPrice.toFixed(2));

            return totalPrice;
        }
        
        function toggleGoodsSelection(btnElement, goodsId, skuId, goodsName, goodsPrice,image) {
            var $btn = $(btnElement);
            var isSelected = $btn.attr('data-selected') === 'true';
            
            if (isSelected) {
                // 如果已选中，则从优惠套装中移除
                $('tbody[dstype="bundling_data"] .bundling_goods_list').each(function() {
                    // 使用更精确的选择器，获取商品名称所在的单元格
                    var rowGoodsName = $(this).find('td.tl').text().trim();
                    console.log("比较: ", rowGoodsName, goodsName);
                    console.log('rowGoodsName == goodsName => ', rowGoodsName == goodsName.trim())
                    if (rowGoodsName == goodsName.trim()) {
                        $(this).remove();
                        console.log("已移除商品:", goodsName);
                    }
                });
                
                // 更新按钮状态
                $btn.removeClass('dssc-btn-orange').addClass('dssc-btn-green');
                $btn.find('.iconfont').html('&#xe6db;'); // 更改图标
                $btn.find('.btn-text').text('@T("添加到优惠套装")');
                $btn.attr('data-selected', 'false');
                
                // 检查是否需要显示"无记录"提示
                const goodsCount = $('tbody[dstype="bundling_data"] .bundling_goods_list').length;
                if (goodsCount == 0) {
                    $('tbody[dstype="bundling_data"] .norecord').show();
                }
                
                // 计算并更新总价
                calculateTotalPrice();
            } else {
                // 如果未选中，则添加到优惠套装
                const goodsCount = $('tbody[dstype="bundling_data"] .bundling_goods_list').length;
                var comboLimit = @Model.comboLimit;
                if (goodsCount > comboLimit-1) {
                    layer.msg('最多只能添加'+comboLimit+'件商品');
                    return;
                } 
                
                // 动态生成表格行并填充商品信息
                var newRow = '<tr class="bundling_goods_list" data-skuid="'+skuId+'">' +
                    '<input type="hidden" name="bundling_skuid[]" value="'+skuId+'" />' +
                    '<td class="w70"><input type="checkbox" name="selected_goods" checked="checked" disabled="disabled" /></td>' +
                    '<td class="w50"><div class="pic-thumb">'+
                    '<img dstype="bundling_data_img" src="'+image+'" onload="javascript:ResizeImage(this,60,60)" width="60" height="60"></div></td>'+
                    '<td class="tl">' + goodsName + '</td>' +
                    '<td class="w90">' + goodsPrice + '</td>' +
                    '<td class="w90"><input type="text" class="text w90" name="discount_price[]" value="' + goodsPrice + '" /></td>' +
                    '<td class="w90"><a href="javascript:void(0);" class="delete"><i class="iconfont">&#xe754;</i><p>移除</p></a></td>' +
                    '</tr>';
                $('tbody[dstype="bundling_data"]').append(newRow);
                $('tbody[dstype="bundling_data"] .norecord').hide();
                
                // 更新按钮状态
                $btn.removeClass('dssc-btn-green').addClass('dssc-btn-orange');
                $btn.find('.iconfont').html('&#xe754;'); // 更改图标
                $btn.find('.btn-text').text('@T("从优惠套装中移除")');
                $btn.attr('data-selected', 'true');
                
                // 计算并更新总价
                calculateTotalPrice();
                // 将焦点移到新增行的折后价输入并绑定事件（如果需要）
                // 使用事件委托统一处理动态添加的输入框
            }
        }
        
        $(document).ready(function(){
            // 检查商品是否已添加到优惠套装中
            function checkSelectedGoods() {
                // 获取已添加到优惠套装的商品ID
                var selectedGoodsIds = [];
                $('tbody[dstype="bundling_data"] .bundling_goods_list').each(function() {
                    var goodsName = $(this).find('td.tl').text().trim();
                    // 遍历搜索结果中的所有商品
                    $('.goods-list li').each(function() {
                        var liGoodsName = $(this).find('.goods-info dt').text().trim();
                        var btnElement = $(this).find('a[dstype="btn_add_xianshi_goods"]');
                        
                        // 如果商品名称匹配，则更改按钮文本和图标
                        if(goodsName === liGoodsName) {
                            btnElement.removeClass('dssc-btn-green').addClass('dssc-btn-orange');
                            btnElement.find('.iconfont').html('&#xe754;'); // 更改图标
                            btnElement.find('.btn-text').text('@T("从优惠套装中移除")');
                            btnElement.attr('data-selected', 'true');
                        }
                    });
                });
                
                // 计算总价
                calculateTotalPrice();
            }
            
            // 页面加载完成后检查商品状态
            setTimeout(checkSelectedGoods, 100);
            
            // 将 checkSelectedGoods 函数暴露为全局函数，以便其他地方调用
            window.checkSelectedGoods = checkSelectedGoods;
            
            // 监听删除按钮点击事件，更新总价
            $(document).on('click', '.bundling_goods_list .delete', function() {
                // 延迟执行，确保DOM已更新
                setTimeout(calculateTotalPrice, 100);
            });

            // 监听折后价输入变化，使用事件委托以覆盖动态行
            $(document).on('input change', 'tbody[dstype="bundling_data"] input[name="discount_price[]"]', function() {
                // 直接重新计算并更新总价
                calculateTotalPrice();
            });
            
            // 监听价格输入框变化，但这里我们不需要，因为我们只关心原始价格总和
            
            var defaultAlbumId = $('#jumpMenu option:selected').val();
            if(defaultAlbumId != '0') {
                // loadAlbumPics(defaultAlbumId);
            }
    function updatePagination(currentPage, totalPages, aId) {
        var $pagination = $('.pagination');
        var $pageLinks = $pagination.find('li:not(:first):not(:last)');
        // 清空现有页码链接
        $pageLinks.empty();
        console.log("添加首页");
        // 添加首页
        $pageLinks.first().html(`<a href="javascript:void(0);" data-page="1">首页</a>`);

        // 添加上一页
        if(currentPage > 1) {
            $pageLinks.eq(1).html(`<a href="javascript:void(0);" data-page="${currentPage-1}">«</a>`);
        } else {
            $pageLinks.eq(1).html(`<span>«</span>`);
        }

        // 添加页码
        for(let i = 1; i <= totalPages && i <= 5; i++) {
            let isActive = i === currentPage;
            let pageHtml = isActive ?
                `<span>${i}</span>` :
                `<a href="javascript:void(0);" data-page="${i}">${i}</a>`;
            $pageLinks.eq(i+1).html(pageHtml);
        }

        // 添加下一页
        if(currentPage < totalPages) {
            $pageLinks.eq(-2).html(`<a href="javascript:void(0);" data-page="${currentPage+1}">»</a>`);
        } else {
            $pageLinks.eq(-2).html(`<span>»</span>`);
        }

        // 添加尾页
        $pageLinks.last().html(`<a href="javascript:void(0);" data-page="${totalPages}">尾页</a>`);

        // 更新当前页输入框
        $('input[name="page"]').val(currentPage);
    }
            // 分类切换事件
            $('#jumpMenu').change(function(){
                var aId = $(this).val();
                console.log("aId:"+aId);
                if(aId != '0') {
                    // loadAlbumPics(aId);
                }
            });

            // 分页点击事件
            $(document).on('click', 'ul.pagination>li>a', function(e){
                e.preventDefault();
                var aId = $('#jumpMenu').val();
                if(aId != '0') {
                    var page = $(this).data('page') || 1;
                    // loadAlbumPics(aId, page);
                }
            });
            // 跳转页面事件
            $('#pagination_gourl').click(function(e){
                e.preventDefault();
                var aId = $('#jumpMenu').val();
                if(aId != '0') {
                    var page = $('input[name="page"]').val();
                    // loadAlbumPics(aId, page);
                }
            });
        });
        $(document).ready(function(){
        $('ul.pagination>li>a').ajaxContent({
            event:'click', //mouseover
            loaderType:'img',
            loadingMsg:'/static/home/<USER>/loading.gif',
            target:'#div_goods_search_result',
            callback: function() {
                // 分页加载完成后，重新检查商品状态
                setTimeout(checkSelectedGoods, 100);
            }
        });
        $('#jumpMenu').change(function(){
            $('#select_submit').attr('href',$('#select_submit').attr('href')+"&id="+$('#jumpMenu').val());
            $('#select_submit').ajaxContent({
                event:'click', //mouseover
                loaderType:'img',
                loadingMsg:'/static/home/<USER>/loading.gif',
                target:'#div_goods_search_result'
            });
            $('#select_submit').click();
        });
    });
</script>