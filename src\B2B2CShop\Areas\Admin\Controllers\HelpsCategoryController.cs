﻿using B2B2CShop.Entity;

using DH.Core.Domain.Localization;
using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife.Log;

using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Models;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>帮助分类</summary>
[DisplayName("帮助分类")]
[Description("用于帮助分类的管理")]
[AdminArea]
[DHMenu(49, ParentMenuName = "Sites", CurrentMenuUrl = "~/{area}/HelpsCategory", CurrentMenuName = "HelpsCategoryList", CurrentIcon = "&#xe652;", LastUpdate = "20241205")]
public class HelpsCategoryController : PekCubeAdminControllerX {
    /// <summary>
    /// 帮助分类列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("帮助分类列表")]
    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();
        var list = HelpsCategory.FindAllByLevel(0);
        foreach (var item in list)
        {
            var Model = HelpsCategory.FindAllByParentId(item.Id);
            if (Model.Count != 0)
            {
                item.Subset = true;
            }
        }
        viewModel.list = list;
        return View(viewModel);
    }

    /// <summary>
    /// 获取帮助分类表下级数据
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取帮助分类表下级数据")]
    public IActionResult GetSubordinateData(String Id)
    {
        var zList = new List<Hierarchy>();
        if (Id.IsNullOrWhiteSpace())
        {
            return Json(new { });
        }
        var list = HelpsCategory.FindAllByParentId(Id.ToInt());
        foreach (var item in list)
        {
            var model = new Hierarchy
            {
                gc_name = item.Name.SafeString().Trim(),
                gc_id = item.Id,
                gc_parent_id = item.ParentId
            };
            var exc = HelpsCategory.FindAllByParentId(item.Id);
            if (exc.Count > 0)
            {
                model.have_child = 1;
            }
            model.gc_show = 1;
            model.gc_sort = item.DisplayOrder;
            model.deep = item.Level;
            zList.Add(model);
        }
        return Json(new { zList });
    }

    /// <summary>
    /// 打开修改页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("打开修改页面")]
    public IActionResult EditHelpsCategory(String Id)
    {
        dynamic viewModel = new ExpandoObject();
        var Model = HelpsCategory.FindById(Id.ToInt());
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }
        var List = new List<HelpsCategory>();
        var live1 = HelpsCategory.FindAllByLevel(0);
        GetCategoryList(live1, List);
        viewModel.Plist = List;
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
        viewModel.Model = Model;
        return View(viewModel);
    }

    /// <summary>
    /// 获取分类集合
    /// </summary>
    /// <param name="levelList"></param>
    /// <param name="list"></param>
    private static void GetCategoryList(IList<HelpsCategory> levelList, IList<HelpsCategory> list)
    {
        if (levelList.Count > 0)
        {
            foreach (var item in levelList)
            {
                list.Add(item);

                var level = HelpsCategory.FindAllByParentId(item.Id);
                GetCategoryList(level, list);
            }
        }
    }

    /// <summary>
    /// 获取是否存在该名称
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取是否存在该名称")]
    public IActionResult GetByName(String gc_name, Int32 gc_id)
    {
        var Model = HelpsCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null && Model.Id != gc_id)
        {
            return Json(false);
        }
        else
        {
            return Json(true);
        }
    }

    /// <summary>
    /// 获取是否存在该名称
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_id"></param>
    /// <returns></returns>
    public IActionResult GetByNames(String gc_name)
    {
        var Model = HelpsCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null)
        {
            return Json(false);
        }
        else
        {
            return Json(true);
        }
    }

    /// <summary>
    /// 修改帮助分类
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_parent_id"></param>
    /// <param name="gc_id"></param>
    /// <param name="DisplayOrder"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改分类接口")]
    public IActionResult EditHelpsCategory(String gc_name, Int32 gc_parent_id, Int32 gc_id, Int32 DisplayOrder)
    {
        if (gc_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("帮助名称不能为空") });
        }
        var Model = HelpsCategory.FindByName(gc_name.SafeString().Trim());
        if (Model != null && Model.Id != gc_id)
        {
            return Prompt(new PromptModel { Message = GetResource("该下载分类名称已存在") });
        }
        if (gc_parent_id == gc_id)
        {
            return Prompt(new PromptModel { Message = GetResource("父级栏目不能为本身") });
        }
        var models = HelpsCategory.FindById(gc_id);
        if (models == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除") });
        }
        models.Name = gc_name.SafeString().Trim();
        models.ParentId = gc_parent_id.ToInt();
        models.DisplayOrder = DisplayOrder.ToDGShort();
        var pmodel = HelpsCategory.FindById(gc_parent_id);
        if (pmodel == null)
        {
            models.ParentIdList = gc_id.ToString();
            models.Level = 0;
        }
        else
        {
            models.ParentIdList = pmodel.ParentIdList + "," + gc_id.ToString();
            models.Level = pmodel.Level + 1;
        }
        if (models.Level > 2)
        {
            return Prompt(new PromptModel { Message = GetResource("分类不能超过三层") });
        }
        models.Update();
        HelpsCategory.Meta.Cache.Clear("");//清楚缓存
        //循环修改子集的父级Id集合
        var zList = HelpsCategory.FindAllByParentId(gc_id);
        foreach (var item in zList)
        {
            item.ParentIdList = models.ParentIdList + "," + item.Id;
            var slist = HelpsCategory.FindAllByParentId(item.Id);
            foreach (var row in slist)
            {
                item.ParentIdList = item.ParentIdList + "," + row.Id;
            }
            slist.Save();
        }
        zList.Save();

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言               
            foreach (var item in Languagelist)
            {
                var articleCategoryLan = HelpsCategoryLan.FindByHIdAndLId(models.Id, item.Id);
                articleCategoryLan = articleCategoryLan ?? new HelpsCategoryLan();

                articleCategoryLan.Name = GetRequest($"[{item.Id}].gc_name").SafeString().Trim();
                articleCategoryLan.HId = models.Id;
                articleCategoryLan.LId = item.Id;
                articleCategoryLan.Save();
            }
            HelpsCategoryLan.Meta.Cache.Clear("");
        }

        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 新增页面打开
    /// </summary>
    /// <param name="parent_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("帮助分类分类页面打开")]
    public IActionResult AddHelpsCategory(Int32 parent_id = 0)
    {
        dynamic viewModel = new ExpandoObject();
        var List = new List<HelpsCategory>();
        viewModel.ParentId = parent_id;
        var live1 = HelpsCategory.FindAllByLevel(0);//一级数据
        GetCategoryList(live1, List);

        viewModel.Plist = List;
        Int32 DisplayOrder;
        if (parent_id != 0)
        {
            DisplayOrder = HelpsCategory.FindMax("DisplayOrder", HelpsCategory._.ParentId == parent_id).ToInt();
        }
        else
        {
            DisplayOrder = HelpsCategory.FindMax("DisplayOrder").ToInt();
        }

        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
        ViewBag.DisplayOrder = DisplayOrder + 1;
        return View(viewModel);
    }

    /// <summary>
    /// 帮助分类新增
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_parent_id"></param>
    /// <param name="DisplayOrder"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("帮助分类新增")]
    public IActionResult AddHelpsCategory(String gc_name, Int32 gc_parent_id, Int32 DisplayOrder)
    {
        XTrace.WriteLine("获取到的gc_parent_id==" + gc_parent_id);
        if (gc_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("帮助名称不能为空") });
        }

        var model = HelpsCategory.FindByName(gc_name.SafeString().Trim());
        if (model != null)
        {
            return Prompt(new PromptModel { Message = GetResource("帮助分类名称已存在") });
        }
        var Pmodel = HelpsCategory.FindById(gc_parent_id);
        if (Pmodel != null)
        {
            if (Pmodel.Level >= 2)
            {
                return Prompt(new PromptModel { Message = GetResource("帮助分类最多存在三级,创建失败！") });
            }
        }

        var Model = new HelpsCategory
        {
            Name = gc_name.SafeString().Trim(),
            ParentId = gc_parent_id,
            DisplayOrder = DisplayOrder.ToDGShort()
        };
        Model.Insert();

        if (Pmodel == null)
        {
            Model.Level = 0;
            Model.ParentIdList = Model.Id.ToString();
        }
        else
        {
            Model.Level = Pmodel.Level + 1;
            Model.ParentIdList = Pmodel.ParentIdList + "," + Model.Id;
        }
        Model.Update();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言               
            foreach (var item in Languagelist)
            {
                var articleCategoryLan = new HelpsCategoryLan();

                articleCategoryLan.Name = GetRequest($"[{item.Id}].gc_name").SafeString().Trim();
                articleCategoryLan.HId = Model.Id;
                articleCategoryLan.LId = item.Id;
                articleCategoryLan.Insert();
            }
            HelpsCategoryLan.Meta.Cache.Clear("");
        }

        Helps.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("新增成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 删除分类
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除")]
    public IActionResult Delete(String Ids)
    {
        var res = new DResult();

        var list = HelpsCategory.FindByIds(Ids);
        var dellist = new List<HelpsCategory>();

        res = DeleteHelpsCategory(res, dellist, list);

        if (!res.msg.IsNullOrWhiteSpace())
        {
            return Json(res);
        }

        foreach (var item in dellist)
        {
            HelpsCategoryLan.Delete(HelpsCategoryLan._.HId == item.Id);
        }
        if (dellist.Delete(true) > 0)
        {
            HelpsCategory.Meta.Cache.Clear("", true);
            HelpsCategoryLan.Meta.Cache.Clear("");
        }

        res.msg = GetResource("删除成功");
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 循环删除多级数据
    /// </summary>
    /// <param name="res"></param>
    /// <param name="dellist"></param>
    /// <param name="list"></param>
    /// <returns></returns>
    private DResult DeleteHelpsCategory(DResult res, IList<HelpsCategory> dellist, IList<HelpsCategory> list)
    {
        if (list.Count > 0)
        {
            foreach (var item in list)
            {
                var listKnowledge = Helps.FindAllByHId(item.Id);
                if (listKnowledge.Count > 0)
                {
                    res.msg = String.Format(GetResource("选中的{0}有关联帮助数据 不允许被删除"), item.Name);
                    return res;
                }
                else
                {
                    dellist.Add(item);
                    var childlist = HelpsCategory.FindAllByParentId(item.Id);
                    res = DeleteHelpsCategory(res, dellist, childlist);
                }
            }
        }
        return res;
    }

    /// <summary>
    /// 修改列表字段值
    /// </summary>
    /// <param name="value">修改名称</param>
    /// <param name="Id">分类编号</param>
    /// <param name="column">字段名</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("修改列表字段值")]
    public IActionResult ChangeName(String value, Int32 Id, String column)
    {
        if (value.IsNullOrWhiteSpace()) return Json(false);

        var Models = HelpsCategory.FindById(Id);

        if (Models == null) return Json(false);

        if (column == "gc_name")
        {
            var Model = HelpsCategory.FindByName(value);
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }

            Models.Name = value;
        }
        else if (column == "gc_sort")
        {
            Models.DisplayOrder = value.ToDGShort();
        }
        else
        {
            return Json(false);
        }

        Models.Update();

        return Json(true);
    }
}
