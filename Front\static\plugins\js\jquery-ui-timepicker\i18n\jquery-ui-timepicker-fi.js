/* Finnish translation for the jQuery Timepicker Addon */
/* Written by <PERSON><PERSON> (http://github.com/paa<PERSON><PERSON><PERSON>) */
(function($) {
	$.timepicker.regional['fi'] = {
		timeOnlyTitle: '<PERSON><PERSON>e aika',
		timeText: '<PERSON><PERSON>',
		hourText: '<PERSON><PERSON>',
		minuteText: 'Minuutti',
		secondText: '<PERSON><PERSON><PERSON>',
		millisecText: 'Millisekunnin',
		microsecText: 'Mikrosekuntia',
		timezoneText: 'Aikavyöhyke',
		currentText: 'Nyt',
		closeText: 'Sulje',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['ap.', 'AM', 'A'],
		pmNames: ['ip.', 'PM', 'P'],
		isRTL: false
	};
	$.timepicker.setDefaults($.timepicker.regional['fi']);
})(jQuery);
