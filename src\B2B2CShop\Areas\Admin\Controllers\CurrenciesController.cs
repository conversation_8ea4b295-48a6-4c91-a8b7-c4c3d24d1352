﻿using B2B2CShop.Entity;
using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Webs;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;

using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>
/// 货币管理
/// </summary>
[DisplayName("货币管理")]
[Description("货币管理")]
[AdminArea]
[DHMenu(78, ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/Currencies", CurrentMenuName = "CurrenciesList", CurrentIcon = "&#xe631;", LastUpdate = "20250222")]
public class CurrenciesController : PekCubeAdminControllerX
{
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("货币管理列表")]
    public IActionResult Index(int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };
        viewModel.list = Currencies.FindAll(null, pages).Select(e => new Currencies
        {
            Id = e.Id,
            Name = e.Name,
            Code = e.Code,
            SymbolLeft = e.SymbolLeft,
            SymbolRight = e.SymbolRight,
            DecimalPlace = e.DecimalPlace,
            DecimalValue = e.DecimalValue,
            Status = e.Status,
            ExchangeRate = e.ExchangeRate,
            ImgPath = e.ImgPath?.IsNullOrWhiteSpace() == false ? UrlHelper.Combine(DHWeb.GetSiteUrl(), e.ImgPath) : ""
        }).ToList();
        viewModel.page = page;
        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"),new Dictionary<string, string>());
        return View(viewModel);
    }

    /// <summary>
    /// 新增货币
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增货币")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddCurrencies()
    {
        return View();
    }

    /// <summary>
    /// 新增货币
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增货币")]
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    public IActionResult AddCurrencies(string name, string code, string status, string symbolLeft, string symbolRight, short decimalPlace, decimal exchangeRate, string decimalValue, IFormFile default_user_portrait)
    {
        var currencies = new Currencies
        {
            Name = name,
            Code = code,
            SymbolLeft = symbolLeft,
            SymbolRight = symbolRight,
            DecimalPlace = decimalPlace,
            ExchangeRate = exchangeRate,
            //DecimalValue = decimalValue == "on",
            //Status = status == "on",
            DecimalValue = false,
            Status = true,
        };

        currencies.Insert();

        if (default_user_portrait != null)
        {
            var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
            if (!bytes.IsImageFile())
            {
                return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
            }

            var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(default_user_portrait.FileName)}";
            var filepath = $"Currencies/{filename}";
            var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

            filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

            saveFileName.EnsureDirectory();
            default_user_portrait.SaveAs(saveFileName);
            currencies.ImgPath = filepath;
            currencies.Update();
        }

        return MessageTip(GetResource("创建成功"));
    }

    /// <summary>
    /// 修改状态
    /// </summary>
    /// <returns></returns>
    [DisplayName("修改状态")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult ModifyState(Int32 Id, Boolean Status)
    {
        var result = new DResult();

        var model = Currencies.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("状态调整出错");
            return Json(result);
        }

        model.Status = Status;
        model.Update();

        result.success = true;
        result.msg = GetResource("状态调整成功");

        return Json(result);
    }

    /// <summary>
    /// 修改默认货币
    /// </summary>
    /// <returns></returns>
    [DisplayName("修改默认货币")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult ModifyDecimalValue(Int32 Id, Boolean DecimalValue)
    {
        var result = new DResult();

        var model = Currencies.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("默认货币调整出错");
            return Json(result);
        }

        if (DecimalValue)
        {
            Currencies.UpdateDecimalValue();
        }

        model.DecimalValue = DecimalValue;
        model.Update();

        result.success = true;
        result.msg = GetResource("默认货币调整成功");

        return Json(result);
    }

    /// <summary>
    /// 删除货币
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除货币")]
    public IActionResult Delete(String Ids)
    {
        var res = new DResult();

        Currencies.Delete(Currencies._.Id.In(Ids.Trim(',')));
        Currencies.Meta.Cache.Clear("");

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 编辑货币
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [DisplayName("编辑货币")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int32 Id)
    {
        var model = Currencies.FindById(Id);

        if (model == null)
        {
            return Content(GetResource("货币不存在"));
        }
        ViewBag.Images = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), model.ImgPath?.IsNullOrWhiteSpace() == false ? model.ImgPath : "");
        return View(model);
    }

    /// <summary>
    /// 编辑货币
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="Name"></param>
    /// <param name="Code"></param>
    /// <param name="SymbolLeft"></param>
    /// <param name="SymbolRight"></param>
    /// <param name="DecimalPlace"></param>
    /// <param name="ExchangeRate"></param>
    /// <returns></returns>
    [DisplayName("编辑货币")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult Edit(Int32 Id, String Name, String Code, String SymbolLeft, String SymbolRight, short DecimalPlace, decimal ExchangeRate, IFormFile default_user_portrait)
    {
        var model = Currencies.FindById(Id);
        if (model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("货币不存在") });
        }

        model.Name = Name;
        model.Code = Code;
        model.SymbolLeft = SymbolLeft;
        model.SymbolRight = SymbolRight;
        model.DecimalPlace = DecimalPlace;
        model.ExchangeRate = ExchangeRate;

        if (default_user_portrait != null)
        {
            var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
            if (!bytes.IsImageFile())
            {
                return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
            }

            var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(default_user_portrait.FileName)}";
            var filepath = $"Currencies/{filename}";
            var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

            filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

            saveFileName.EnsureDirectory();
            default_user_portrait.SaveAs(saveFileName);
            model.ImgPath = filepath;
        }

        model.Update();

        //return Prompt(new PromptModel { Message = GetResource("编辑成功"), IsOk = true, BackUrl = Url.Action("Index") });
        return MessageTip(GetResource("保存成功！"));
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg(Int32 Id, IFormFile fileupload)
    {
        var bytes = fileupload.OpenReadStream().ReadBytes(fileupload.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }
        var fileModel = new UploadInfo();
        fileModel.FileSize = fileupload.Length;
        fileModel.FileType = 1;
        fileModel.ItemId = Id;

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(fileupload.FileName)}";
        var filepath = $"SingleArticle/{filename}";
        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

        filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

        saveFileName.EnsureDirectory();
        fileupload.SaveAs(saveFileName);

        fileModel.FileName = filename;
        fileModel.FileUrl = filepath.Replace("\\", "/");
        fileModel.Insert();

        return Json(new { file_id = fileModel.Id, file_name = filename, file_path = Pek.Helpers.DHWeb.GetSiteUrl() + "/" + filepath.Replace("\\", "/") });
    }

    /// <summary>
    /// 图片删除
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片删除")]
    public IActionResult DeleteImg(Int32 Id)
    {
        var model = UploadInfo.FindById(Id);

        if (model != null)
        {
            DHSetting.Current.UploadPath.GetFullPath().CombinePath(model.FileUrl??"").AsFile().Delete();
            model.Delete();
        }

        return Ok("true");
    }
}
