﻿@{

    var content = Model.Model.Content as String;
    content = content.SafeString().ToUnicodeString();

    var localizationSettings = LocalizationSettings.Current;
    IList<UploadInfo> uploadInfos = ViewBag.FileList;
}
<style asp-location="true">
    .type-file-preview {
        z-index: 99999
    }
</style>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("帮助管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("EditHelps",new { Id=Model.Model.Id})" class="current"><span>@T("修改")</span></a></li>
            </ul>
        </div>
    </div>
    <form id="goods_class_form" enctype="multipart/form-data" method="post">

        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">标准</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li data="@item.Id">@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td class="required w120">@T("标题")</td>
                                <td class="vatop rowform"><input type="text" name="Helps_title" id="Helps_title" value="@Model.Model.Name" class="w200" /></td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td class="required">@T("文章封面:") </td>
                                <td class="vatop rowform">
                                    <span class="type-file-show">
                                        @if (Model.Model.Pic != null)
                                        {
                                            <img class="show_image" src="/static/admin/images/preview.png">
                                            <div class="type-file-preview"><img src="@ViewBag.Images"></div>
                                        }
                                    </span>
                                    <span class="type-file-box">
                                        <input type='text' name='textfield' id='textfields' class='type-file-text' value="" />
                                        <input type='button' id="fileuploads" name="fileuploads" value='@T("上传")' class='type-file-button' />
                                        <input name="default_user_portrait" type="file" class="type-file-file" id="default_user_portrait" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                                    </span>
                                </td>
                                <td class="vatop tips">@T("图片限于png,gif,jpeg,jpg格式")</td>
                            </tr>

                            <tr class="noborder">
                                <td class="required">@T("文章内容")</td>
                                <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.config.js"></script>
                                <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.all.min.js"></script>
                                <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/lang/zh-cn/zh-cn.js"></script>
                                <script type="text/javascript">
                                    var ue = UE.getEditor('Helps_content');
                                    ue.ready(function () {
                                        this.setContent("@Html.Raw(content)");
                                    })
                                </script>
                                <td class="vatop rowform" colspan="2"><textarea name="Helps_content" id="Helps_content" value="" style="width:100%;"></textarea></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {

                        var ModelLan = HelpsLan.FindByHIdAndLId((int)Model.Model.Id, item.Id);

                        var contentlan = ModelLan?.Content;
                        contentlan = contentlan.SafeString().ToUnicodeString();
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td class="required w120">@T("标题")</td>
                                        <td class="vatop rowform"><input type="text" name="[@item.Id].Helps_title" id="Helps_title" value="@ModelLan?.Name" class="w200" /></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="required">@T("文章封面:") </td>
                                        <td class="vatop rowform">
                                            <span class="type-file-show">
                                                @if (ModelLan?.Pic != null)
                                                {
                                                    <img class="show_image" src="/static/admin/images/preview.png">
                                                    <div class="type-file-preview"><img src="@ModelLan.Pic"></div>
                                                }
                                            </span>
                                            <span class="type-file-box">
                                                <input type='text' name='textfield@(item.Id)' id='textfields@(item.Id)' class='type-file-text' value="" />
                                                <input type='button' id="fileuploads@(item.Id)" name="fileuploads@(item.Id)" value='@T("上传")' class='type-file-button' />
                                                <input name="[@item.Id].default_user_portrait" type="file" class="type-file-file" id="[@item.Id].default_user_portrait" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                                            </span>
                                        </td>
                                        <td class="vatop tips">@T("图片限于png,gif,jpeg,jpg格式")</td>
                                    </tr>

                                    <tr class="noborder">
                                        <td class="required">@T("文章内容")</td>
                                        <script type="text/javascript">
                                            var ue@(item.Id) = UE.getEditor('<EMAIL>');
                                            ue@(item.Id).ready(function () {
                                                this.setContent('@Html.Raw(contentlan)');
                                            })
                                        </script>
                                        <td class="vatop rowform" colspan="2"><textarea name="Helps_content_@(item.Id)" id="Helps_content_@(item.Id)" value="" style="width:100%;"></textarea></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
        </div>



        <table class="ds-default-table">
            <tbody>
                @*<tr class="noborder">
                        <td class="required w120">@T("标题")</td>
                        <td class="vatop rowform"><input type="text" name="article_title" id="article_title" value="@Model.Model.Name" class="w200" /></td>
                        <td class="vatop tips"></td>
                    </tr>*@
                @* <tr>
                    <td>@T("所属产品型号")</td>
                    <td colspan="2">
                        <div id="demo1" style="width:50%"></div>
                    </td>

                </tr> *@
                <tr class="noborder">
                    <td class="required w120">@T("所属分类")</td>
                    <td class="vatop rowform">
                        <select name="gc_class_id" id="gc_class_id">
                            <option value="">@T("请选择")...</option>
                            @foreach (var item in Model.Plist)
                            {
                                if (Model.Model.HId == item.Id)
                                {
                                    if (item.Level == 0)
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp; @item.Name</option>
                                    }
                                    else if (item.Level == 1)
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }

                                }
                                else
                                {
                                    @*<option value="@item.Id">@item.Name</option>*@
                                    if (item.Level == 0)
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp; @item.Name</option>
                                    }
                                    else if (item.Level == 1)
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                }
                            }
                        </select>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required">@T("链接")</td>
                    <td class="vatop rowform"><input type="text" name="Helps_url" id="Helps_url" value="@Model.Model.Url" class="w200" /></td>
                    <td class="vatop tips"></td>
                </tr>
                @*<tr class="noborder">
                        <td class="required">@T("文章封面:") </td>
                        <td class="vatop rowform">
                            <span class="type-file-show">
                                @if (Model.Model.Pic != null)
                                {
                                    <img class="show_image" src="/static/admin/images/preview.png">
                                    <div class="type-file-preview"><img src="@ViewBag.Images"></div>
                                }
                            </span>
                            <span class="type-file-box">
                                <input type='text' name='textfield' id='textfield4' class='type-file-text' value="" />
                                <input type='button' id="fileupload1" name="fileupload1" value='@T("上传")' class='type-file-button' />
                                <input name="default_user_portrait" type="file" class="type-file-file" id="default_user_portrait" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                            </span>
                        </td>
                        <td class="vatop tips">@T("图片限于png,gif,jpeg,jpg格式")</td>
                    </tr>*@

                <tr class="noborder">
                    <td class="required">@T("文章是否显示")</td>
                    <td class="vatop rowform onoff">
                        @if (Model.Model.Show)
                        {
                            <label for="Helps_show1" class="cb-enable selected"><span>@T("是")</span></label>
                            <label for="Helps_show2" class="cb-disable "><span>@T("否")</span></label>
                            <input id="Helps_show1" name="Helps_show" checked="checked" value="1" type="radio">
                            <input id="Helps_show2" name="Helps_show" value="0" type="radio">
                        }
                        else
                        {
                            <label for="Helps_show1" class="cb-enable "><span>@T("是")</span></label>
                            <label for="Helps_show2" class="cb-disable selected"><span>@T("否")</span></label>
                            <input id="Helps_show1" name="Helps_show" value="1" type="radio">
                            <input id="Helps_show2" name="Helps_show" checked="checked" value="0" type="radio">
                        }
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                @*<tr class="noborder">
                        <td class="required">@T("简介")</td>
                        <td><textarea name="summary" style="min-height:150px;" class="textarea h60 w400">@Model.Model.Summary</textarea></td>
                        <td vatop tips></td>
                    </tr>*@
                <tr class="noborder">
                    <td class="required">@T("排序")</td>
                    <td class="vatop rowform"><input type="text" name="Helps_sort" id="Helps_sort" value="@Model.Model.Sort" class="w200" /></td>
                    <td class="vatop tips"></td>
                </tr>
                @*<tr class="noborder">
                        <td class="required">@T("文章内容")</td>
                        <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.config.js"></script>
                        <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.all.min.js"></script>
                        <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/lang/zh-cn/zh-cn.js"></script>
                        <script type="text/javascript" asp-location="Footer">
                            var ue = UE.getEditor('article_content', {
                                toolbars: [[
                                    'fullscreen', 'source', '|', 'undo', 'redo', '|',
                                    'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
                                    'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
                                    'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
                                    'directionalityltr', 'directionalityrtl', 'indent', '|',
                                    'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|',
                                    'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
                                    'emotion', 'map', 'gmap', 'insertcode', 'template', '|',
                                    'horizontal', 'date', 'time', 'spechars', '|',
                                    'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts', '|',
                                    'searchreplace', 'help', 'drafts', 'charts'
                                ]],
                            });

                                ue.ready(function () {
                                    this.setContent('@Html.Raw(content)');
                                })

                        </script>
                        <td class="vatop rowform" colspan="2"><textarea name="article_content" id="article_content" value="" style="width:100%;"></textarea></td>
                    </tr>*@
                <tr style="background: rgb(255, 255, 255);">
                    <td class="required" aria-required="true">图片上传:</td>
                    <td id="divComUploadContainer"><input type="file" multiple="multiple" id="fileupload" name="fileupload"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr>
                    <td class="required">@T("已传图片:")</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2">
                        <div class="tdare">
                            <table cellspacing="0" class="dataTable">
                                <tbody id="thumbnails">
                                    @foreach (var item in uploadInfos)
                                    {
                                        <tr id="@item.Id" class="tatr2">
                                            <input type="hidden" name="file_id[]" value="@item.Id" />
                                            <td><img width="40px" height="40px" src="@(Pek.Helpers.DHWeb.GetSiteUrl())@(item.FileUrl)" /></td>
                                            <td>@item.FileName</td>
                                            <td><a href="javascript:insert_editor('@(Pek.Helpers.DHWeb.GetSiteUrl())@(item.FileUrl)');">插入编辑器</a> | <a href="javascript:del_file_upload('@item.Id');">删除</a></td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="required">@T("已传文件:")</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2">
                        <div class="tdare">
                            <table cellspacing="0" class="dataTable">
                                <tbody id="thumbnails2">
                                    @foreach (var item in uploadInfos)
                                    {
                                        <tr id="@item.Id" class="tatr2">
                                            <input type="hidden" name="file_id[]" value="@item.Id" />
                                            <td><a href="@(Pek.Helpers.DHWeb.GetSiteUrl()+item.FileUrl)" target="_blank">@item.FileName</a> </td>
                                            <td><a href="javascript:insert_editor2('@(Pek.Helpers.DHWeb.GetSiteUrl())@(item.FileUrl)');">插入编辑器</a> | <a href="javascript:del_file_upload('@item.Id');">删除</a></td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script src="/static/plugins/js/jquery-file-upload/jquery.fileupload.js"></script>
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<script src="~/static/admin/js/xm-select.js"></script>
<script type="text/javascript" asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,
            element = layui.element;
    })

    var lid = "";

    $(function () {
        //$("#default_user_portrait").change(function () {
        //    $("#textfield4").val($("#default_user_portrait").val());
        //});


        $(".layui-tab-title").on("click", "li", function () {
            //debugger;
            console.log($(this).attr("data"));
            lid = $(this).attr("data");
        })

        $(".type-file-file").change(function () {
            $(this).parents(".layui-tab-item").find(".type-file-text").val($(this).parents(".layui-tab-item").find(".type-file-file").val())
        });

        // 图片上传
        $('#fileupload').each(function () {
            $(this).fileupload({
                dataType: 'json',
                url: "@Url.Action("UploadImg", new { Id = Model.Model.Id })",
                done: function (e, data) {
                    if (data != 'error') {
                        add_uploadedfile(data.result);
                    }
                }
            });
        });

        // 图片上传
        $('#fileupload2').each(function () {
            $(this).fileupload({
                dataType: 'json',
                url: "@Url.Action("UploadFile", new { Id = Model.Model.Id })",
                done: function (e, data) {
                    if (data != 'error') {
                        add_uploadedfile2(data.result);
                    }
                }
            });
        });

        $('#article_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                article_title: {
                    required: true,
                    remote: {
                        url: "@Url.Action("FinByName")",
                        type: 'get',
                        data: {
                            title: function () {
                                return $('#article_title').val();
                            },
                            Id: '@Model.Model.Id'
                        }
                    }
                },
                article_url: {
                    url: true
                },
                article_sort: {
                    number: true,
                }
            },
            messages: {
                article_title: {
                    required: '@T("标题名称不能为空")',
                    remote: '@T("该分类名称已经存在了，请您换一个")'
                },
                article_url: {
                    url: '@T("必须输入正确格式的网址")'
                },
                article_sort: {
                    number: '@T("排序只能为数字")'
                },
    @*gc_class_id:{
                     required: '@T("所属文章分类不能为空")'
                }*@
            }
        });

    });

    function add_uploadedfile(file_data) {
        var newImg = '<tr id="' + file_data.file_id + '" class="tatr2"><input type="hidden" name="file_id[]" value="' + file_data.file_id + '" /><td><img width="40px" height="40px" src="' + file_data.file_path + '" /></td><td>' + file_data.file_name + '</td><td><a href="javascript:insert_editor(\'' + file_data.file_path + '\');">插入编辑器</a> | <a href="javascript:del_file_upload(' + file_data.file_id + ');">删除</a></td></tr>';
        $('#thumbnails').prepend(newImg);
    }

    //文件的添加
    function add_uploadedfile2(file_data) {
        var newImg = '<tr id="' + file_data.file_id + '" class="tatr2"><input type="hidden" name="file_id[]" value="' + file_data.file_id + '" /><td></td><td><a href="' + file_data.file_path + '" target="_blank">' + file_data.file_name + '</a></td><td><a href="javascript:insert_editor2(\'' + file_data.file_path + '\');">插入编辑器</a> | <a href="javascript:del_file_upload(' + file_data.file_id + ');">删除</a></td></tr>';
        $('#thumbnails2').prepend(newImg);
    }

    //function insert_editor(file_path) {
    //    ue.execCommand('insertimage', { src: file_path });
    //}
    //function insert_editor2(file_path) {
    //    $("iframe:eq(0)").contents().find("body").append('<p><a href="' + file_path + '">下载</a></p>');
    //    $("[name=article_content]").val($("[name=article_content]").val() + '<a href="' + file_path + '">下载</a>')
    //}

    function insert_editor(file_path) {
        debugger;
        if (!lid) {
            var ue = UE.getEditor('article_content');
            ue.execCommand('insertimage', { src: file_path });
        } else {
            var ue = UE.getEditor('article_content_' + lid);
            ue.execCommand('insertimage', { src: file_path });
        }
    }

    function insert_editor2(file_path) {
        debugger;
        $(".layui-show iframe:eq(0)").contents().find("body").append('<p><a href="' + file_path + '">下载</a></p>');
        if (!lid) {
            $("[name=article_content]").val($("[name=article_content]").val() + '<a href="' + file_path + '">下载</a>')
        } else {
            var name = "article_content_" + lid;
            $("[name=" + name + "]").val($("[name=" + name + "]").val() + '<a href="' + file_path + '">下载</a>')
        }


    }


    function del_file_upload(file_id) {
        layer.confirm('您确定要删除吗?', {
            btn: ['确定', '取消'],
            title: false,
        }, function () {
            $.getJSON("@Url.Action("DeleteImg")", { id: + file_id }, function (result) {
                if (result) {
                    $('#' + file_id).remove();
                    layer.close(layer.index);
                } else {
                    layer.alert('删除失败');
                }
            });
        });
    }

     var data1 = $.parseJSON('@Html.Raw(ViewBag.List)');

    var demo1 = xmSelect.render({
        el: '#demo1',
        radio: false,
        paging: true,
        pageSize: 10,
        filterable: true,
        filterMethod: function (val, item, index, prop) {
            if (item.name.toLowerCase().indexOf(val.toLowerCase()) != -1) {//名称中包含的大小写都搜索出来
                return true;
            }
            return false;//其他的就不要了
        },
        pageEmptyShow: false,
        clickClose: true,
        data: data1
    });

    layui.use('laydate', function () {
        var laydate = layui.laydate;

        //执行一个laydate实例
        laydate.render({
            elem: '#CreateTime', //指定元素
            type: "datetime",
        });
    });

</script>