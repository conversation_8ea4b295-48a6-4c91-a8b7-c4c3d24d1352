﻿using B2B2CShop.Entity;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("礼品兑换")]
    [Description("礼品兑换管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/PointOrders", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/PointOrders", CurrentMenuName = "PointOrdersList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class PointOrdersController : PekCubeAdminControllerX
    {
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("礼品兑换列表")]
        public IActionResult Index(string ordersn,string buyname, int state,int page = 1,int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.ordersn = ordersn;
            viewModel.buyname = buyname;
            viewModel.state = state;
            viewModel.list = PointsOrder.Search(ordersn, buyname, state, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> {
                { "ordersn", ordersn },
                { "buyname", buyname },
                { "state", state.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("兑换详情")]
        public IActionResult PointOrderDetail(long id)
        {
            var entity = PointsOrder.FindById(id);
            if (entity == null) return Prompt(new PromptModel() { Message = GetResource("兑换记录不存在") });
            ViewBag.ordersgoodslist = PointsOrderGoods.FindAllByOrderId(entity.Id);
            return View(entity);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("礼品发货")]
        public IActionResult PointOrderShip(long id)
        {
            var entity = PointsOrder.FindById(id);
            ViewBag.expresslist = InternationalExpress.FindAllLanEnable(0);
            if (entity == null) return Prompt(new PromptModel() { Message = GetResource("兑换记录不存在") });
            if (entity.OrderState != 20) return Prompt(new PromptModel() { Message = GetResource("该状态无法发货") });
            return View(entity);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("礼品发货")]
        [HttpPost]
        public IActionResult PointOrderShip(long id,string shippingcode, int expresscode)
        {
            var entity = PointsOrder.FindById(id);
            if (entity == null) return Json(new DResult() { success = false, msg = GetResource("兑换记录不存在") });
            if(shippingcode.IsNullOrWhiteSpace()) return Json(new DResult() { success = false, msg = GetResource("请输入物流单号") });
            if (expresscode <= 0) return Json(new DResult() { success = false, msg = GetResource("请选择物流公司") });
            if (entity.OrderState != 20) return Json(new DResult() { success = false, msg = GetResource("该状态无法发货") });
            using (var tran = PointsOrder.Meta.CreateTrans())
            {
                entity.ShippingCode = shippingcode;
                entity.ShippingId = expresscode;
                entity.OrderState = 30;
                entity.Update();
                //更改库存
                var orderGoods = PointsOrderGoods.FindAllByOrderId(entity.Id);
                foreach (var item in orderGoods)
                {
                    var pointgoods = PointsGoods.FindById(item.GoodsId);
                    if (pointgoods == null) return Json(new DResult() { success = false, msg = GetResource("找不到积分商品记录") });
                    if(pointgoods.Storage < item.GoodsNum) return Json(new DResult() { success = false, msg = GetResource("积分商品库存不足，无法发货") });
                    pointgoods.Storage -= item.GoodsNum;
                    pointgoods.TemporaryStorage -= pointgoods.TemporaryStorage>0?item.GoodsNum:0;
                    pointgoods.SaleNum += item.GoodsNum;
                    pointgoods.Update();
                }
                tran.Commit();
            }
            PointsOrder.Meta.Cache.Clear("", true);
            return Json(new DResult() { success = true, msg = GetResource("发货成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("取消兑换")]
        public IActionResult CancelOrder(long id)
        {
            var entity = PointsOrder.FindById(id);
            if (entity == null) return Json(new DResult() { success = false,msg = GetResource("兑换记录不存在")});
            if (entity.OrderState!=20) return Json(new DResult() { success = false, msg = GetResource("该状态无法取消兑换") });
            using (var tran = PointsOrder.Meta.CreateTrans())
            {
                //修改状态
                entity.OrderState = 2;
                //退还积分
                var usere = UserDetail.FindById(entity.BuyerId);
                if (usere == null) return Json(new DResult() { success = false,msg = GetResource("用户不存在")});

                usere.Points += entity.AllPoint;
                usere.Update();
                var pointslog = new PointsLog();
                pointslog.UId = entity.BuyerId;
                pointslog.UName = entity.BuyerName;
                pointslog.AdminId = ManageProvider.User?.ID ?? 0;
                pointslog.AdminName = ManageProvider.User?.Name ?? "";
                pointslog.Points = entity.AllPoint;
                pointslog.Desc = $"积分礼品订单[{entity.OrderSn}] 取消兑换，返还积分";
                pointslog.Stage = "pointorder";
                pointslog.Insert();
                //更改暂扣库存
                var orderGoods = PointsOrderGoods.FindAllByOrderId(entity.Id);
                foreach (var item in orderGoods)
                {
                    var pointgoods = PointsGoods.FindById(item.GoodsId);
                    if (pointgoods == null) return Json(new DResult() { success = false, msg = GetResource("找不到积分商品记录") });
                    pointgoods.TemporaryStorage -= item.GoodsNum;
                    pointgoods.Update();
                }
                entity.Update();
                tran.Commit();
            }
            return Json(new DResult() { success = true, msg = GetResource("取消成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("删除订单")]
        public IActionResult DeleteOrder(long id)
        {
            var entity = PointsOrder.FindById(id);
            if (entity == null) return Json(new DResult() { success = false, msg = GetResource("兑换记录不存在") });
            if (entity.OrderState != 2 && entity.OrderState != 3) return Json(new DResult() { success = false, msg = GetResource("该状态无法删除") });
            entity.Delete();
            PointsOrder.Meta.Cache.Clear("", true);
            return Json(new DResult() { success = true, msg = GetResource("删除成功") });
        }

    }
}
