﻿@{

}
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>

<div class="page" id="material-list">
    @*  <form id="formSearch" method="get" name="StoreId">
        <div class="ds-search-form">
            <dl>
                <dt>@T("所属店铺")</dt>
                <dd>
                    <span class="w400">
                        <select id="Store" name="StoreId">
                            <!option value="0">
                                @T("默认")
                            </!option>
                            @foreach (SelectListItem item in storeList)
                            {
                                <!option value="@item.Value" @(item.Selected ? "selected" : "")>
                                    @item.Text
                                </!option>
                            }
                        </select>
                    </span>
                </dd>
            </dl>
        </div>
    </form> *@
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("物料出库")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="/Admin/MaterialOutbound" class="current"><span>@T("管理")</span></a></li>
                <li><a href="/biz/MaterialOutbound/Add"><span>@T("出库")</span></a></li>
            </ul>
        </div>
    </div>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th></th>
                <th>@T("ID")</th>
                <th>@T("仓库名称")</th>
                <th>@T("物料名称")</th>
                <th>@T("数量")</th>
                <th>@T("出库时间")</th>
                @* <th>@T("操作")</th> *@
            </tr>
        </thead>
        <tbody>
            @foreach (MaterialOutbound item in Model.list)
            {
                <tr>
                    <th>@* <input type="checkbox" class="checkitem" value="@item.Id"> *@</th>
                    <td>@item.Id</td>
                    <td>@item.WareHouseName</td>
                    <td>@item.MerchantMaterialName</td>
                    <td>@item.Quantity</td>
                    <td>@item.CreateTime</td>
@*                     <td>
                        <a href="javascript:dsLayerOpen('@Url.Action("ViewMaterial",new  { Id = item.Id })','@T("查看")')"
                           class="dsui-btn-edit">
                            <i class="iconfont"></i>@T("查看")
                        </a>
                    </td> *@
                </tr>
            }
        </tbody>
        @*  <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small"
                                   onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot> *@
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>

<script asp-location="Footer">
    function submit_delete(items) {
        _uri = "@Url.Action("Delete")?ids=" + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
    }
</script>

<script>

    function excute(id) {
         $.post("@Url.Action("Enabled")",{id},function (res) {
                layui.layer.open({
                    content:res,
                    btn:[],
                    area:['520px', 'auto']
                })
            })
            .fail(function(err) {
                console.log('选择失败！',err);
                // 在这里处理错误请求或者代码报错
               layui.layer.msg('@T("启用失败")')
            });
    }
    $(document).ready(function () {
        $('#Store').select2({
            placeholder: "@T("请选择店铺")",
            allowClear: true
        }).on("change", function (e) {
            var storeId = $(this).val();
            if (!storeId) $(this).val(0);
            $('#formSearch').submit();
        });
    });
</script>