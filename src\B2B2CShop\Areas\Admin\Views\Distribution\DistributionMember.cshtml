﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("分销设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("DistributionSetting")"><span>@T("分销设置")</span></a></li>
                <li><a href="@Url.Action("DistributionGoods")"><span>@T("分销商品")</span></a></li>
                <li><a href="@Url.Action("DistributionMember")" class="current"><span>@T("分销员管理")</span></a></li>
                <li><a href="@Url.Action("DistributionMemberLevel")" ><span>@T("分销员等级")</span></a></li>
                <li><a href="@Url.Action("DistributionOrder")" ><span>@T("分销员订单")</span></a></li>
            </ul>
        </div>
    </div>

    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dd>
                    <input type="text" value="@Model.searchkey" name="searchkey" class="txt w200" placeholder='@T("会员名/邮箱/手机或真实姓名")' title='@T("会员名/邮箱/手机或真实姓名进行搜索")' aria-label='@T("搜索关键字")' />
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value='@T("搜索")'>
                            </div>
        </div>
    </form>


    <table class="ds-default-table">
            <thead>
                <tr class="thead">
                    <th>&nbsp;</th>
                    <th colspan="2">@T("会员名")</th>
                    <th class="align-center">@T("分销员等级")</th>
                    <th class="align-center">@T("已分销金额")</th>
                    <th class="align-center">@T("分销商品数")</th>
                    <th class="align-center">@T("分销商品金额")</th>
                    <th class="align-center">@T("上级")</th>
                    <th class="align-center">@T("分销下级成员")</th>
                    <th class="align-center">@T("状态")</th>
                    <th class="align-center">@T("申请时间")</th>
                    <th class="align-center">@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (Inviter item in Model.list)
            {
                <tr class="hover member">
                    <td class="w24"><input type="checkbox" name='del_id[]' value="@item.Id" class="checkitem"></td>
                    <td class="w48 picture">
                        <div class="size-44x44">
                            <span class="thumb">
                                <i></i>
                                <img src="/Uploads/common/default_user_portrait.gif" width="44" height="44" />
                            </span>
                        </div>
                    </td>
                    <td>
                        <p class="name"><strong>@item.Member?.Name</strong>(<span>@T("真实姓名")</span>:@item.UserDetail?.TrueName )</p>
                        <p class="smallfont">@T("注册时间"):&nbsp;@item.Member?.RegisterTime</p>

                        <div class="im">
                            <span class="email">
                                <a href="JavaScript:void(0);" class="" title="member_index_null"></a>
                            </span>
                        </div>
                    </td>
                    <td class="align-center">@item.InviterClass.Name</td>
                    <td class="align-center">@item.GoodsAmount</td>
                    <td class="align-center">@item.GoodsQuantity</td>
                    <td class="align-center">@item.TotalAmount</td>
                    <td class="align-center"></td>
                    <td class="align-center">
                        <p>@T("一级成员")<span>@item.Quantity1</span></p>
                        <p>@T("二级成员")<span>@item.Quantity2</span></p>
                        <p>@T("三级成员")<span>@item.Quantity3</span></p>
                    </td>
                    <td class="align-center">@(item.State==0?T("待审核"):item.State == 1?T("已启用"):item.State==2?T("已禁用"):T("未知"))</td>
                    <td class="align-center">@(UnixTime.ToDateTime(item.ApplyTime))</td>
                    <td class="align-center">
                        <a href="@Url.Action("MemberDetail", new { id = item.Id })" class="dsui-btn-view"><i class="iconfont"></i>@T("查看")</a>
                        @if (item.State == 0 || item.State == 2)
                        {
                            <a href="javascript:dsLayerConfirm('@Url.Action("EnableInviter",new {ids = item.Id})','@T("您确定要启用吗?")')" class="dsui-btn-add"><i class="iconfont"></i>@T("启用")</a>
                        }
                        else
                        {
                            <a href="javascript:dsLayerConfirm('@Url.Action("DisableInviter", new { ids = item.Id })','@T("您确定要禁用吗?")')" class="dsui-btn-del"><i class="iconfont"></i>@T("禁用")</a>
                        }
                        @* <a href="javascript:dsLayerOpen('@Url.Action("AdjustTheSuperior")','@T("调整上级-xzrkm666")')" class="dsui-btn-edit"><i class="iconfont"></i>@T("调整上级")</a> *@
                    </td>
                </tr>
            }
        </tbody>
        <tfoot class="tfoot">
            <tr>
                <td class="w24"><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_disable_batch()"><span>@T("禁用")</span></a>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_enable_batch()"><span>@T("启用")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<script type="text/javascript">
    (function () {
        function getSelectedIds() {
            var checks = document.querySelectorAll('input.checkitem:checked');
            var ids = Array.prototype.map.call(checks, function (c) { return c.value; });
            return ids;
        }

        window.submit_enable_batch = function () {
            var ids = getSelectedIds();
            if (!ids || ids.length === 0) {
                layer.msg('@T("请选择要操作的项")', { icon: 0 });
                return;
            }
            var url = '@Url.Action("EnableInviter")' + '?ids=' + encodeURIComponent(ids.join(','));
            dsLayerConfirm(url, '@T("您确定要启用吗?")');
        };

        window.submit_disable_batch = function () {
            var ids = getSelectedIds();
            if (!ids || ids.length === 0) {
                layer.msg('@T("请选择要操作的项")', { icon: 0 });
                return;
            }
            var url = '@Url.Action("DisableInviter")' + '?ids=' + encodeURIComponent(ids.join(','));
            dsLayerConfirm(url, '@T("您确定要禁用吗?")');
        };
    })();
</script>