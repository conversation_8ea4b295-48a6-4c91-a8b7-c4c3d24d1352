.integral {
  width: 100%;
  height: 400px;
  background-image: url('/public/images/pics/huiyuanzhongxin.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding-top: 40px;
}

.vinfo {
  width: 322px;
  height: 353px;
  background: #FAFCFF;
  border-radius: 0px 30px 0px 0px;
  border: 1px solid rgba(44, 121, 232, 0.2);
  margin-left: 192px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  font-size: 20px;
}

.avatarBox {
  margin-top: 30px;
  width: 140px;
  height: 140px;
  line-height: 140px;
  border: 4px solid #2C79E8;
  text-align: center;
  border-radius: 50%;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
}

.textBox {
  display: flex;
  align-items: center;
  justify-content: space-around;

}

.text {
  width: 140px;
  text-align: center;
  font-size: 16px;
  color: var(--text-color2);
}

.text:first-child {
  border-right: 1px solid var(--text-color2);
}

.num {
  font-size: 20px;
  color: var(--blue-deep);
  margin-top: 10px;
}

.voucherBox {
  width: 1300px;
  margin: 0 auto;
}

.voucher {
  display: flex;
  align-items: center;
  color: var(--white);
  border-bottom: 4px solid var(--blue-deep);
  margin: 24px 0 10px;
}

.blueBox {
  padding: 0 14px;
  height: 46px;
  background: var(--blue-deep);
  display: flex;
  align-items: center;
  justify-content: space-around;
  gap: 10px;
  font-size: 20px;
}

.more {
  color: var(--text-color2);
  font-size: 14px;
}

.threeBox {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  /* 行/列间距（可调） */
}

.voucherBg {
  height: 120px;
  background-image: url('/public/images/pics/youhuiquan.png');
  background-size: 100% 100%;
  /* 拉伸图片填满容器，可能变形 */
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-color2);


}

.voucherBg-left {
  margin-left: 24px;
}

.voucherBg-right {
  margin-right: 28px;
  color: var(--white);
}

.p1 {
  color: #2C79E8;
  letter-spacing: -2px;
}

.p2 {
  margin: 3px 0;
}

.s1 {
  font-size: 36px;

}

.shop-avatar {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  margin-right: 20px;
}

.hot {
  display: flex;
  align-items: center;

}

@media screen and (max-width: 1300px) {
  .vinfo {
      margin-left: 142px;
    }
}