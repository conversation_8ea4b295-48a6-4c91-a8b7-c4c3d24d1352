@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    var localizationSettings = LocalizationSettings.Current;
    var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
}
<div class="page">
    <form method="post" id="marketmanage_form" data-api="@Url.Action("Add")" class="form-horizontal"
        enctype="multipart/form-data">
        <input type="hidden" name="type" value="@Model.type" />
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准")</li>
                    @foreach (var item in LanguageList)
                    {
                        <li data="@item.Id">@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td class="required w120">@T("活动名称")</td>
                                <td class="vatop rowform"><input id="name" name="name" value="" class="input-txt"
                                        type="text"></td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td class="required w120">@T("活动详情")</td>
                                <td class="vatop rowform"><textarea name="detail"></textarea></td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td class="required w120">@T("未中奖说明")</td>
                                <td class="vatop rowform"><textarea name="failed"></textarea></td>
                                <td class="vatop tips"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in LanguageList)
                    {
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td class="required w120">@T("活动名称")</td>
                                        <td class="vatop rowform"><input id="<EMAIL>" name="[@item.Id].name" value=""
                                                class="input-txt" type="text"></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="required w120">@T("活动详情")</td>
                                        <td class="vatop rowform"><textarea name="[@item.Id].detail"></textarea></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="required w120">@T("未中奖说明")</td>
                                        <td class="vatop rowform"><textarea name="[@item.Id].failed"></textarea></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
        </div>
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120">@T("开始时间")</td>
                    <td class="vatop rowform"><input id="begintime" name="begintime" value="" class="input-txt"
                            type="text"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("结束时间")</td>
                    <td class="vatop rowform"><input id="endtime" name="endtime" value="" class="input-txt" type="text">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("参与次数")</td>
                    <td class="vatop rowform" id="jointype">
                        <input name="jointype" value="0" type="radio" checked>@T("一共")
                        <input id="joincount_0" name="joincount" value="" class="input-txt w60" type="number" min="0"
                            step="1" lay-verify="number">
                        @T("次")
                        <input name="jointype" value="1" type="radio">@T("一天")
                        <input id="joincount_1" name="joincount" value="" class="input-txt w60" type="number" min="0"
                            step="1" disabled lay-verify="number">
                        @T("次")
                        <input name="jointype" value="2" type="radio">@T("无限制")
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("消耗积分")</td>
                    <td class="vatop rowform">
                        <input id="pointtype0" name="pointtype" value="0" type="radio" checked>@T("不消耗积分")
                        <input id="pointtype1" name="pointtype" value="1" type="radio">@T("消耗积分")
                        <input id="point" name="point" value="0" class="input-txt w36" type="number" min="0" step="1"
                            lay-verify="number">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("奖品设置")</td>
                    <td class="vatop rowform" style="width:500px;">
                        <div class="marketmanage_award_list" dstype="marketmanage_award_list">
                            <ul class="tab-menu">
                                <li class="current">@T("一等奖")</li>
                                <li>@T("二等奖")</li>
                                <li>@T("三等奖")</li>
                                <li>@T("四等奖")</li>
                            </ul>
                            @for (int i = 0; i < 4; i++)
                            {
                                <div class="marketmanage_award" @(i > 0 ? "style=display:none" : "")>
                                    <dl>
                                        <dt>@T("选择奖品")</dt>
                                        <dd>
                                            <label><input name="Awards[@i].Type" type="radio" value="1"
                                                    checked>@T("积分")</label>
                                            <label><input name="Awards[@i].Type" type="radio" value="2">@T("吸粉红包")</label>
                                            <label><input name="Awards[@i].Type" type="radio" value="3">@T("代金券")</label>
                                        </dd>
                                    </dl>
                                    <dl class="award_type_show" id="award_type_show_@(i)_1">
                                        <dt>@T("赠送积分:")</dt>
                                        <dd>
                                            <input name="Awards[@i].Point" value="0" class="input-txt w60" type="number"
                                                min="0" step="1" lay-verify="number">
                                        </dd>
                                    </dl>
                                    <dl class="award_type_show" id="award_type_show_@(i)_2" style="display:none">
                                        <dt>@T("选择红包:")</dt>
                                        <dd>
                                            <select name="Awards[@i].BonusId">
                                                @foreach (Bonus item in Model.bonusList)
                                                {
                                                    <option value="@item.Id">@item.Name</option>
                                                }
                                            </select>
                                        </dd>
                                    </dl>
                                    <dl class="award_type_show" id="award_type_show_@(i)_3" style="display:none">
                                        <dt>@T("选择优惠券:")</dt>
                                        <dd>
                                            <select name="Awards[@i].VoucherTemplateId">
                                                @foreach (VoucherTemplate item in Model.voucherlist)
                                                {
                                                    <option value="@item.Id">@item.Title</option>
                                                }
                                            </select>
                                        </dd>
                                    </dl>
                                    <dl>
                                        <dt>@T("奖品数量:")</dt>
                                        <dd><input name="Awards[@i].Count" value="0" class="input-txt w60" type="number"
                                                min="0" step="1" lay-verify="number"><em>@T("中奖数量：0")</em></dd>
                                    </dl>
                                    <dl>
                                        <dt>@T("奖品概率:")</dt>
                                        <dd><input name="Awards[@i].Probability" value="0" class="input-txt w60"
                                                type="number" min="0" max="100" step="1"
                                                lay-verify="prob">%<em>@T("单个奖品中奖概率(0%-100%)")</em></dd>
                                    </dl>
                                </div>
                            }
                        </div>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><button type="submit" class="btn" lay-submit
                            lay-filter="marketmanage_submit">@T("确认提交")</button></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<style>
    .marketmanage_award_list {
        border: 1px solid #ccc;
        background: #f2f2f2;
    }

    .marketmanage_award_list .tab-menu {
        display: flex;
        margin: 0;
        width: auto;
    }

    .marketmanage_award_list .tab-menu li {
        background: #fff;
        margin: 0;
        flex: 1;
        height: 40px;
        text-align: center;
        line-height: 40px;
        padding: 0;
        border: 1px solid #ccc;
        border-left-color: transparent;
        border-top-color: transparent;
        cursor: pointer;
    }

    .marketmanage_award_list .tab-menu li.current {
        background: #f2f2f2;
        border-bottom-color: transparent;
    }

    .marketmanage_award_list .tab-menu li:last-child {
        border-right-color: transparent;
    }

    .marketmanage_award {
        margin-bottom: 10px;
        padding: 30px 50px;
    }

    .marketmanage_award dl {
        margin: 15px 0;
        height: 30px;
        line-height: 30px;
    }

    .marketmanage_award dt {
        float: left;
        width: 80px;
        height: 30px;
    }

    .marketmanage_award dd {
        height: 30px;
        float: left;
    }

    .marketmanage_award dd {
        height: 30px;
        float: left;
    }

    .marketmanage_award dd em {
        font-size: 12px;
        margin-left: 10px;
        color: #999;
    }

    .marketmanage_award select {
        width: 100px;
    }
</style>
<script>
    layui.use(['form', 'laydate'], function () {
        var form = layui.form;
        var laydate = layui.laydate;
        var startIns, endIns;

        // 设置默认日期为今天和明天（格式 yyyy-MM-dd）如果输入框为空
        function _fmtDate(d) { var y = d.getFullYear(); var m = (d.getMonth() + 1).toString().padStart(2, '0'); var dd = d.getDate().toString().padStart(2, '0'); return y + '-' + m + '-' + dd; }
        var _today = new Date();
        var _tomorrow = new Date(_today);
        _tomorrow.setDate(_tomorrow.getDate() + 1);
        var _startEl = document.getElementById('startdate');
        var _endEl = document.getElementById('enddate');
        try {
            if (_startEl && (!_startEl.value || _startEl.value.trim() === '')) _startEl.value = _fmtDate(_today);
            if (_endEl && (!_endEl.value || _endEl.value.trim() === '')) _endEl.value = _fmtDate(_tomorrow);
        } catch (e) { }

        // 日期选择器初始化
        startIns = laydate.render({
            elem: '#begintime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            value: (_startEl && _startEl.value) ? _startEl.value : undefined,
            trigger: 'click',
            done: function (value) {
                if (value) {
                    try { endIns.config.min = value; } catch (e) { }
                } else {
                    try { endIns.config.min = ''; } catch (e) { }
                }
            }
        });

        endIns = laydate.render({
            elem: '#endtime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            value: (_endEl && _endEl.value) ? _endEl.value : undefined,
            trigger: 'click',
            done: function (value) {
                if (value) {
                    try { startIns.config.max = value; } catch (e) { }
                } else {
                    try { startIns.config.max = ''; } catch (e) { }
                }
            }
        });
        updateJointypeInputs();

        // 监听 jointype 单选按钮变化
        $("input[name='jointype']").change(function () {
            updateJointypeInputs();
        });

        // 更新输入框状态的函数
        function updateJointypeInputs() {
            var selectedValue = $("input[name='jointype']:checked").val();

            // 根据选中值设置输入框状态
            if (selectedValue === "0") { // 一共
                $("#joincount_0").prop("disabled", false);
                $("#joincount_1").prop("disabled", true);
                // 当选择一共时，将每天限制重置为0
                try { $("#joincount_1").val('0'); } catch (e) { }
                // 只保留总次数的 name，避免序列化重复字段
                try { $("#joincount_0").attr('name', 'joincount'); $("#joincount_1").removeAttr('name'); } catch (e) { }
            } else if (selectedValue === "1") { // 一天
                $("#joincount_0").prop("disabled", true);
                $("#joincount_1").prop("disabled", false);
                // 当选择一天时，将总次数重置为0
                try { $("#joincount_0").val('0'); } catch (e) { }
                // 只保留每天次数的 name，避免序列化重复字段
                try { $("#joincount_1").attr('name', 'joincount'); $("#joincount_0").removeAttr('name'); } catch (e) { }
            } else if (selectedValue === "2") { // 无限制
                $("#joincount_0").prop("disabled", true);
                $("#joincount_1").prop("disabled", true);
                // 无限制时清零所有次数输入
                try { $("#joincount_0").val('0'); $("#joincount_1").val('0'); } catch (e) { }
                // 无限制时移除 name，避免提交
                try { $("#joincount_0").removeAttr('name'); $("#joincount_1").removeAttr('name'); } catch (e) { }
            }
        }

        // 初始加载时执行一次
        updatePointInput();

        // 监听 pointtype 单选按钮变化
        $("input[name='pointtype']").change(function () {
            updatePointInput();
        });

        // 更新输入框状态的函数
        function updatePointInput() {
            var selectedValue = $("input[name='pointtype']:checked").val();

            // 根据选中值设置输入框状态
            if (selectedValue === "0") { // 不消耗积分
                $("#point").prop("disabled", true);
            } else if (selectedValue === "1") { // 消耗积分
                $("#point").prop("disabled", false);
            }
        }

        // 监听tab-menu点击事件
        $(".tab-menu li").click(function () {
            // 移除所有li的current类
            $(".tab-menu li").removeClass("current");
            // 为当前点击的li添加current类
            $(this).addClass("current");

            // 隐藏所有奖品设置div
            $(".marketmanage_award").hide();
            // 显示对应序号的奖品设置div
            var index = $(this).index();
            $(".marketmanage_award").eq(index).show();
        });

        // 初始化并绑定每个面板的 Awards[N].Type 切换逻辑，使用唯一的 #award_type_show_{i}_{val} id
        $(".marketmanage_award").each(function (panelIndex) {
            var $panel = $(this);
            var idx = panelIndex; // 0-based

            // 初始显示
            var $checked = $panel.find('input[type=radio][name="Awards[' + idx + '].Type"]:checked');
            if ($checked && $checked.length) {
                var val = $checked.val();
                $panel.find('.award_type_show').hide();
                $panel.find('#award_type_show_' + idx + '_' + val).show();
            }

            // 绑定 change
            $panel.find('input[type=radio][name="Awards[' + idx + '].Type"]').change(function () {
                if (this.checked) {
                    var v = $(this).val();
                    $panel.find('.award_type_show').hide();
                    $panel.find('#award_type_show_' + idx + '_' + v).show();
                }
            });
        });

        // 使用 layui 的 form.on 提交处理
        // 自定义验证规则：number 为非负整数，prob 为 0-100
        form.verify({
            number: function (value, item) {
                if (value === undefined || value === null || value === '') return null;
                var v = Number(value);
                if (!Number.isFinite(v) || v < 0 || Math.floor(v) !== v) {
                    return '请输入有效的非负整数';
                }
            },
            prob: function (value, item) {
                if (value === undefined || value === null || value === '') return null;
                var v = Number(value);
                if (!Number.isFinite(v) || v < 0 || v > 100) {
                    return '概率必须在 0 到 100 之间';
                }
            }
        });

        form.on('submit(marketmanage_submit)', function (data) {
            var $form = $("#marketmanage_form");
            var apiUrl = $form.data('api');


            // 包含 disabled 字段的序列化：临时启用并序列化
            var $disabled = $form.find(':input:disabled');
            var disabledElems = $disabled.toArray();
            $($disabled).prop('disabled', false);
            var postData = $form.serialize();
            $(disabledElems).prop('disabled', true);

            // 简单校验
            var name = $.trim($form.find('input[name="name"]').val() || '');
            if (!name) {
                layer.msg('活动名称不能为空', { icon: 2 });
                return false;
            }

            $.ajax({
                url: apiUrl,
                type: 'POST',
                data: postData,
                dataType: 'json',
                success: function (res) {
                    if (res && res.success) {
                        layer.msg(res.msg || '操作成功', { icon: 1 });
                        setTimeout(function () {
                            try {
                                // 如果该页面在 layer 弹窗内，先关闭弹窗并刷新父页面
                                if (window.parent && window.parent !== window && window.parent.layer && window.name) {
                                    var idx = window.parent.layer.getFrameIndex(window.name);
                                    // 关闭当前弹窗
                                    window.parent.layer.close(idx);
                                    // 刷新父页面列表
                                    try { window.parent.location.reload(); } catch (e) { }
                                } else {
                                    // 否则处理 returnUrl 或在本页刷新
                                    if (res.returnUrl) location.href = res.returnUrl;
                                    else location.reload();
                                }
                            } catch (e) {
                                if (res.returnUrl) location.href = res.returnUrl;
                                else location.reload();
                            }
                        }, 800);
                    } else {
                        layer.msg((res && res.msg) || '操作失败', { icon: 2 });
                    }
                },
                error: function (xhr, status, err) {
                    layer.msg('请求出错：' + (err || status), { icon: 2 });
                }
            });

            return false; // 阻止 layui 的默认跳转/表单提交
        });

    });
</script>