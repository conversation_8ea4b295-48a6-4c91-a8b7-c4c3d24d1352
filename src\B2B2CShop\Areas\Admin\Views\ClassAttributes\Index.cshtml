﻿@using Pek.IO

<style>
    img {
        object-fit: contain;
        max-width: 250px;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("商品分类属性")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("Add")?ClassId=@Model.ClassId"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th></th>
                <th>@T("分类属性ID")</th>
                <th>@T("分类属性名称")</th>
                <th>@T("分类属性字段")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (ClassAttributes item in Model.list)
            {
                <tr>
                    <th><input type="checkbox" class="checkitem" value="@item.Id"></th>
                    <td>@item.Id</td>
                    <td>@item.Name</td>
                    <td>@item.MappingField</td>
                    <td class="w96" style="width: 120px;">
                        <a class="skyblue" href="@Url.Action("Edit",new { Id=item.Id})">@T("编辑")</a> |
                        <a class="skyblue" href="javascript:dsLayerConfirm('@Url.Action("Delete",new {Ids=item.Id})','@T("您确定要删除该数据吗")?')">@T("删除")</a>
                    </td>
                </tr>
            }
            @if (Model.list.Count == 0)
            {
                <tr class="no_data">
                    <td colspan="10">@T("没有符合条件的记录")</td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small"
                                   onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>

</div>
<script asp-location="Footer">
    function submit_delete(items) {
        _uri = "@Url.Action("Delete")?ids=" + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
         window.location.reload();
    }
</script>
