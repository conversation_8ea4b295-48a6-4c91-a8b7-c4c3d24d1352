﻿@using Pek.IO
@{
    var localizationSettings = LocalizationSettings.Current;
}
@model Currencies
<style asp-location="true">
    .type-file-preview {
        z-index: 99999
    }

    ul.layui-tab-title {
        width: 95%;
        margin: 0 auto;
    }
</style>

<div class="page">
    @using (Html.BeginForm("Edit", "Currencies", FormMethod.Post, new { id = "form1", enctype = "multipart/form-data" }))
    {
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="ncap-form-default">
                        <dl>
                            <dt>@T("货币名称")</dt>
                            <dd>
                                <input id="name" name="name" value="@Model.Name" class="input-txt" type="text">
                                <span class="err"></span>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("货币编码")</dt>
                            <dd>
                                <input id="code" name="code" value="@Model.Code" class="input-txt" type="text">
                                <span class="err"></span>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("图片标识")</dt>
                            <dd>
                                <span class="type-file-show">
                                    @if (Model.ImgPath != null)
                                    {
                                        <img class="show_image" src="/static/admin/images/preview.png">
                                        <div class="type-file-preview"><img src="@ViewBag.Images"></div>
                                    }
                                </span>
                                <span class="type-file-box">
                                    <input type='text' name='textfield' id='textfield' class='type-file-text' />
                                    <input type='button' name='fileupload' id='fileupload' value='上传' class='type-file-button' />
                                    <input name="default_user_portrait" type="file" class="type-file-file" id="default_user_portrait" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                                </span>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("左符号")</dt>
                            <dd>
                                <input id="symbolLeft" name="symbolLeft" value="@Model.SymbolLeft" class="input-txt" type="text">
                                <span class="err"></span>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("右符号")</dt>
                            <dd>
                                <input id="symbolRight" name="symbolRight" value="@Model.SymbolRight" class="input-txt" type="text">
                                <span class="err"></span>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("小数位数")</dt>
                            <dd>
                                <input id="decimalPlace" name="decimalPlace" value="@Model.DecimalPlace" class="input-txt" type="text">
                                <span class="err"></span>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("汇率值")</dt>
                            <dd>
                                <input id="exchangeRate" name="exchangeRate" value="@Model.ExchangeRate" class="input-txt" type="text">
                                <span class="err"></span>
                            </dd>
                        </dl>

                    </div>
                </div>
                <div class="ncap-form-default">
                    <dl>
                        <dt></dt>
                        <dd><input class="btn" type="submit" value="提交"></dd>
                    </dl>
                </div>
            </div>
        </div>

    }
</div>
<script src="/static/plugins/js/jquery-file-upload/jquery.fileupload.js"></script>
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>

<script src="~/static/admin/js/xm-select.js"></script>
<script type="text/javascript" asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,

            element = layui.element;
    })
    $(function () {
        $("#default_user_portrait").change(function () {
            $("#textfield").val($("#default_user_portrait").val());
        });

        $(".type-file-file").change(function () {
            var id = $(this).attr("data");
            var obj = $(this).siblings().eq(0);
            obj.val($(this).val());
        })

         // 图片上传
        $('#fileupload').each(function () {
            $(this).fileupload({
                dataType: 'json',
                url: "@Url.Action("UploadImg", new { Id = 0 })",
                done: function (e, data) {
                    if (data != 'error') {
                        add_uploadedfile(data.result);
                    }
                }
            });
        });
    });

</script>
