﻿@using B2B2CShop.Dto
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("会员等级折扣")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("店铺折扣")</span></a></li>
                <li><a href="@Url.Action("GoodsDiscount")" class="current"><span>@T("商品折扣")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("MemberDiscountSetting ")','@T("折扣设置")')"><span>@T("折扣设置")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("店铺名称")</dt>
                <dd>
                    <select id="storeId" name="storeId" class="form-control" style="width:200px;">
                        <!option value="">@T("全部")</!option>
                        @foreach (var store in Model.storelist)
                        {
                            <!option value="@store.Id" @(Model.storeId == store.Id ? "selected" : "")>@store.Name</!option>
                        }
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
                <a href="/index.php/admin/Store/store.html" class="btn btn-default" title="@T("取消")">@T("取消")</a>
            </div>
        </div>
    </form>




    <table class="ds-default-table">
        <thead>
            <tr class="thead">
                <th class="w200">@T("商品名称")</th>
                <th class="w120">@T("商品折扣")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (Goods item in Model.list)
            {

                <tr class="hover edit">
                    <td>
                        @item.Name
                    </td>
                    <td>
                         @foreach (MgDiscountDto diccount in item.MgDiscountDto)
                        {
                            @(diccount.GradeName +"/" +diccount.Discount + T("折")) <br/>
                        }
                    </td>
                </tr>
            }
        </tbody>
    </table>
        <ul class="pagination">
            @Html.Raw(Model.PageHtml)
        </ul>
</div>
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>
<script type="text/javascript">
    $(function() {
        $('#storeId').select2({
            placeholder: '@T("请选择店铺")',
            allowClear: true,
        });
    });
</script>