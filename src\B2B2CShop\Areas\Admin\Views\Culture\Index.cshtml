﻿<style asp-location="true">
    .opt_for {
        color: #aaa !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("多语言")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("AddCulture")"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("关键字")</dt>
                <dd><input type="text" value="@Model.name" name="name" class="txt"></dd>
                <dd>
                    <select name="status">
                        <option value="-1">@T("请选择")</option>
                        <!option value="1" @(Model.status == 1 ? "selected" : "")>@T("有效")</!option>
                        <!option value="0" @(Model.status == 0 ? "selected" : "")>@T("无效")</!option>
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
            </div>
        </div>
    </form>
    <table class="ds-default-table">
        <thead>
            <tr>
                <th class="w24"></th>
                <th>@T("标题")</th>
                <th>@T("显示名称")</th>
                <th>@T("英文名")</th>
                <th>@T("语言缩写")</th>
                <th>@T("Url缩写")</th>
                <th>@T("默认")</th>
                <th>@T("无效")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.list)
            {
                <tr id="ds_row_11" style="background: rgb(255, 255, 255);">
                    <td><input type="checkbox" class="checkitem" name="nav_id[]" value="@item.Id"></td>
                    <td>@item.Name</td>
                    <td>@item.DisplayName</td>
                    <td>@item.EnglishName</td>
                    <td>@item.LanguageCulture</td>
                    <td>@item.UniqueSeoCode </td>
                    <td>@(item.IsDefault == 1 ? "是" : "否")</td>
                    <td>@(item.Status?"有效":"无效")</td>
                    <td>
                        <a href="@Url.Action("EditCulture",new { Id=item.Id})" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                        @*<a href="javascript:;" onclick="submit_delete('@item.Id')" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>*@
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { Ids = item.Id })','您确定要删除吗?',@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<script asp-location="Footer">
    function submit_delete(ids_str) {
        _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }
</script>
