﻿<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("规格管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index", "GoodsSpec")"><span>@T("规格管理")</span></a></li>
                <li><a href="@Url.Action("AddSpec", "GoodsSpec")" class="current"><span>@T("添加规格")</span></a></li>
            </ul>
        </div>
    </div>

    <form action="@Url.Action("AddSpec")" method="post" id="addSpecForm">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120">@T("分类名称")</td>
                    <td class="vatop rowform">
                        <select name="rubbish" id="classificationId" class="w200">
                            <option value="0">@T("请选择分类")</option>
                        </select>
                    </td>
                    <td class="vatop tips">
                        <p class="notic">@T("请选择商品分类")</p>
                    </td>
                    <input type="hidden" name="subClassificationId" id="subClassificationId" value="0">
                </tr>

                <!-- 子分类容器 -->
                <tr class="noborder">
                    <td colspan="3">
                        <div id="subClassificationContainer"></div>
                    </td>
                </tr>

                <tr class="noborder">
                    <td class="required w120">@T("规格名称")</td>
                    <td class="vatop rowform">
                        <input type="text" name="specificationName" id="specificationName" class="w200" />
                    </td>
                    <td class="vatop tips">
                        <p class="notic">@T("请输入规格名称")</p>
                    </td>
                </tr>

                <tr class="noborder">
                    <td class="required w120">@T("排序")</td>
                    <td class="vatop rowform">
                        <input type="number" name="sort" id="sort" class="w200" />
                    </td>
                    <td class="vatop tips">
                        <p class="notic">@T("数字越小越靠前")</p>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td></td>
                    <td colspan="15">
                        <input class="btn" type="submit" value="@T("提交")" />
                        <a href="@Url.Action("Index")" class="btn ml10">@T("返回")</a>
                    </td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>

<script asp-location="Footer">
$(function () {
    initGoodsClass();
    bindClassChange();
    initFormSubmit();
});

/**
 * 初始化商品分类
 */
function initGoodsClass() {
    loadGoodsClass(0, '#classificationId');
}

/**
 * 绑定一级分类change事件
 */
function bindClassChange() {
    $('#classificationId').on('change', function() {
        var selectedId = $(this).val();
        // 清空子分类容器
        $('#subClassificationContainer').empty();
        if (selectedId) {
            loadSubGoodsClass(selectedId, 1); // 传入当前级别参数
        }
        $('#subClassificationId').val(selectedId);
    });
}

/**
 * 初始化表单提交
 */
function initFormSubmit() {
    $('#addSpecForm').on('submit', function() {
        // 不需要特别处理select了，因为最后一级的select会自动覆盖前面的值
        return true;
    });
}

/**
 * 加载商品分类
 * param {number} parentId - 父级分类ID
 * param {string} targetSelector - 目标选择器
 */
function loadGoodsClass(parentId, targetSelector) {
    var _url = "@Url.Action("GetGoodsClass")";
    $.getJSON(_url, { pid: parentId }, function(data) {
        var options = buildClassOptions(data);
        $(targetSelector).html(options);
    });
}

/**
 * 加载子分类
 * param {number} parentId - 父级分类ID
 * param {number} level - 当前级别（1表示二级分类，2表示三级分类）
 */
function loadSubGoodsClass(parentId, level) {
    // 如果已经是三级分类，则不再加载
    if (level > 2) {
        return;
    }

    var _url = "@Url.Action("GetGoodsClass")";
    $.getJSON(_url, { pid: parentId }, function(data) {
        if (data && data.length > 0) {
            var subClassHtml = buildSubClassHtml(data, level,'none');
            $('#subClassificationContainer').append(subClassHtml);
            
            // 绑定新添加的子分类选择事件
            bindSubClassChange(level);
        }
    });
}

/**
 * 构建分类选项HTML
 */
function buildClassOptions(data) {
    var options = '<option value="">请选择分类</option>';
    $.each(data, function(index, item) {
        options += '<option value="' + item.Id + '">' + item.Name + '</option>';
    });
    return options;
}

/**
 * 构建子分类HTML
 * param {Array} data - 分类数据
 * param {number} level - 当前级别
 * param {string} formData-name - 标签的名称
 */
function buildSubClassHtml(data, level,name='subClassificationId') {
    var levelText = level === 1 ? "二级分类" : "三级分类";
    
    var html = '<tr class="noborder">';
    html += '<td class="required w120">' + levelText + '</td>';
    html += '<td class="vatop rowform">';
    // 关键修改：三级分类的name要设置为非后端字段，反正就是不要了；当三级存在时，三级的值应该去覆盖二级的值；
    html += '<select name="' + name + '" class="w200 sub-class-select" data-level="' + level + '">';
    html += buildClassOptions(data);
    html += '</select>';
    html += '</td>';
    html += '<td class="vatop tips">';
    html += '<p class="notic">请选择' + levelText + '</p>';
    html += '</td>';
    html += '</tr>';
    return html;
}
/**
 * 绑定子分类change事件
 * param {number} currentLevel - 当前级别
 */
function bindSubClassChange(currentLevel) {
    $('.sub-class-select[data-level="' + currentLevel + '"]').on('change', function() {
        var selectedId = $(this).val();
        console.log('selectedId',selectedId)
        // 清除当前级别之后的所有子分类
        $('.sub-class-select[data-level="' + (currentLevel + 1) + '"]').closest('tr').remove();
        console.log('currentLevel',currentLevel)
        if(selectedId && currentLevel === 2){ //三级分类
            $('#subClassificationId').val(selectedId); //三级分类的值要覆盖二级分类的值
        }else if (selectedId) { //二级分类
            loadSubGoodsClass(selectedId, currentLevel + 1);
            if(currentLevel === 1){
                $('#subClassificationId').val(selectedId); //二级分类的值要覆盖一级分类的值
            }
        }
    });
}
</script>