﻿using Aop.Api.Domain;
using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife.Data;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("推荐展位")]
    [Description("推荐展位管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/RecommendedBooth", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/RecommendedBooth", CurrentMenuName = "RecommendedBoothList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class RecommendedBoothController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("优惠套装列表")]
        public IActionResult Index(long classId1,long classId2,long storeId, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };

            viewModel.list = BoothGoods.SearchByCategory(classId1,classId2, storeId, pages);
            viewModel.storelist = Store.FindAll();
            viewModel.storeId = storeId;
            viewModel.classId1 = classId1;
            viewModel.classId2 = classId2;
            viewModel.classlist = GoodsClass.FindAllByParentId(0);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {
                { "classId1", classId1.SafeString() },
                { "classId2", classId2.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐管理")]
        public IActionResult RecommendedBoothPackage(long storeId, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "UpdateTime",
                Desc = true
            };
            viewModel.storelist = Store.FindAll();
            viewModel.storeId = storeId;
            viewModel.list = BoothQuota.Search(storeId, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("DiscountSuitPackage"), new Dictionary<string, string> {
                { "storeId", storeId.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        public IActionResult RecommendedBoothSetting()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.price = OperationConfig.GetValueByCode("BoothPackagePrice").ToDecimal();//推荐展位套餐价格
            viewModel.maxnum = OperationConfig.GetValueByCode("BoothStoreMaxNum").ToInt();//推荐展位每个店铺推荐商品的最大数量
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        [HttpPost]
        public IActionResult PackageSet(decimal price, int maxnum)
        {
            // 参数校验
            if (price < 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("价格不能为负数") });
            }
            if (maxnum < 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("最大数量不能为负数") });
            }

            var flag = OperationConfig.UpdateValueByCode("BoothPackagePrice", price.SafeString(), "推荐展位套餐价格");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到推荐展位套餐价格配置") });
            }

            flag = OperationConfig.UpdateValueByCode("BoothStoreMaxNum", maxnum.SafeString(), "推荐展位每个店铺推荐商品的最大数量");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到推荐展位每个店铺推荐商品的最大数量配置") });
            }

            OperationConfig.Meta.Cache.Clear("", true);

            return Json(new DResult() { success = true, msg = GetResource("设置成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("删除推荐展位商品")]
        public IActionResult DelBoothGoods(long id)
        {
            var booth = BoothGoods.FindById(id);
            if (booth == null || booth.Id <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到推荐展位商品") });
            }
            booth.Delete();
            BoothGoods.Meta.Cache.Clear("删除推荐展位", true);
            return Json(new DResult() { success = true, msg = GetResource("删除成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("获取商品分类")]
        public IActionResult GetGoodsClass(long classId1)
        {
            var list = GoodsClass.FindAllByParentId(classId1).Select(e => new
            {
                Id = e.Id.SafeString(),
                Name = e.Name
            });
            return Json(new { code = 0, data = list });
        }
    }
}
