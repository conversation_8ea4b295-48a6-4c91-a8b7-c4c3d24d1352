﻿using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;

using NewLife;
using NewLife.Common;
using NewLife.Data;
using NewLife.Log;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.DsMallUI.Models;
using Pek.Helpers;
using Pek.Models;
using Pek.NCube;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;
using static SKIT.FlurlHttpClient.Wechat.TenpayV3.Models.CreateNewTaxControlFapiaoApplicationRequest.Types.Fapiao.Types;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>
/// 国家/地区
/// </summary>
[DisplayName("国家/地区")]
[Description("用于国家/地区的管理")]
[AdminArea]
[DHMenu(77, ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/Regions", CurrentMenuName = "RegionsList", CurrentIcon = "&#xe720;", LastUpdate = "20241205")]
public class RegionsController : PekCubeAdminControllerX
{
    private readonly IWorkContext _workContext;

    public RegionsController(IWorkContext workContext) => _workContext = workContext;

    /// <summary>
    /// 国家/地区管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("国家/地区管理")]
    public IActionResult Index(string key, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();


        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            OrderBy = "DisplayOrder",
            Desc = false,
            RetrieveTotalCount = true
        };

        var exp = new WhereExpression();
        if (key.IsNotNullAndWhiteSpace()) exp &= (Expression)(Country._.Name.Contains(key) | Country._.ContinentCode.Contains(key) | Country._.TwoLetterIsoCode.Contains(key));
        var list = Country.FindAll(exp, pages).Select(e => new Country
        {
            Id = e.Id,
            DisplayOrder = e.DisplayOrder,
            TwoLetterIsoCode = e.TwoLetterIsoCode,
            Name = (CountryLan.FindByCIdAndLId(e.Id, WorkingLanguage.Id)?.Name).IsNullOrWhiteSpace() ? e.Name :  CountryLan.FindByCIdAndLId(e.Id,WorkingLanguage.Id)?.Name,
        });
        viewModel.list = list;
        viewModel.page = page;
        viewModel.key = key;
        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "key", key } });

        return View(viewModel);
    }

    /// <summary>
    /// 省份管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("省份管理")]
    public IActionResult Province(string id)
    {
        dynamic viewModel = new ExpandoObject();

        var list = Regions.FindAllByCIdAndLevel(id, 0).OrderBy(x => x.Id).Select(x => new { Name = (RegionsLan.FindByRIdAndLId(x.Id,WorkingLanguage.Id)?.Name).IsNullOrWhiteSpace() ? x.Name : RegionsLan.FindByRIdAndLId(x.Id, WorkingLanguage.Id)?.Name, type = 1, Id = x.Id, x.AreaCode, deep = x.Level, x.Regional, subset = Regions.FindByParentCode(x.AreaCode.ToDGLong()).Count > 0, Sort = x.Sort }).ToDynamicList();
        viewModel.list = list;

        var Model2 = Country.FindByTwoLetterIsoCode(id);
        if (Model2 != null)
        {
            ViewBag.CName = Model2.Name;
            ViewBag.CountryId = Model2.Id;
        }

        return View(viewModel);
    }

    /// <summary>
    /// 获取区域表下级数据
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="type">0为国家 1为区域</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取区域表下级数据")]
    public IActionResult GetSubordinateData(string Id, int type)
    {
        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
        {
            var zList = new List<Hierarchy>();
            if (type == 0)
            {
                var list = Regions.FindAllByCIdAndLevel(Id, 0).OrderBy(x => x.Id);//国家点击第一次
                foreach (var item in list)
                {
                    var model = new Hierarchy
                    {
                        gc_name = item.Name.SafeString().Trim(),
                        gc_id = item.Id,
                        type = 1,
                        AreaCode = item.AreaCode.ToDGLong(),
                        CreateUser = item.Regional.IsNullOrWhiteSpace() ? "" : item.Regional,
                        ParentCode = item.ParentCode.ToDGLong()
                    };
                    var exc = Regions.FindAllByParentCode(item.AreaCode);
                    if (exc.Count > 0)
                    {
                        model.have_child = 1;
                    }

                    model.gc_show = 1;
                    model.gc_sort = item.Id;
                    model.deep = item.Level;
                    model.Sort = item.Sort;
                    zList.Add(model);
                }

            }
            else if (type == 1)
            {
                return Json(GetHierarchy(Id));
            }
            return Json(zList);
        }
        else
        {
            return Json(GetHierarchy(Id));
        }
    }

    /// <summary>
    /// 根据父级行政id获取list
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    public static List<Hierarchy> GetHierarchy(string Id)
    {
        var zList = new List<Hierarchy>();
        if (Id.IsNullOrWhiteSpace())
        {
            return zList;
        }
        var list = Regions.FindAllByParentCode(Id);
        foreach (var item in list)
        {
            var model = new Hierarchy
            {
                gc_name = item.Name,
                gc_id = item.Id,
                AreaCode = item.AreaCode.ToDGLong(),
                ParentCode = item.ParentCode.ToDGLong()
            };
            var exc = Regions.FindAllByParentCode(item.AreaCode);
            if (exc.Count > 0)
            {
                model.have_child = 1;
            }
            model.gc_show = 1;
            model.type = 1;
            model.gc_sort = item.Id;
            model.deep = item.Level;
            model.Sort = item.Sort;
            zList.Add(model);
        }
        return zList;
    }

    /// <summary>
    /// 新增页面
    /// </summary>
    /// <param name="parentId"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增页面")]
    public IActionResult AddRegions(string parentId,long AreaCode, bool isMul)
    {
        dynamic viewModel = new ExpandoObject();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            ViewBag.CountryList = Country.FindAllWithCache().OrderBy(e => e.Id).Select(e => new SelectListItem { Value = e.TwoLetterIsoCode, Text = CountryLan.FindNameByCIdAndLId(e.Id, _workContext.WorkingLanguage.Id) });
        }
        ViewBag.LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);

        if (isMul == true)
        {
            var Model2 = Country.FindById(parentId.ToInt());
            ViewBag.Cid = Model2?.TwoLetterIsoCode;
        }
        else
        {
            var Model = Regions.FindById(parentId.ToInt());
            if (Model != null)
            {
                ViewBag.ParentCode = Model.ParentCode;
                ViewBag.Name = Model.Name;
                ViewBag.AreaCode = AreaCode;
                ViewBag.AreaName = Regions.FindByAreaCode(AreaCode.ToString());
                ViewBag.shortName = Model.MergerName;
                ViewBag.Level = Model.Level;
                ViewBag.Cid = Model.CId;
                ViewBag.Id = Model.Id;
            }
            else
            {
                var Model2 = Country.FindById(parentId.ToInt());
                ViewBag.Cid = Model2?.TwoLetterIsoCode;
            }
        }

        return View(viewModel);
    }

    /// <summary>
    /// 修改列表字段值
    /// </summary>
    /// <param name="value">修改名称</param>
    /// <param name="Id">分类编号</param>
    /// <param name="column">字段名</param>
    /// <param name="type"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("修改列表字段值")]
    public IActionResult ChangeName(String value, string Id, String column, int type = 1, int ctype = 2)
    {
        if (value.IsNullOrWhiteSpace()) return Json(true);

        value = value.SafeString().Trim();
        column = column.SafeString().Trim();

        var localizationSettings = LocalizationSettings.Current;
        if (localizationSettings.IsEnable)
        {
            if (type == 0 || ctype == 0)
            {
                var CountryModels = Country.FindByTwoLetterIsoCode(Id);
                if (CountryModels != null)
                {
                    if (column == "area_name")
                    {
                        var Model = Country.FindByName(value);
                        if (Model != null && Model.Id != CountryModels.Id)
                        {
                            return Json(false);
                        }
                        CountryModels.Name = value;
                    }
                    else if (column == "gc_sort")
                    {
                        CountryModels.DisplayOrder = value.ToDGShort();
                    }
                    CountryModels.Update();
                }
                else
                {
                    return Json(false);
                }
            }
            else
            {
                var Models = new Regions();
                if (type == 1 && ctype == 1)
                {
                    Models = Regions.FindByAreaCode(Id);
                }
                else if (ctype == 1)
                {
                    Models = Regions.FindByAreaCode(Id);
                }
                else
                {
                    Models = Regions.FindById(Id.ToDGInt());
                }
                if (Models == null) return Json(false);
                if (column == "area_name")
                {
                    var Model = Regions.FindByName(value);
                    if (Model != null && Model.Id != Id.ToDGInt() && Model.ParentCode == Models.ParentCode)
                        if (Model != null && Model.Id != Models.Id && Model.ParentCode == Models.ParentCode)
                        {
                            return Json(false);
                        }

                    Models.Name = value;
                }
                else if (column == "area_region")
                {
                    Models.Regional = value;
                }
                else if (column == "gc_sort")
                {
                    Models.Sort = value.ToInt();
                }
                else
                {
                    return Json(false);
                }
                Models.Update();
            }
        }
        else
        {
            var Models = Regions.FindById(Id.ToDGInt());
            if (Models == null) return Json(false);
            if (column == "area_name")
            {
                var Model = Regions.FindByName(value);
                if (Model != null && Model.Id != Id.ToDGInt() && Model.ParentCode == Models.ParentCode)
                {
                    return Json(false);
                }
                Models.Name = value;
            }
            else if (column == "area_region")
            {
                Models.Regional = value;
            }
            else if (column == "gc_sort")
            {
                Models.Sort = value.ToInt();
            }
            else
            {
                return Json(false);
            }
            Models.Update();
        }
        Regions.Meta.Cache.Clear("", true);
        return Json(true);
    }

    /// <summary>
    /// 新增区域接口
    /// </summary>
    /// <param name="Name"></param>
    /// <param name="ShortName"></param>
    /// <param name="AreaCode"></param>
    /// <param name="ZipCode"></param>
    /// <param name="CityCode"></param>
    /// <param name="Regional"></param>
    /// <param name="Lng"></param>
    /// <param name="Lat"></param>
    /// <param name="ProvinceId"></param>
    /// <param name="CityId"></param>
    /// <param name="AreaId"></param>
    /// <param name="CountryId"></param>
    /// <param name="OtherName"></param>
    /// <param name="AliasName"></param>
    /// <param name="WCityId">天气城市Id</param>
    /// <param name="Sort">排序</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("新增区域接口")]
    public IActionResult AddRegions(string Name, string ShortName, string AreaCode, string ZipCode, string CityCode, string Regional, int Lng, int Lat, int ProvinceId, int CityId, int AreaId, string CountryId, String OtherName, String AliasName, Int32 WCityId, Int32 Sort)
    {
        var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        if (Name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
        }
        if (ShortName.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("简称不能为空") });
        }
        if (AreaCode.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("行政代码不能为空") });
        }

        var ECIT = Regions.FindByAreaCode(AreaCode.ToString());
        if (ECIT != null)
        {
            return Prompt(new PromptModel { Message = GetResource("行政代码已存在") });
        }
        string cId = "";
        using (var tran1 = Regions.Meta.CreateTrans())
        {
            var Model = new Regions
            {
                Name = Name,
                ShortName = ShortName,
                OtherName = OtherName,
                AliasName = AliasName,
                AreaCode = AreaCode.ToString(),
                ZipCode = ZipCode,
                CityCode = CityCode,
                Regional = Regional,
                Lng = Lng,
                Lat = Lat
            };
            Model.PinYin = PinYin.Get(Model.ShortName);
            //Model.CityId = WCityId;
            Model.Sort = Sort;

            var ParentId = 0;

            if (AreaId > 0)         //获取选择的最后一个级别
            {
                ParentId = AreaId;
            }
            else
            {
                if (CityId > 0)
                {
                    ParentId = CityId;
                }
                else if (ProvinceId > 0)
                {
                    ParentId = ProvinceId;
                }
            }

            var localizationSettings = LocalizationSettings.Current;

            var PMODEL = Regions.FindById(ParentId);
            if (PMODEL == null)
            {
                Model.CId = "CN";//默认中国
                Model.Level = 0;
                Model.MergerName = Model.ShortName;

                if (localizationSettings.IsEnable)
                {
                    if (CountryId.IsNullOrWhiteSpace())
                    {
                        Model.CId = "CN";//默认中国
                    }
                    else
                    {
                        var Cmodel = Country.FindByTwoLetterIsoCode(CountryId);

                        if (Cmodel == null)
                        {
                            Model.CId = "CN";//默认中国
                        }
                        else
                        {
                            Model.CId = Cmodel.TwoLetterIsoCode;//默认中国
                        }
                    }
                }
            }
            else
            {
                Model.ParentCode = PMODEL.AreaCode;
                Model.Level = PMODEL.Level + 1;
                Model.MergerName = PMODEL.MergerName + "," + Model.ShortName;
                Model.CId = PMODEL.CId;//有父级直接使用父级别的国家
            }

            Model.Insert();
            cId = Model.CId;

            if (localizationSettings.IsEnable)
            {
                var list = new List<RegionsLan>();
                foreach (var item in Languagelist)
                {
                    var aaaa = new RegionsLan
                    {
                        Name = GetRequest($"[{item.Id}].Name").SafeString().Trim(),
                        ShortName = GetRequest($"[{item.Id}].ShortName").SafeString().Trim(),
                        OtherName = GetRequest($"[{item.Id}].OtherName").SafeString().Trim(),
                        AliasName = GetRequest($"[{item.Id}].AliasName").SafeString().Trim(),
                        RId = Model.Id,
                        LId = item.Id
                    };
                    if (PMODEL == null)
                    {
                        aaaa.MergerName = aaaa.ShortName;
                    }
                    else
                    {
                        var Pmodel = RegionsLan.FindByRIdAndLId(PMODEL.Id, item.Id);
                        if (Pmodel != null)
                        {
                            aaaa.MergerName = Pmodel.MergerName + "," + aaaa.ShortName;
                        }
                        else
                        {
                            aaaa.MergerName = aaaa.ShortName;
                        }
                    }
                    list.Add(aaaa);
                }
                list.Save();
            }
            tran1.Commit();
        }
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Province", new { id = cId }) });
    }

    /// <summary>
    /// 修改列表字段值
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改列表字段值")]
    public IActionResult UpdateRegions(int Id)
    {
        dynamic viewModel = new ExpandoObject();

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
        {
            ViewBag.CountryList = Country.FindAllWithCache().OrderBy(e => e.Id).Select(e => new SelectListItem { Value = e.Id.ToString(), Text = CountryLan.FindNameByCIdAndLId(e.Id, _workContext.WorkingLanguage.Id) });
        }
        ViewBag.LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);

        var Model = Regions.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }
        var country = Country.FindByTwoLetterIsoCode(Model.CId);
        if (country == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }
        country.Name = CountryLan.FindNameByCIdAndLId(country.Id, _workContext.WorkingLanguage.Id);
        var city = Regions.FindByAreaCode(Model.ParentCode);
        ViewBag.Country = country;
        if (city != null)
        {
            ViewBag.City = city;
            ViewBag.Province = city.ParentRegions;
        }
        return View(Model);
    }

    /// <summary>
    /// 编辑区域接口
    /// </summary>
    /// <param name="Name"></param>
    /// <param name="ShortName"></param>
    /// <param name="AreaCode"></param>
    /// <param name="ZipCode"></param>
    /// <param name="CityCode"></param>
    /// <param name="Regional"></param>
    /// <param name="Lng"></param>
    /// <param name="Lat"></param>
    /// <param name="ProvinceId"></param>
    /// <param name="CityId"></param>
    /// <param name="AreaId"></param>
    /// <param name="CountryId"></param>
    /// <param name="Id"></param>
    /// <param name="OtherName"></param>
    /// <param name="AliasName"></param>
    /// <param name="WCityId">天气城市Id</param>
    /// <param name="Sort">排序</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("编辑区域接口")]
    public IActionResult UpdateRegions(string Name, string ShortName, string AreaCode, string ZipCode, string CityCode, string Regional, int Lng, int Lat, int ProvinceId, int CityId, int AreaId, int CountryId, int Id, String OtherName, String AliasName, Int32 WCityId, Int32 Sort)
    {
        var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        if (Name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
        }
        if (ShortName.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("简称不能为空") });
        }
        if (AreaCode.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("行政代码不能为空") });
        }
        var ECIT = Regions.FindByAreaCode(AreaCode.ToString());
        if (ECIT != null && ECIT.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("行政代码已存在") });
        }
        var Model = Regions.FindById(Id);
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("区域不存在") });
        }

        using (var tran1 = Regions.Meta.CreateTrans())
        {
            Model.Name = Name;
            Model.ShortName = ShortName;
            Model.OtherName = OtherName;
            Model.AliasName = AliasName;
            Model.AreaCode = AreaCode.ToString();
            Model.ZipCode = ZipCode;
            Model.CityCode = CityCode;
            Model.Regional = Regional;
            Model.Lng = Lng;
            Model.Lat = Lat;
            Model.PinYin = PinYin.Get(Model.ShortName);
            Model.MergerName = ShortName;
            Regions regions = null;
            if (Model.Level==2)
            {
                regions = Regions.FindByAreaCode(Model.ParentCode)??new Regions();
              
                Model.MergerName = regions?.MergerName + "," + Model.ShortName;
            }

            //Model.CityId = WCityId;
            Model.Sort = Sort;

           
            var localizationSettings = LocalizationSettings.Current;


            Model.Update();
            if (localizationSettings.IsEnable)
            {
                var list = new List<RegionsLan>();
                foreach (var item in Languagelist)
                {
                    var aaaa = RegionsLan.FindByRIdAndLId(Model.Id, item.Id);
                    aaaa ??= new RegionsLan();
                    aaaa.Name = GetRequest($"[{item.Id}].Name").SafeString().Trim();
                    aaaa.ShortName = GetRequest($"[{item.Id}].ShortName").SafeString().Trim();
                    aaaa.OtherName = GetRequest($"[{item.Id}].OtherName").SafeString().Trim();
                    aaaa.AliasName = GetRequest($"[{item.Id}].AliasName").SafeString().Trim();
                    aaaa.RId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.MergerName = aaaa.ShortName;
                    if (regions!=null)
                    {
                        aaaa.MergerName = regions.MergerName + "," + aaaa.ShortName;
                    }
                    aaaa.MergerName = aaaa.ShortName;
                    list.Add(aaaa);
                }
                list.Save();
            }
            tran1.Commit();
        }
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Province", new { id = Model.CId }) });
    }

    /// <summary>
    /// 新增国家页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增国家页面")]
    public IActionResult AddCountry()
    {
        var OrderMax = Country.FindMax("DisplayOrder");
        ViewBag.Maxsort = OrderMax + 1;
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View();
    }

    /// <summary>
    /// 新增国家接口
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("新增国家接口")]
    public IActionResult AddCountry(string area_name, string TwoLetterIsoCode, string ThreeLetterIsoCode, int NumericIsoCode, int IsEnabled, int IsDefaultm, int area_sort)
    {
        if (area_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
        }
        if (TwoLetterIsoCode.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("两个字母ISO代码不能为空") });
        }
        var ECIT = Country.FindByNumericIsoCode(NumericIsoCode);
        if (ECIT != null)
        {
            return Prompt(new PromptModel { Message = GetResource("ISO数字代码已存在") });
        }
        var ECIT3 = Country.FindByThreeLetterIsoCode(ThreeLetterIsoCode);
        if (ECIT3 != null)
        {
            return Prompt(new PromptModel { Message = GetResource("三个字母ISO代码已存在") });
        }
        var ECIT2 = Country.FindByTwoLetterIsoCode(TwoLetterIsoCode);
        if (ECIT2 != null)
        {
            return Prompt(new PromptModel { Message = GetResource("两个字母ISO代码已存在") });
        }
        var ECIT4 = Country.FindByName(area_name);
        if (ECIT4 != null)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }
        var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        using (var tran1 = Regions.Meta.CreateTrans())
        {
            var Model = new Country
            {
                Name = area_name,
                TwoLetterIsoCode = TwoLetterIsoCode,
                ThreeLetterIsoCode = ThreeLetterIsoCode,
                NumericIsoCode = NumericIsoCode,
                DisplayOrder = area_sort,
                IsDefault = IsDefaultm == 1
            };
            if (IsDefaultm == 1)
            {
                var CountryModel = Country.FindByDefault();
                if (CountryModel != null)
                {
                    CountryModel.IsDefault = false;
                    CountryModel.Update();
                }
            }
            Model.IsEnabled = IsEnabled == 1;
            Model.Insert();

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var list = new List<CountryLan>();
                foreach (var item in Languagelist)
                {
                    var aaaa = new CountryLan
                    {
                        Name = GetRequest($"[{item.Id}].area_name").SafeString().Trim(),
                        CId = Model.Id,
                        LId = item.Id
                    };
                    list.Add(aaaa);
                }
                list.Save();
            }
            tran1.Commit();
        }
        return MessageTip(GetResource("保存成功"));
    }

    /// <summary>
    /// 编辑国家页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑国家页面")]
    public IActionResult UpdateCountry(string Id)
    {
        var Model = Country.FindByTwoLetterIsoCode(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(Model);
    }

    /// <summary>
    /// 编辑国家接口
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("编辑国家页面")]
    public IActionResult UpdateCountry(string Id, string area_name, string TwoLetterIsoCode, string ThreeLetterIsoCode, int NumericIsoCode, int IsEnabled, int IsDefaultm, int area_sort)
    {

        var Model = Country.FindByTwoLetterIsoCode(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }

        if (area_name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
        }
        var ECIT = Country.FindByNumericIsoCode(NumericIsoCode);
        if (ECIT != null && ECIT.Id != Model.Id)
        {
            return Prompt(new PromptModel { Message = GetResource("ISO数字代码已存在") });
        }
        var ECIT3 = Country.FindByThreeLetterIsoCode(ThreeLetterIsoCode);
        if (ECIT3 != null && ECIT3.Id != Model.Id)
        {
            return Prompt(new PromptModel { Message = GetResource("三个字母ISO代码已存在") });
        }
        var ECIT2 = Country.FindByTwoLetterIsoCode(TwoLetterIsoCode);
        if (ECIT2 != null && ECIT2.Id != Model.Id)
        {
            return Prompt(new PromptModel { Message = GetResource("两个字母ISO代码已存在") });
        }
        var ECIT4 = Country.FindByName(area_name);
        if (ECIT4 != null && ECIT4.Id != Model.Id)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }

        var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        using (var tran1 = Regions.Meta.CreateTrans())
        {
            if (IsDefaultm == 1)
            {
                var CountryModel = Country.FindByDefault();
                if (CountryModel != null && CountryModel.Id != Model.Id)
                {
                    CountryModel.IsDefault = false;
                    CountryModel.Update();
                }
            }
            Model.Name = area_name;
            Model.TwoLetterIsoCode = TwoLetterIsoCode;
            Model.ThreeLetterIsoCode = ThreeLetterIsoCode;
            Model.NumericIsoCode = NumericIsoCode;
            Model.DisplayOrder = area_sort;
            Model.IsDefault = IsDefaultm == 1;
            Model.IsEnabled = IsEnabled == 1;
            Model.Update();

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var list = new List<CountryLan>();
                foreach (var item in Languagelist)
                {
                    var aaaa = CountryLan.FindByCIdAndLId(Model.Id, item.Id);
                    aaaa ??= new CountryLan();
                    aaaa.Name = GetRequest($"[{item.Id}].area_name").SafeString().Trim();
                    aaaa.CId = Model.Id;
                    aaaa.LId = item.Id;
                    list.Add(aaaa);
                }
                list.Save();
            }
            tran1.Commit();
        }
        return MessageTip(GetResource("保存成功"));
    }

    /// <summary>
    /// 区域数据批量删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("区域数据批量删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        var list = Regions.FindByIds(Ids);

        var dellist = new List<Regions>();
        using (var tran1 = Regions.Meta.CreateTrans())
        {
            res = DeleteRegions(res, dellist, list);
            if (dellist.Delete(true) > 0)
                Regions.Meta.Cache.Clear("");
            RegionsLan.DelByRIds(Ids);
            tran1.Commit();
        }
        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 循环删除多级数据
    /// </summary>
    /// <param name="res"></param>
    /// <param name="dellist"></param>
    /// <param name="list"></param>
    /// <returns></returns>
    private static DResult DeleteRegions(DResult res, IList<Regions> dellist, IList<Regions> list)
    {
        if (list.Count > 0)
        {
            foreach (var item in list)
            {
                dellist.Add(item);
                var childlist = Regions.FindAllByParentCode(item.AreaCode);
                res = DeleteRegions(res, dellist, childlist);
            }
        }
        return res;
    }

    /// <summary>
    /// 国家数据批量删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("国家数据批量删除")]
    public IActionResult Deletes(string Ids)
    {
        var res = new DResult();
        var list = Country.FindAllByTwoLetterIsoCodes(Ids);
        var dellist = new List<Regions>();

        using (var tran1 = Regions.Meta.CreateTrans())
        {
            res = DeleteCountry(res, dellist, list);

            foreach (var item in dellist)
            {
                var Model = RegionsLan.FindByRIds(item.Id.ToString());
                Model.Delete();//删除区域翻译数据
            }

            if (dellist.Delete(true) > 0)
                Regions.Meta.Cache.Clear("");
            if (list.Delete(true) > 0)
                Country.Meta.Cache.Clear("");

            CountryLan.DelByCIds(Ids);//删除国家翻译数据
            tran1.Commit();
        }
        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 循环删除多级数据
    /// </summary>
    /// <param name="res"></param>
    /// <param name="dellist"></param>
    /// <param name="Countr"></param>
    /// <returns></returns>
    private static DResult DeleteCountry(DResult res, List<Regions> dellist, IList<Country> Countr)
    {
        if (Countr.Count > 0)
        {
            foreach (var item in Countr)
            {
                var zList = Regions.FindAllByCId(item.TwoLetterIsoCode);

                if (zList != null)
                {
                    foreach (var ROW in zList)
                    {
                        dellist.Add(ROW);
                    }
                }
            }
        }
        return res;
    }

    /// <summary>
    /// 根据省获取市
    /// </summary>
    /// <param name="Code"></param>
    /// <returns></returns>
    [DisplayName("根据省获取市")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetbyPId(Int64 Code)
    {
        var list = Regions.FindAllByParentCode(Code.ToString()).OrderBy(x => x.Id);
        return Json(new { Data = list });
    }

    /// <summary>
    /// 根据国家获取省
    /// </summary>
    /// <param name="CId"></param>
    /// <returns></returns>
    [DisplayName("根据国家获取省")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetbyCId(string CId)
    {
        var list = Regions.FindAllByCIdAndLevel(CId, 0).OrderBy(x => x.Id);
        return Json(new { Data = list });
    }
}
