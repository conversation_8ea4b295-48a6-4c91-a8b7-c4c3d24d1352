﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("满即送")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("活动列表")</span></a></li>
                <li><a href="@Url.Action("FullGiftPackage")" class="current"><span>@T("套餐管理")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("FullGiftSetting")', '@T("设置")')"><span>@T("设置")</span></a></li>
            </ul>
        </div>
    </div>

    <form method="get" name="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("店铺名称")</dt>
                <dd>
                    <select name="storeId" id="storeId">
                        <option value="">@T("请选择")</option>
                        @foreach (Store store in Model.storelist)
                        {
                            <!option value="@store.Id" @(Model.storeId == store.Id ? "selected" : "")>@store.Name</!option>
                        }
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <a href="javascript:document.formSearch.submit();" class="btn " title="@T("查询")">@T("查询")</a>
            </div>
        </div>
    </form>
    <!-- 帮助 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="提示相关设置操作时应注意的要点">@T("操作提示")</h4>
            <span id="explanationZoom" title="收起提示" class="arrow"></span>
        </div>
        <ul>
            <li>@T("卖家的满即送套餐列表")</li>
        </ul>
    </div>

    <!-- 列表 -->
    <form id="list_form" method="post">
        <table class="ds-default-table">
            <thead>
                <tr class="thead">
                    <th class="w24"></th>
                    <th class="align-left"><span>@T("店铺名称")</span></th>
                    <th class="align-center"><span>@T("开始时间")</span></th>
                    <th class="align-center"><span>@T("结束时间")</span></th>
                </tr>
            </thead>
            <tbody id="treet1">
                @foreach (FullGiftQuota item in Model.list)
                {
                    <tr class="hover">
                        <td></td>
                        <td class="align-left"><a href="@Url.Action("ProviderDetail", "Supplier", new { Area = "", sid = item.StoreId })"><span>@item.StoreName</span></a></td>
                        <td class="align-center"><span>@(UnixTime.ToDateTime(item.StartTime).ToDateString())</span></td>
                        <td class="align-center"><span>@(UnixTime.ToDateTime(item.EndTime).ToDateString())</span></td>
                    </tr>
                }
            </tbody>
        </table>
        <ul class="pagination">
            @Html.Raw(Model.PageHtml)
        </ul>
    </form>
</div>
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>
<script type="text/javascript">
    $(function() {
        $('#storeId').select2({
            placeholder: '@T("请选择店铺")',
            allowClear: true,
        });
    });
</script>