﻿@using B2B2CShop.Dto
@using B2B2CShop.Entity
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<input type="hidden" name="activity_id" value="3" />
<ul class="goods-list" style="width:760px;">
    @foreach (GoodsDto item in Model.list)
    {
        <li>
            <div class="goods-thumb"><img src="@item.GoodsImage" /></div>
            <dl class="goods-info">
                <dt>
                    <input type="checkbox" value="@item.SkuId" class="vm" name="item_id[]" />&nbsp;
                    <label><a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })"
                            target="_blank">@item.Name</a></label>
                </dt>
                <dd>@T("销售价")：@item.GoodsPrice</dd>
            </dl>
        </li>
    }
</ul>
<ul class="pagination">
    @Html.Raw(Model.PageHtml)
</ul>
<div class="bottom tc p10">
    <input id="submit_button" type="submit" value="@T("选择完毕,参与活动")" class="submit">
</div>
<script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
<script>
    $(document).ready(function () {
        var defaultAlbumId = $('#jumpMenu option:selected').val();
        if (defaultAlbumId != '0') {
            // loadAlbumPics(defaultAlbumId);
        }
        function updatePagination(currentPage, totalPages, aId) {
            var $pagination = $('.pagination');
            var $pageLinks = $pagination.find('li:not(:first):not(:last)');
            // 清空现有页码链接
            $pageLinks.empty();
            console.log("添加首页");
            // 添加首页
            $pageLinks.first().html(`<a href="javascript:void(0);" data-page="1">首页</a>`);

            // 添加上一页
            if (currentPage > 1) {
                $pageLinks.eq(1).html(`<a href="javascript:void(0);" data-page="${currentPage - 1}">«</a>`);
            } else {
                $pageLinks.eq(1).html(`<span>«</span>`);
            }

            // 添加页码
            for (let i = 1; i <= totalPages && i <= 5; i++) {
                let isActive = i === currentPage;
                let pageHtml = isActive ?
                    `<span>${i}</span>` :
                    `<a href="javascript:void(0);" data-page="${i}">${i}</a>`;
                $pageLinks.eq(i + 1).html(pageHtml);
            }

            // 添加下一页
            if (currentPage < totalPages) {
                $pageLinks.eq(-2).html(`<a href="javascript:void(0);" data-page="${currentPage + 1}">»</a>`);
            } else {
                $pageLinks.eq(-2).html(`<span>»</span>`);
            }

            // 添加尾页
            $pageLinks.last().html(`<a href="javascript:void(0);" data-page="${totalPages}">尾页</a>`);

            // 更新当前页输入框
            $('input[name="page"]').val(currentPage);
        }
        // 分类切换事件
        $('#jumpMenu').change(function () {
            var aId = $(this).val();
            console.log("aId:" + aId);
            if (aId != '0') {
                // loadAlbumPics(aId);
            }
        });

        // 分页点击事件
        $(document).on('click', 'ul.pagination>li>a', function (e) {
            e.preventDefault();
            var aId = $('#jumpMenu').val();
            if (aId != '0') {
                var page = $(this).data('page') || 1;
                // loadAlbumPics(aId, page);
            }
        });
        // 跳转页面事件
        $('#pagination_gourl').click(function (e) {
            e.preventDefault();
            var aId = $('#jumpMenu').val();
            if (aId != '0') {
                var page = $('input[name="page"]').val();
                // loadAlbumPics(aId, page);
            }
        });
    });
    $(document).ready(function () {
        $('ul.pagination>li>a').ajaxContent({
            event: 'click', //mouseover
            loaderType: 'img',
            loadingMsg: '/static/home/<USER>/loading.gif',
            target: 'div[dstype="div_goods_search_result"]'
        });
        $('#jumpMenu').change(function () {
            $('#select_submit').attr('href', $('#select_submit').attr('href') + "&id=" + $('#jumpMenu').val());
            $('#select_submit').ajaxContent({
                event: 'click', //mouseover
                loaderType: 'img',
                loadingMsg: '/static/home/<USER>/loading.gif',
                target: 'div[dstype="div_goods_search_result"]'
            });
            $('#select_submit').click();
        });
    });
</script>