﻿using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    /// <summary>店铺等级管理</summary>
    [DisplayName("店铺等级管理")]
    [Description("用于店铺等级管理")]
    [AdminArea]
    [DHMenu(70, ParentMenuName = "Stores", ParentMenuDisplayName = "店铺", ParentMenuUrl = "~/{area}/Store", ParentMenuOrder = 80, CurrentMenuUrl = "~/{area}/StoreGrade", CurrentMenuName = "StoreGradeList", CurrentIcon = "&#xe6a3;", LastUpdate = "20241203")]
    public class StoreGradeController : PekCubeAdminControllerX
    {
        /// <summary>
        /// 店铺等级列表
        /// </summary>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("店铺等级列表")]
        public IActionResult Index(string name, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Sort",
                Desc = false
            };

            name = name.SafeString().Trim();

            viewModel.list = StoreGrade.FindByName(name, pages);
            viewModel.page = page;
            viewModel.name = name;

            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> { { "name", name } });
            return View(viewModel);
        }

        /// <summary>
        /// 新增店铺等级
        /// </summary>
        /// <returns></returns>
        [DisplayName("新增店铺等级")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add()
        {
            return View();
        }

        /// <summary>添加店铺等级</summary>
        /// <returns></returns>
        [DisplayName("添加店铺等级")]
        [HttpPost]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add(string Name, int GoodsLimit, int AlbumLimit, int Price, string Description, int Sort)
        {
            if (Name.IsNullOrWhiteSpace())
            {
                return Json(new DResult { msg = GetResource("等级名称不能为空"), success = false, });
            }

            if (GoodsLimit < 0)
            {
                return Json(new DResult { msg = GetResource("商品数量不能小于零"), success = false, });
            }

            if (AlbumLimit < 0)
            {
                return Json(new DResult { msg = GetResource("相册数量不能小于零"), success = false, });
            }

            if (Price < 0)
            {
                return Json(new DResult { msg = GetResource("价格不能小于零"), success = false, });
            }

            if (Sort<0)
            {
                return Json(new DResult { msg = GetResource("等级级别不能小于零"), success = false, });
            }

            var model = StoreGrade.FindByName(Name);
            if (model != null)
            {
                return Json(new DResult { msg = GetResource("等级名称不能重复"), success = false, });
            }

            model = new StoreGrade();
            model.Name = Name;
            model.GoodsLimit = GoodsLimit;
            model.AlbumLimit = AlbumLimit;
            model.SpaceLimit = AlbumLimit;//MB
            model.Price = Price;
            model.Description = Description;
            model.Sort = Sort;

            model.Insert();

            return Json(new DResult { msg = GetResource("新增成功"), success = true, });
        }

        /// <summary>
        /// 编辑店铺等级
        /// </summary>
        /// <returns></returns>
        [DisplayName("编辑店铺等级")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Edit(int id)
        {
            var model = StoreGrade.FindById(id);
            if (model == null)
            {
                return Content(GetResource("店铺等级不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑店铺等级
        /// </summary>
        /// <returns></returns>
        [DisplayName("编辑店铺等级")]
        [EntityAuthorize(PermissionFlags.Update)]
        [HttpPost]
        public IActionResult Edit(int Id, string Name, int GoodsLimit, int AlbumLimit, int Price, string Description, int Sort)
        {
            if (Name.IsNullOrWhiteSpace())
            {
                return Json(new DResult { msg = GetResource("等级名称不能为空"), success = false, });
            }

            if (GoodsLimit < 0)
            {
                return Json(new DResult { msg = GetResource("商品数量不能小于零"), success = false, });
            }

            if (AlbumLimit < 0)
            {
                return Json(new DResult { msg = GetResource("相册数量不能小于零"), success = false, });
            }

            if (Price < 0)
            {
                return Json(new DResult { msg = GetResource("价格不能小于零"), success = false, });
            }

            if (Sort < 0)
            {
                return Json(new DResult { msg = GetResource("等级级别不能小于零"), success = false, });
            }

            var model = StoreGrade.FindByName(Name);
            if (model != null && model.Id != Id)
            {
                return Json(new DResult { msg = GetResource(model.Id+"等级名称不能重复"+ Id), success = false, });
            }

            model = StoreGrade.FindById(Id);
            if (model == null)
            {
                return Json(new DResult { msg = GetResource("等级名称不存在"), success = false, });
            }

            model.Name = Name;
            model.GoodsLimit = GoodsLimit;
            model.AlbumLimit = AlbumLimit;
            model.SpaceLimit = AlbumLimit;//MB
            model.Price = Price;
            model.Description = Description;
            model.Sort = Sort;
            model.Update();

            return Json(new DResult { msg = GetResource("编辑成功"), success = true, });
        }

        /// <summary>
        /// 店铺数据删除
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("店铺数据删除")]
        public IActionResult Delete(string Ids)
        {
            var res = new DResult();
            StoreClass.DelByIds(Ids);
            res.success = true;
            res.code = 10000;
            res.msg = GetResource("删除成功");
            return Json(res);
        }
    }
}
