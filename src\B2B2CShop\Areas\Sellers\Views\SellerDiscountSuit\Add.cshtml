@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "DiscountSuit";
    PekHtml.AppendPageCssClassParts("html-sellerdiscountsuit-page");
    PekHtml.AppendTitleParts(T("添加活动").Text);
    var localizationSettings = LocalizationSettings.Current;
    var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
}
@await Html.PartialAsync("_Left")
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li><a href="@Url.Action("Index")">@T("活动列表")</a></li>
                <li class="current"><a href="@Url.Action("Add")">@T("添加活动")</a></li>
            </ul>

        </div>
        <div class="p20">

            <div class="alert"> <strong>@T("说明：")</strong>
                <ul>
                    <li>@T("您只能发布")<strong>@Model.releaseLimit</strong>@T("个优惠套装活动；每个活动最多可以添加")<strong>@Model.comboLimit</strong>@T("个商品。")</li>
                    <li>@T("凡选择指定优惠的商品，在这个商品的详细页将出现发布的优惠套装。")</li>
                    <li>@T("特殊商品不能参加该活动。")</li>
                </ul>
            </div>
            <div class="dssc-form-default">
                <!-- 说明 -->

                <form id="add_form" method="post" action="@Url.Action("Add")">
                    <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                        @if (localizationSettings.IsEnable)
                        {
                            <ul class="layui-tab-title">
                                <li data="" class="layui-this">@T("标准") </li>
                                @foreach (var item in LanguageList)
                                {
                                    <li data="@item.Id" class="LId">@item.DisplayName</li>
                                }
                            </ul>
                        }
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <dl>
                                    <dt><i class="required">*</i>@T("活动名称：")</dt>
                                    <dd>
                                        <p>
                                            <input id="name" name="name" type="text" maxlength="25" class="w400 text"
                                                value="" />
                                            <span></span>
                                        </p>
                                        <p class="hint">@T("活动名称最多为25个字符")</p>
                                    </dd>
                                </dl>
                            </div>
                            @if (localizationSettings.IsEnable)
                            {
                                @foreach (var item in LanguageList)
                                {
                                    <div class="layui-tab-item ">
                                        <dl>
                                            <dt>@T("活动名称：")</dt>
                                            <dd>
                                                <p>
                                                    <input id="<EMAIL>" name="[@item.Id].name" type="text" maxlength="25"
                                                        class="w400 text" value="" />
                                                    <span></span>
                                                </p>
                                                <p class="hint">@T("活动名称最多为25个字符")</p>
                                            </dd>
                                        </dl>
                                    </div>
                                }
                            }
                        </div>
                    </div>

                    <dl>
                        <dt><i class="required">*</i>@T("优惠套装价格：")</dt>
                        <dd>
                            <input id="discountprice" name="discountprice" type="text" readonly
                                style="background:#E7E7E7 none;" class="text w60 mr5" value="" />
                            @T("元") <span></span>
                            <p class="hint mt10">@T("原价")<span dstype="storecost_price"
                                    class="price mr5 ml5"></span>@T("元")&nbsp;(@T("已添加搭配商品的默认价格总计"))</p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("商品：")</dt>
                        <dd>
                            <p class="bundling_goods_box">
                                <input id="bundling_goods" type="text" value="" name="bundling_goods">
                                <span></span>
                            </p>
                            <table class="dssc-default-table mb15">
                                <thead>
                                    <tr>
                                        <th class="w70">@T("指定优惠")</th>
                                        <th class="tl" colspan="2">@T("商品名称")</th>
                                        <th class="w90">@T("原价")</th>
                                        <th class="w90">@T("优惠价格")</th>
                                        <th class="w90">@T("操作")</th>
                                    </tr>
                                </thead>
                                <tbody dstype="bundling_data" class="bd-line tip"
                                    title='@T("搭配销售的商品可上下<br/>拖移商品列可自定义显示排序；编辑、删除、排序等操作提交后生效。")'>
                                    <tr class="norecord" style="display:none;">
                                        <td colspan="20" class="norecord">
                                            <div class="no-promotion"><i class="zh"></i><span>@T("优惠套装还未选择添加商品。")</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <a id="bundling_add_goods" href="javascript:void(0);"
                                class="dssc-btn dssc-btn-acidblue">@T("添加商品")</a>
                            <div class="div-goods-select mt10">
                                <div id="bundling_add_goods_search" style="display: none;">
                                    <table class="search-form">
                                        <tr>
                                            <td>&nbsp;</td>
                                            @* <th>@T("店铺分类")</th>
                                            <td class="w100">
                                                <select id="search_goods_class" name="goods_class" class="w150">
                                                    <option value='all'>@T("--请选择--")</option>
                                                    <option value='0'>111</option>
                                                    <option value='1'>222</option>
                                                </select>
                                            </td> *@
                                            <th class="w110">@T("商品名称")</th>
                                            <td class="w160"><input type="text" id="search_goods_name" class="text w150"
                                                    name="goods_name" value="" /></td>
                                            <td class="w70 tc">
                                                <a href="javascript:void(0);" id="btn_search_goods"
                                                    class="dssc-btn" /><i class="iconfont">&#xe718;</i>@T("搜索")</a>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div id="div_goods_search_result" class="search-result" style="width:739px;"></div>
                                <a id="bundling_add_goods_delete" class="close" href="javascript:void(0);"
                                    style="right: -10px;">X</a>
                            </div>
                        </dd>
                    </dl>
                    <dl>
                        <dt>@T("运费承担：")</dt>
                        <dd>
                            <ul class="dssc-form-radio-list">
                                <li>
                                    <label for="whops_seller">
                                        <input id="whops_seller" type="radio" name="reightchoose" checked="checked"
                                            value="1" />@T("卖家承担运费")
                                    </label>
                                </li>
                                <li>
                                    <label for="whops_buyer">
                                        <input id="whops_buyer" type="radio" name="reightchoose"
                                            value="0" />@T("买家承担运费（快递）")
                                    </label>
                                    <div id="whops_buyer_box" class="transport_tpl" style="display:none;">
                                        <input class="w50 text" type="text" name="freight" value="" /><em
                                            class="add-on"><i class="iconfont">&#xe65c;</i></em>
                                    </div>
                                </li>
                            </ul>
                        </dd>
                    </dl>
                    <dl>
                        <dt>@T("活动状态：")</dt>
                        <dd>
                            <ul class="dssc-form-radio-list">
                                <li>
                                    <label for="bundling_status_1">
                                        <input type="radio" name="state" value="1" id="bundling_status_1"
                                            checked="checked" />
                                        @T("开启")
                                    </label>
                                </li>
                                <li>
                                    <label for="bundling_status_0">
                                        <input type="radio" name="state" value="0" id="bundling_status_0" /> @T("关闭")
                                    </label>
                                </li>
                            </ul>
                        </dd>
                    </dl>
                    <div class="bottom">
                        <input id="submit_button" type="submit" value='@T("提交")' class="submit">
                    </div>
                </form>
            </div>
            <script type="text/javascript">
                layui.use('element', function () {
                    var element = layui.element;
                })
            </script>
            <script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
            <script>
                jQuery.browser = {}; (function () { jQuery.browser.msie = false; jQuery.browser.version = 0; if (navigator.userAgent.match(/MSIE ([0-9]+)./)) { jQuery.browser.msie = true; jQuery.browser.version = RegExp.$1; } })();
            </script>
            <script>

                $(document).ready(function () {
                    $("#bundling_add_goods_search").hide();

                    $('#bundling_add_goods').on('click', function (e) {
                        e.preventDefault();
                        $("#bundling_add_goods_search").show();
                        $("#bundling_add_goods_delete").show();
                        generateSearchGoodsListView();
                    });

                    $(document).on('click', '.delete', function () {
                        // 获取要删除的商品名称
                        var goodsName = $(this).closest('tr.bundling_goods_list').find('td:nth-child(2)').text().trim();

                        // 删除表格行
                        $(this).closest('tr.bundling_goods_list').remove();

                        // 检查是否需要显示"无记录"提示
                        const goodsCount = $('tbody[dstype="bundling_data"] .bundling_goods_list').length;
                        if (goodsCount == 0) {
                            $('tbody[dstype="bundling_data"] .norecord').show();
                        }

                        // 更新搜索结果中对应商品的按钮状态
                        $('#div_goods_search_result .goods-list li').each(function () {
                            var liGoodsName = $(this).find('.goods-info dt').text().trim();
                            var btnElement = $(this).find('a[dstype="btn_add_xianshi_goods"]');

                            if (goodsName === liGoodsName) {
                                btnElement.removeClass('dssc-btn-orange').addClass('dssc-btn-green');
                                btnElement.find('.btn-text').text('@T("添加到优惠套装")');
                                btnElement.attr('data-selected', 'false');
                            }
                        });
                    });

                    //点击搜索
                    $('#btn_search_goods').on('click', function (e) {
                        console.log('e => ', e)
                        e.preventDefault();
                        var goodsName = $("#search_goods_name").val();
                        var goodsClass = $("#search_goods_class").val();
                        generateSearchGoodsListView(goodsName, goodsClass)
                    })

                    // 生成SearchGoodsList视图
                    function generateSearchGoodsListView(goodsName = '', goodsClass = '') {
                        $.ajax({
                            url: '@Url.Action("SearchGoodsList")',
                            type: 'POST',
                            data: {
                                keyword: goodsName,
                                goodsClass
                            },
                            success: function (result) {
                                // 直接将返回的HTML内容显示在搜索结果区域
                                $("#div_goods_search_result").html(result);

                                // 注意：商品选择按钮的点击事件现在直接在按钮上通过 onclick 属性绑定
                                // 这里不再需要重复绑定事件
                            },
                            error: function () {
                                $("#div_goods_search_result").html('<div class="error-result">搜索出错，请重试</div>');
                            }
                        });
                    }

                    $('#bundling_add_goods_delete').click(function () {
                        $(this).hide();
                        $("#bundling_add_goods_search").hide();
                        $('#div_goods_search_result').html('');
                        $('#bundling_add_goods').show();
                    });


                    $('#div_goods_search_result').on('click', '.pagination li a', function () {
                        $('#div_goods_search_result').load($(this).attr('href'));
                        return false;
                    });

                    // 退拽效果
                    $('tbody[dstype="bundling_data"]').sortable({ items: 'tr' });
                    $('#goods_images').sortable({ items: 'li' });
                });


                /* 计算商品原价 */
                function count_cost_price_sum() {
                    data_price = $('td[dstype="bundling_data_price"]');
                    if (typeof (data_price) != 'undefined') {
                        var S_price = 0;
                        data_price.each(function () {
                            S_price += parseFloat($(this).html());
                        });
                        $('span[dstype="storecost_price"]').html(S_price.toFixed(2));
                    } else {
                        $('span[dstype="storecost_price"]').html('');
                    }
                }

                /* 计算商品售价 */
                function count_price_sum() {
                    data_price = $('input[dstype="price"]');
                    if (typeof (data_price) != 'undefined') {
                        var S_price = 0;
                        data_price.each(function () {
                            S_price += parseFloat($(this).val());
                        });
                        $('#discountprice').val(S_price.toFixed(2));
                    } else {
                        $('#discountprice').val('');
                    }
                }
            </script>
            <script src="/static/plugins/jquery.poshytip.min.js"></script>
            <style>
                .pic_list .small_pic ul li {
                    height: 100px
                }

                .ui-sortable-helper {
                    border: dashed 1px #F93;
                    box-shadow: 2px 2px 2px rgba(153, 153, 153, .25);
                    filter: alpha(opacity=75);
                    -moz-opacity: .75;
                    opacity: .75;
                    cursor: ns-resize
                }

                .ui-sortable-helper td {
                    background-color: #FFC !important
                }

                .ajaxload {
                    display: block;
                    width: 16px;
                    height: 16px;
                    margin: 100px 300px
                }
            </style>
            <script type="text/javascript">
                $(function () {
                    jQuery.validator.addMethod('bundling_goods', function (value, element) {
                        return $('tbody[dstype="bundling_data"] > tr').length > 2 ? true : false;
                    });
                    //Ajax提示
                    $('.tip').poshytip({
                        className: 'tip-yellowsimple',
                        showTimeout: 1,
                        alignTo: 'target',
                        alignX: 'left',
                        alignY: 'top',
                        offsetX: 5,
                        offsetY: -78,
                        allowTipHover: false
                    });
                    $('.tip2').poshytip({
                        className: 'tip-yellowsimple',
                        showTimeout: 1,
                        alignTo: 'target',
                        alignX: 'right',
                        alignY: 'center',
                        offsetX: 5,
                        offsetY: 0,
                        allowTipHover: false
                    });
                    //页面输入内容验证
                    $("#add_form").validate({
                        errorPlacement: function (error, element) {
                            var error_td = element.nextAll('span:first');
                            error_td.append(error);
                        },
                        submitHandler: function (form) {
                            // 在提交前收集 bundling 列表中的 skuId 和折后价，加入到表单作为 items[index].SkuId / items[index].DiscountPrice
                            // 先移除之前可能存在的临时输入
                            $(form).find('input[name^="items["]').remove();

                            var $rows = $('tbody[dstype="bundling_data"] .bundling_goods_list');
                            if ($rows.length === 0) {
                                layer.msg('@T("请添加商品")', { icon: 2 });
                                return false;
                            }
                            var valid = true;
                            $rows.each(function (i) {
                                var $row = $(this);
                                // 优先从 data-skuid 或隐藏 bundling_skuid[] 中获取 skuId
                                var skuId = $row.data('skuid') || $row.find('input[name="bundling_skuid[]"]').val();
                                var price = $row.find('input[name="discount_price[]"]').val()
                                if (!skuId) {
                                    valid = false;
                                    return false; // break
                                }
                                // 清理价格字符串
                                price = String(price || '').replace(/[^0-9.\-]/g, '');
                                if (price === '') price = '0';

                                // 添加到表单，使用 indexed 名称以便 model binding
                                $('<input>').attr({ type: 'hidden', name: 'items[' + i + '].SkuId', value: skuId }).appendTo(form);
                                $('<input>').attr({ type: 'hidden', name: 'items[' + i + '].DiscountPrice', value: price }).appendTo(form);
                            });

                            if (!valid) {
                                layer.msg('@T("存在未识别的商品，请重试")', { icon: 2 });
                                return false;
                            }

                            // 使用 AJAX 提交，显示后端返回的提示并在成功后跳转回列表页
                            var $form = $(form);
                            var action = $form.attr('action') || window.location.href;
                            // 禁用提交按钮，避免重复提交
                            var $submitBtn = $form.find('#submit_button');
                            $submitBtn.prop('disabled', true);
                            var postData = $form.serialize();
                            $.post(action, postData, function (res) {

                                if (res && res.success) {
                                    layer.msg(res.msg || '@T("保存成功")', { icon: 1 ,time:800}, function () {
                                        var redirect = res.url || '@Url.Action("Index")';
                                        window.location.href = redirect;
                                    });
                                } else {
                                    layer.msg(res.msg || '@T("保存失败")', { icon: 2 });
                                    $submitBtn.prop('disabled', false);
                                }

                            }, 'json').fail(function () {
                                layer.msg('@T("网络错误")', { icon: 2 });
                                $submitBtn.prop('disabled', false);
                            });
                        },
                        rules: {
                            name: {
                                required: true
                            },
                            bundling_goods: {
                                bundling_goods: true
                            },
                            discountprice: {
                                required: true,
                                number: true
                            }
                        },
                        messages: {
                            name: {
                                required: '<i class="iconfont">&#xe64c;</i>请填写活动名称'
                            },
                            bundling_goods: {
                                bundling_goods: '<i class="iconfont">&#xe64c;</i>请选择2件及以上的商品'
                            },
                            discountprice: {
                                required: '<i class="iconfont">&#xe64c;</i>请填写活动价格',
                                number: '<i class="iconfont">&#xe64c;</i>价格只能为数字'
                            }

                        }
                    });

                    $('input[name="reightchoose"]').click(function () {
                        if ($(this).val() == '0') {
                            $('#whops_buyer_box').show();
                        } else {
                            $('#whops_buyer_box').hide();
                        }
                    });

                    check_bundling_data_length();

                    $('tbody[dstype="bundling_data"]').on('change', 'input[dstype="price"]', function () {
                        count_price_sum();
                    });
                });


                /* 删除商品 */
                function bundling_operate_delete(o, id) {
                    // 获取要删除的商品名称
                    var goodsName = o.find('td:nth-child(2)').text().trim();

                    o.remove();
                    check_bundling_data_length();

                    // 更新搜索结果中对应商品的按钮状态
                    $('#div_goods_search_result .goods-list li').each(function () {
                        var liGoodsName = $(this).find('.goods-info dt').text().trim();
                        var btnElement = $(this).find('a[dstype="btn_add_xianshi_goods"]');

                        if (goodsName === liGoodsName) {
                            btnElement.removeClass('dssc-btn-orange').addClass('dssc-btn-green');
                            btnElement.find('.btn-text').text('@T("添加到优惠套装")');
                            btnElement.attr('data-selected', 'false');
                        }
                    });

                    // 兼容旧代码
                    $('li[dstype="' + id + '"]').children(':last').html('<a href="JavaScript:void(0);" onclick="bundling_goods_add($(this))" class="dssc-btn-mini dssc-btn-green"><i class="iconfont">&#xe6db;</i>添加到优惠套装</a>');
                    count_cost_price_sum();
                    count_price_sum();
                }

                function check_bundling_data_length() {
                    if ($('tbody[dstype="bundling_data"] tr').length == 1) {
                        $('tbody[dstype="bundling_data"]').children(':first').show();
                    }
                }
            </script>


        </div>
    </div>
</div>