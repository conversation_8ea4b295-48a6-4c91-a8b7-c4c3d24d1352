﻿@{
}
<style asp-location="true">
    .page {
        min-height: 415px
    }
</style>
<div class="page">
    <form id="AdminUser_form" method="post" action='' name="adminUserForm">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder" style="background: rgb(255, 255, 255);">
                    <td class="required w120">@T("角色")</td>
                    <td class="vatop rowform">
                        <select id="RoleID" name="RoleID">
                            @foreach (var item in Model.Roles)
                            {
                                if (Model.User.RoleID == item.ID)
                                {
                                    <option value="@item.ID" selected>@item.Name</option>
                                }
                                else
                                {
                                    <option value="@item.ID">@item.Name</option>
                                }
                            }
                        </select>
                    </td>

                </tr>
                <tr>
                    <td class="required w120"><label class="validation" for="nav_title">@T("账户")</label></td>
                    <td><input type="text" name="Name" id="Name" value="@Model.User.Name" disabled class="w200"></td>
                    <td></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("密码")</td>
                    <td><input type="password" name="PassWord" id="PassWord" value="" class="w200"></td>
                    <td></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="2"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script asp-location="Footer">
        $(document).ready(function () {
            $('#AdminUser_form').validate({
                errorPlacement: function (error, element) {
                    error.appendTo(element.parent().parent().find('td:last'));
                },
                rules: {
                    Name: {
                        required: true,
                    },
                    // PassWord: {
                    //     required: true,
                    //     minlength: 6,
                    //     maxlength: 20,
                    // }
                },
                messages: {
                    Name: {
                        required: '@T("请填写账户名")',
                    },
                    // PassWord: {
                    //     required: '@T("密码不能为空")',
                    //     minlength: '@T("密码长度为6-20")',
                    //     maxlength: '@T("密码长度为6-20")',

                    // }
                }
            });
        });
</script>