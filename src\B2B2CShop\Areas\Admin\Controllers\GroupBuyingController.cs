﻿using B2B2CShop.Entity;
using iTextSharp.text.pdf.parser.clipper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.CodeAnalysis.Recommendations;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Timing;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("拼团管理")]
    [Description("拼团活动管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/GroupBuying", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/GroupBuying", CurrentMenuName = "GroupBuyingList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class GroupBuyingController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("拼团列表")]
        public IActionResult Index(string name, long storeId, int status = -1,int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            
            viewModel.list = GroupBuy.Search(name,storeId, status, pages);
            viewModel.storelist = Store.FindAll();
            viewModel.name = name;
            viewModel.storeId = storeId;
            viewModel.status = status;
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {
                { "name", name },
                { "storeId", storeId.SafeString() },
                { "status", status.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("活动详情")]
        public IActionResult GroupBuyingDetail(long id,int status = -1)
        {
            var entity = GroupBuy.FindById(id);
            if (entity == null || entity.Id <= 0)
                return Content(GetResource("找不到活动记录"));
            dynamic viewModel = new ExpandoObject();
            viewModel.entity = GroupBuy.FindById(id);

            viewModel.list = GroupBuyGroup.FindAllByGIdAndState(id,status);
            viewModel.orderlist = GroupBuyOrder.FindAllByGroupBuyId(id);
            viewModel.status = status;
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("提前结束")]
        public IActionResult EndGroupBuying(long id)
        {
            var entity = GroupBuy.FindById(id);
            if (entity == null || entity.Id <= 0)
                return Json(new DResult() { success = false, msg = GetResource("找不到活动记录") });
            if (entity.State != 1)
                return Json(new DResult() { success = false, msg = GetResource("只有进行中的活动才能结束") });
            entity.State = 0;
            var result = entity.Update();
            if (result <= 0)
                return Json(new DResult() { success = false, msg = GetResource("操作失败") });

            GroupBuy.Meta.Cache.Clear("提前结束", true);
            return Json(new DResult() { success = true, msg = GetResource("操作成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("删除活动")]
        public IActionResult DeleteGroupBuying(long id)
        {
            var entity = GroupBuy.FindById(id);
            if (entity == null || entity.Id <= 0)
                return Json(new DResult() { success = false, msg = GetResource("找不到活动记录") });
            if (entity.State == 1)
                return Json(new DResult() { success = false, msg = GetResource("活动正在进行，无法删除") });

            var result = entity.Delete();
            if (result <= 0)
                return Json(new DResult() { success = false, msg = GetResource("操作失败") });

            GroupBuy.Meta.Cache.Clear("删除活动", true);
            return Json(new DResult() { success = true, msg = GetResource("操作成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐管理")]
        public IActionResult GroupBuyingPackage(long storeId, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.storelist = Store.FindAll();
            viewModel.storeId = storeId;
            viewModel.list = GroupBuyQuota.Search(storeId, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("GroupBuyingPackage"), new Dictionary<string, string> {
                { "storeId", storeId.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        public IActionResult GroupBuyingSetting()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.price = OperationConfig.GetValueByCode("GroupBuyPackagePrice").ToDecimal();//代金券套餐价格
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        [HttpPost]
        public IActionResult PackageSet(decimal price)
        {
            // 参数校验
            if (price < 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("价格不能为负数") });
            }
            var flag = OperationConfig.UpdateValueByCode("GroupBuyPackagePrice", price.SafeString(), "抢购套餐价格");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到抢购套餐价格配置") });
            }
          
            OperationConfig.Meta.Cache.Clear("", true);

            return Json(new DResult() { success = true, msg = GetResource("设置成功") });
        }
    }
}
