@using B2B2CShop.Entity
@using Pek.Timing
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "FullGift";
    PekHtml.AppendPageCssClassParts("html-sellerFullGift-page");
    PekHtml.AppendTitleParts(T("满即送活动").Text);
}
@await Html.PartialAsync("_Left")

<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li class="current"><a href="@Url.Action("Index")">@T("活动列表")</a></li>
            </ul>
            <a class="dssc-btn dssc-btn-green" style="right:100px" href="@Url.Action("Add")"><i class="iconfont">&#xe6db;</i>@T("添加活动")</a>
            <a class="dssc-btn dssc-btn-acidblue" href="@Url.Action("BuyPackage")" title=""><i class="iconfont">&#xe6a1;</i>@T("套餐续费")</a>

        </div>
        <div class="p20">
            <div class="alert alert-block mt10">
                @if (Model.quotaTip != "")
                {
                    <strong style="color: #F00;">@Model.quotaTip</strong>
                }
                else
                {
                    <strong>@T("当前没有可用套餐，请先购买套餐")</strong>
                }
                <ul>
                    <li>@T("1、已参加秒杀、抢购的商品，可同时参加满即送活动")</li>
                    <li>2、<strong style="color: red">@T("相关费用会在店铺的账期结算中扣除")</strong>。</li>
                </ul>
            </div>

            <form method="get">
                <table class="search-form">
                    <tr>
                        <td>&nbsp;</td>
                        <th>@T("活动状态")</th>
                        <td class="w100">
                            <select name="state">
                                <!option value="0" @(Model.state == 0 ? "selected" : "")>@T("全部")</!option>
                                <!option value="1" @(Model.state == 1 ? "selected" : "")>@T("正常")</!option>
                                <!option value="2" @(Model.state == 2 ? "selected" : "")>@T("已结束")</!option>
                                <!option value="3" @(Model.state == 3 ? "selected" : "")>@T("管理员关闭")</!option>
                            </select>
                        </td>
                        <th class="w110">@T("活动名称")</th>
                        <td class="w160"><input type="text" class="text w150" name="name" value="@Model.name"/></td>
                        <td class="w70 tc">
                            <input type="submit" class="submit" value='@T("搜索")' />
                        </td>
                    </tr>
                </table>
            </form>
                <table class="dssc-default-table">
                    <thead>
                    <tr>
                        <th class="w30"></th>
                        <th>@T("活动名称")</th>
                        <th class="w200">@T("开始时间")</th>
                        <th class="w200">@T("结束时间")</th>
                        <th class="w150">@T("状态")</th>
                        <th class="w150">@T("操作")</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (FullGift item in Model.list)
                    {
                        <tr class="bd-line">
                            <td></td>
                            <td>
                                <dl>
                                    <dt>@item.Name</dt>
                                </dl>
                            </td>
                            <td class="goods-time">@UnixTime.ToDateTime(item.StartTime)</td>
                            <td class="goods-time">@UnixTime.ToDateTime(item.EndTime)</td>
                            <td>@(item.State switch{
                                    1 => T("正常"),
                                    2 => T("已结束"),
                                    3 => T("管理员关闭"),
                                    _ => T("未知")
                                })</td>
                            <td class="dscs-table-handle">
                                <span>
                                    <a href="@Url.Action("Detail",new {id = item.Id})" class="btn-blue">
                                        <i class="iconfont">&#xe600;</i>
                                        <p>@T("详细")</p>
                                    </a>
                                </span> <span>
                                    <a dstype="btn_mansong_del" data-mansong-id="@item.Id" href="javascript:return void(0)" class="btn-red">
                                        <i class="iconfont">&#xe725;</i>
                                        <p>@T("删除")</p>
                                    </a>
                                </span>
                            </td>
                        </tr>
                    }
                        </tbody>
                    <tfoot>
                    <tr>
                        <td colspan="20"><div class="pagination"></div></td>
                    </tr>
                </tfoot>
            </table>
            <ul class="pagination">
                @Html.Raw(Model.PageHtml)
            </ul>
            
            <form id="submit_form" action="" method="post" >
                <input type="hidden" id="ids" name="ids" value="">
            </form>

            <script type="text/javascript">
                $(document).ready(function () {
                    $('[dstype="btn_mansong_del"]').on('click', function () {
                        var action = "@Url.Action("Delete")";
                        var mansong_id = $(this).attr('data-mansong-id');
                        layer.confirm('您确定要取消吗?', {
                            btn: ['确定', '取消'],
                            title: false,
                        }, function () {
                            $.post(action, { ids: mansong_id }, function (res) {
                                if (res && res.success) {
                                    layer.msg('@T("删除成功")', { time: 800,icon:1}, function () {
                                        // 刷新当前页面
                                        location.reload();
                                    });
                                } else {
                                    layer.msg(res?.msg || '@T("删除失败，请重试")',{icon:2});
                                }
                            }, 'json').fail(function () {
                                layer.alert('@T("请求失败，请重试")');
                            });
                        });
                    });
                });
            </script>

        </div>
    </div>
</div>