@using B2B2CShop.Entity
@using Pek.Timing
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "Bargain";
    PekHtml.AppendPageCssClassParts("html-sellerbargain-page");
    PekHtml.AppendTitleParts(T("新增砍价").Text);
    var localizationSettings = LocalizationSettings.Current;
    var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
    BargainQuota quota = Model.quota;
}
@await Html.PartialAsync("_Left")
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li><a href="@Url.Action("Index")">@T("砍价活动列表")</a></li>
                <li class="current"><a href="#">@T("新增砍价")</a></li>
            </ul>
        </div>
        <div class="p20">

            <div class="dssc-form-default">
                <form id="add_form" method="post" action="@Url.Action("Add")">
                    <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                        @if (localizationSettings.IsEnable)
                        {
                            <ul class="layui-tab-title">
                                <li data="" class="layui-this">@T("标准") </li>
                                @foreach (var item in LanguageList)
                                {
                                    <li data="@item.Id" class="LId">@item.DisplayName</li>
                                }
                            </ul>
                        }
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <dl>
                                    <dt><i class="required">*</i>@T("砍价名称：")</dt>
                                    <dd>
                                        <input id="name" name="name" type="text" maxlength="25" class="text w400"
                                            value="" />
                                        <span></span>
                                        <p class="hint">@T("输入砍价的名称")</p>
                                    </dd>
                                    <dl>
                                        <dt>@T("分享描述：")</dt>
                                        <dd>
                                            <input id="name" name="remark" type="text" maxlength="25" class="text w400"
                                                value="" />
                                            <span></span>
                                            <p class="hint"></p>
                                        </dd>
                                    </dl>
                                </dl>
                            </div>
                            @if (localizationSettings.IsEnable)
                            {
                                @foreach (var item in LanguageList)
                                {
                                    <div class="layui-tab-item">
                                        <dl>
                                            <dt>@T("砍价名称：")</dt>
                                            <dd>
                                                <input id="<EMAIL>" name="[@item.Id].name" type="text" maxlength="25"
                                                    class="text w400" value="" />
                                                <span></span>
                                                <p class="hint">@T("输入砍价的名称")</p>
                                            </dd>
                                        </dl>
                                        <dl>
                                            <dt>@T("分享描述：")</dt>
                                            <dd>
                                                <input id="<EMAIL>" name="[@item.Id].remark" type="text" maxlength="25"
                                                    class="text w400" value="" />
                                                <span></span>
                                                <p class="hint"></p>
                                            </dd>
                                        </dl>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                    <dl>
                        <dt><i class="required">*</i>@T("砍价开始时间：")</dt>
                        <dd>
                            <input id="starttime" name="starttime" type="text" class="text w130" /><em class="add-on"><i
                                    class="iconfont">&#xe8d6;</i></em>
                            <span></span>
                            <p class="hint">
                                @T("开始时间不能为空且不能早于当前时间")<br /><span class="red">@T("砍价开始时间不可修改")</span>
                            </p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("砍价结束时间：")</dt>
                        <dd>
                            <input id="endtime" name="endtime" type="text" class="text w130" /><em class="add-on"><i
                                    class="iconfont">&#xe8d6;</i></em><span></span>
                            <p class="hint">
                                @T("结束时间不能为空且不能晚于")@(UnixTime.ToDateTime(quota.EndTime))<br /><span
                                    class="red">@T("砍价结束时间不可修改")</span>
                            </p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("砍价商品：")</dt>
                        <dd>
                            <div dstype="bargain_goods_info" class="selected-group-goods " style="display:none;">
                                <div class="goods-thumb"><img id="bargain_goods_image" src="" /></div>
                                <div class="goods-name">
                                    <a dstype="bargain_goods_href" id="bargain_goods_name" href="" target="_blank"></a>
                                </div>
                                <div class="goods-price">@T("商城价：")<span dstype="bargain_goods_price"></span></div>
                            </div>
                            <a href="javascript:void(0);" id="btn_show_search_goods"
                                class="dssc-btn dssc-btn-acidblue">@T("选择商品")</a>
                            <input id="goodsId" name="goodsId" type="hidden" value="" />
                            <input id="skuId" name="skuId" type="hidden" value="" />
                            <span></span>
                            <div id="div_search_goods" class="div-goods-select mt10" style="display: none;">
                                <table class="search-form">
                                    <tr>
                                        <th class="w150">
                                            <strong>@T("第一步：搜索店内商品")</strong>
                                        </th>
                                        <td class="w160">
                                            <input id="search_goods_name" type="text w150" class="text"
                                                name="goods_name" value="" />
                                        </td>
                                        <td class="w70 tc">
                                            <a href="javascript:void(0);" id="btn_search_goods" class="dssc-btn" /><i
                                                class="iconfont">&#xe718;</i>@T("搜索")</a>
                                        </td>
                                        <td class="w10"></td>
                                        <td>
                                            <p class="hint">@T("不输入名称直接搜索将显示店内所有普通商品，特殊商品不能参加。")</p>
                                        </td>
                                    </tr>
                                </table>
                                <div id="div_goods_search_result" class="search-result" style="width:739px;"></div>
                                <a id="btn_hide_search_goods" class="close" href="javascript:void(0);">X</a>
                            </div>
                            <p class="hint">@T("选择参加砍价对应的商品")</p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("商品底价：")</dt>
                        <dd>
                            <input id="floorprice" name="floorprice" type="text" class="text w130"
                                value="" />@T("元")<span></span>
                            <p class="hint">@T("砍价商品的最低价格")</p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("限购数量：")</dt>
                        <dd>
                            <input id="limitnum" name="limitnum" type="text" class="text w130" value="1" /><span></span>
                            <p class="hint">@T("砍价成功后用户最多可购买的商品数量")</p>
                        </dd>
                    </dl>

                    <dl>
                        <dt><i class="required">*</i>@T("砍价有效期：")</dt>
                        <dd>
                            <input id="timelimithour" name="timelimithour" type="text" class="text w130"
                                value="48" />@T("小时")<span></span>
                            <p class="hint">@T("用户发起砍价活动的有效时间")</p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("共多少刀砍至底价：")</dt>
                        <dd>
                            <input id="totalcuts" name="totalcuts" type="text" class="text w130"
                                value="20" /><span></span>
                            <p class="hint">@T("砍价商品达到底价需要的砍价次数")</p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("每刀最多可砍金额：")</dt>
                        <dd>
                            <input id="maxcutamount" name="maxcutamount" type="text" class="text w130"
                                value="1" />元<span></span>
                            <p class="hint">@T("帮砍用户每次砍价最多可砍的金额")</p>
                        </dd>
                    </dl>
                    <div class="bottom">
                        <input id="submit_button" type="submit" class="submit" value='@T("提交")'>
                    </div>
                </form>
            </div>
            <link rel="stylesheet" href="/static/plugins/js/jquery-ui-timepicker/jquery-ui-timepicker-addon.min.css">
            <script src="/static/plugins/js/jquery-ui-timepicker/jquery-ui-timepicker-addon.min.js"></script>
            <script src="/static/plugins/js/jquery-ui-timepicker/i18n/jquery-ui-timepicker-zh-CN.js"></script>
        </div>
    </div>
</div>

<script type="text/javascript">
    layui.use('element', function () {
        var element = layui.element;
    })
</script>

<script type="text/javascript">
    $(function () {
        // 审核期最小可选时间：reviewday<=0 取当前时间，否则取 当前时间+reviewday天
        var reviewMinStr = '@(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))';

        // 套餐结束时间（若无套餐则为空）
        var quotaEndStr = '@(quota != null ? UnixTime.ToDateTime(quota.EndTime).ToString("yyyy-MM-dd HH:mm:ss") : "")';

        // 默认隐藏搜索框
        $("#div_search_goods").hide();

        // 展示搜索框
        $("#btn_show_search_goods").on('click', function () {
            $("#div_search_goods").show();
        })

        // 隐藏搜索框
        $("#btn_hide_search_goods").on('click', function () {
            $("#div_search_goods").hide();
        })

        // 点击搜索按钮进行搜索并展示商品内容
        $("#btn_search_goods").on('click', function (e) {
            e.preventDefault();
            var goodsName = $("#search_goods_name").val();
            console.log('goodsName', goodsName)

            // 通过AJAX加载SearchGoodsList视图
            $.ajax({
                url: '@Url.Action("SearchGoodsList")',
                type: 'POST',
                data: { keyword: goodsName },
                success: function (result) {
                    // 直接将返回的HTML内容显示在搜索结果区域
                    $("#div_goods_search_result").html(result);

                    // 重新绑定商品选择按钮的点击事件
                    $(document).on('click', 'a[dstype="btn_add_xianshi_goods"]', function () {
                        var goodsId = $(this).data('goods-id');
                        var skuId = $(this).data('goods-skuid');
                        var goodsName = $(this).closest('li').find('.goods-info dt').text();
                        var goodsPrice = $(this).closest('li').find('.goods-info dd').text().replace(/[^0-9.]/g, '');
                        var goodsImage = $(this).closest('li').find('.goods-thumb img').attr('src');

                        // 填充商品信息到表单
                        $('#goodsId').val(goodsId);
                        $('#skuId').val(skuId);
                        $('#bargain_goods_name').text(goodsName);
                        $('[dstype="bargain_goods_price"]').text(goodsPrice);
                        $('#bargain_goods_image').attr('src', goodsImage);

                        // 显示已选择商品区域，隐藏搜索区域
                        $('[dstype="bargain_goods_info"]').show();
                        $('#div_search_goods').hide();

                        // console.log('已选择商品：' + goodsName + '，ID：' + goodsId + ',SkuId' + skuId + '，价格：' + goodsPrice);

                    });
                },
                error: function () {
                    $("#div_goods_search_result").html('<div class="error-result">@T("搜索出错，请重试")</div>');
                }
            });
        })
        $('#starttime').datetimepicker({ dateFormat: 'yy-mm-dd' });
        $('#endtime').datetimepicker({ dateFormat: 'yy-mm-dd' });

        jQuery.validator.methods.greaterThanDate = function (value, element, param) {
            var date1 = new Date(Date.parse(param.replace(/-/g, "/")));
            var date2 = new Date(Date.parse(value.replace(/-/g, "/")));
            return date1 < date2;
        };

        jQuery.validator.methods.lessThanDate = function (value, element, param) {
            var date1 = new Date(Date.parse(param.replace(/-/g, "/")));
            var date2 = new Date(Date.parse(value.replace(/-/g, "/")));
            return date1 > date2;
        };

        jQuery.validator.methods.greaterThanStartDate = function (value, element) {
            var start_date = $("#starttime").val();
            if (!start_date) return true; // let required rule handle empty start time
            var date1 = new Date(Date.parse(start_date.replace(/-/g, "/")));
            var date2 = new Date(Date.parse(value.replace(/-/g, "/")));
            return date1 < date2;
        };

        jQuery.validator.methods.lessThanGoodsPrice = function (value, element) {
            var goods_price = $("#input_groupbuy_goods_price").val();
            return Number(value) < Number(goods_price);
        };
        //页面输入内容验证
        $("#add_form").validate({
            errorPlacement: function (error, element) {
                var error_td = element.parent('dd').children('span');
                error_td.append(error);
            },
            onfocusout: false,
            submitHandler: function (form) {
                var $btn = $('#submit_button');
                $btn.prop('disabled', true);
                var postUrl = $('#add_form').attr('action');
                var payload = $(form).serialize();

                $.ajax({
                    url: postUrl,
                    type: 'POST',
                    data: payload,
                    dataType: 'json',
                    success: function (res) {
                        // 兼容不同项目返回格式
                        if (res.success) {
                            layer.msg(res.msg || '@T("添加成功")', { icon: 1, time: 800 }, function () {
                                window.location.href = '@Url.Action("Index")';
                            });
                        } else {
                            layer.msg(res.msg || '@T("添加失败")', { icon: 2, time: 1000 });
                        }
                    },
                    error: function (xhr) {
                        var err = '@T("请求出错，请稍后重试")';
                        layer.msg(err, { icon: 2, time: 1000 });
                    },
                    complete: function () { $btn.prop('disabled', false); }
                });
                return false;
             },
            rules: {
                name: {
                    required: true
                },
                floorprice: {
                    required: true,
                    number: true,
                    min: 0.01
                },
                limitnum: {
                    required: true,
                    digits: true,
                    min: 1
                },
                starttime: {
                    required: true,
                    greaterThanDate: reviewMinStr
                },
                endtime: {
                    required: true,
                    lessThanDate: quotaEndStr,
                    greaterThanStartDate: true
                },
                timelimithour: {
                    required: true,
                    digits: true,
                    min: 1,
                    max: 48,
                }
            },
            messages: {
                name: {
                    required: '<i class="iconfont">&#xe64c;</i>@T("请输入砍价名称")'
                },
                floorprice: {
                    required: '<i class="iconfont">&#xe64c;</i>@T("商品底价不能为空")',
                    number: '<i class="iconfont">&#xe64c;</i>@T("商品底价必须为数字，允许小数")',
                    min: '<i class="iconfont">&#xe64c;</i>@T("商品底价必须大于0")'
                },
                limitnum: {
                    required: '<i class="iconfont">&#xe64c;</i>@T("数量不能为空")',
                    digits: '<i class="iconfont">&#xe64c;</i>@T("必须为数字类型")',
                    min: '<i class="iconfont">&#xe64c;</i>@T("数量不能小于1")'
                },
                starttime: {
                    required: '<i class="iconfont">&#xe64c;</i>@T("开始时间不能为空")',
                    greaterThanDate: '<i class="iconfont">&#xe64c;</i>@T("开始时间不能为空且不能早于当前时间")'
                },
                endtime: {
                    required: '<i class="iconfont">&#xe64c;</i>@T("结束时间不能为空")',
                    lessThanDate: '<i class="iconfont">&#xe64c;</i>' +
                        "@T("结束时间不能为空且不能晚于")"+quotaEndStr,
                    greaterThanStartDate: '<i class="iconfont">&#xe64c;</i>@T("结束时间必须大于开始时间")'
                },
                timelimithour: {
                    required: '<i class="iconfont">&#xe64c;</i>@T("请输入时间限制")',
                    digits: '<i class="iconfont">&#xe64c;</i>@T("时间限制必须为数字")',
                    min: '<i class="iconfont">&#xe64c;</i>@T("时间限制不能小于1")',
                    max: '<i class="iconfont">&#xe64c;</i>@T("时间限制不能大于48")',
                }

            }
        });
    })
</script>