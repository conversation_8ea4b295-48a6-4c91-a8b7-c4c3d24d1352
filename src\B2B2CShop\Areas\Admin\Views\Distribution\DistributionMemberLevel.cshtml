﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("分销设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("DistributionSetting")"><span>@T("分销设置")</span></a></li>
                <li><a href="@Url.Action("DistributionGoods")"><span>@T("分销商品")</span></a></li>
                <li><a href="@Url.Action("DistributionMember")"><span>@T("分销员管理")</span></a></li>
                <li><a href="@Url.Action("DistributionMemberLevel")" class="current"><span>@T("分销员等级")</span></a></li>
                <li><a href="@Url.Action("DistributionOrder")"><span>@T("分销员订单")</span></a></li>
            </ul>
        </div>
    </div>
    <div class="ds-search-form">
        <div class="btn_group">
            <a href="javascript:dsLayerOpen('@Url.Action("AddMemberLevel")','@T("添加")')" class="btn"><span>@T("添加")</span></a>
        </div>
    </div>


    <table class="ds-default-table">
        <thead>
            <tr class="thead">
                <th></th>
                <th class="w60">@T("分销员等级")</th>
                <th class="w200">@T("佣金门槛")</th>
                <th class="w200 align-center">@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (InviterClass item in Model.list)
            {
                <tr class="hover edit">
                    <td class="w48"><input type="checkbox" name="id[]" value="@item.Id" class="checkitem"></td>
                    <td class="w48 sort">@item.Name</td>
                    <td class="w50pre name">@item.Amount</td>

                    <td class="w132 align-center">
                        <a href="javascript:dsLayerOpen('@Url.Action("EditMemberLevel",new{id = item.Id})','@(T("编辑") + "-" + item.Name)')" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                        <a href="javascript:dsLayerConfirm('@Url.Action("DeleteMemberLevel", new { ids = item.Id })','@T("您确定要删除吗")?')" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td>
                    <input type="checkbox" class="checkall" id="checkall_1">
                    <span class="all_checkbox">
                        <label for="checkall_2">@T("全选")</label>
                    </span>&nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
                <td id="batchAction" colspan="15">
                    
                </td>
            </tr>
        </tfoot>
    </table>
</div>
<script type="text/javascript">
    function getSelectedIds() {
        var checks = document.querySelectorAll('input.checkitem:checked');
        var ids = Array.prototype.map.call(checks, function (c) { return c.value; });
        return ids;
    }

    window.submit_delete_batch = function () {
        var ids = getSelectedIds();
        if (!ids || ids.length === 0) {
            layer.msg('@T("请选择要操作的项")', { icon: 0 });
            return;
        }
        var url = '@Url.Action("DeleteMemberLevel")' + '?ids=' + encodeURIComponent(ids.join(','));
        dsLayerConfirm(url, '@T("您确定要删除所选项吗?")');
    };
</script>