@using B2B2CShop.Entity
@using Pek.Timing
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "FullGift";
    PekHtml.AppendPageCssClassParts("html-sellerFullGift-page");
    PekHtml.AppendTitleParts(T("新增活动").Text);
    var localizationSettings = LocalizationSettings.Current;
    var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
    FullGiftQuota quota = Model.quota;
}
@await Html.PartialAsync("_Left")
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li><a href="@Url.Action("Index")">@T("活动列表")</a></li>
                <li class="current"><a href="@Url.Action("Add")">@T("添加活动")</a></li>
            </ul>

        </div>
        <div class="p20">

            <div class="dssc-form-default">
                <div class="alert alert-block">
                    <h4>@T("说明：")</h4>
                    <ul>
                        <li>@T("满即送活动包括店铺所有商品，活动时间不能和已有活动重叠")</li>
                        <li>@T("每个满即送活动最多可以设置3个价格级别，点击新增级别按钮可以增加新的级别，价格级别应该由低到高")</li>
                        <li>@T("每个级别可以有减现金、送礼品2种促销方式，至少需要选择一种")</li>
                    </ul>
                </div>
                <form id="add_form" action="@Url.Action("Add")" method="post">
                    <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                        @if (localizationSettings.IsEnable)
                        {
                            <ul class="layui-tab-title">
                                <li data="" class="layui-this">@T("标准") </li>
                                @foreach (var item in LanguageList)
                                {
                                    <li data="@item.Id" class="LId">@item.DisplayName</li>
                                }
                            </ul>
                        }
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <dl>
                                    <dt><i class="required">*</i>@T("活动名称：")</dt>
                                    <dd>
                                        <input id="name" name="name" type="text" maxlength="25" class="w400 text" />
                                        <span class="error-message"></span>
                                        <p class="hint">@T("活动名称最多为25个字符")</p>
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>@T("备注：")</dt>
                                    <dd>
                                        <textarea name="remark" rows="3" id="remark" maxlength="100"
                                            class="textarea w400"></textarea>
                                        <p class="hint">@T("活动备注最多为100个字符")</p>
                                    </dd>
                                </dl>
                            </div>
                            @if (localizationSettings.IsEnable)
                            {
                                @foreach (var item in LanguageList)
                                {
                                    <div class="layui-tab-item">
                                        <dl>
                                            <dt>@T("活动名称：")</dt>
                                            <dd>
                                                <input id="<EMAIL>" name="[@item.Id].name" type="text" maxlength="25"
                                                    class="w400 text" />
                                                <span class="error-message"></span>
                                                <p class="hint">@T("活动名称最多为25个字符")</p>
                                            </dd>
                                        </dl>
                                        <dl>
                                            <dt>@T("备注：")</dt>
                                            <dd>
                                                <textarea name="[@item.Id].remark" rows="3" id="<EMAIL>" maxlength="100"
                                            class="textarea w400"></textarea>
                                                <p class="hint">@T("活动备注最多为100个字符")</p>
                                            </dd>
                                        </dl>
                                    </div>

                                }
                            }
                        </div>
                    </div>
                    <dl>
                        <dt><i class="required">*</i>@T("开始时间：")</dt>
                        <dd>
                            <input id="starttime" name="starttime" type="text" class="text w130" /><em class="add-on"><i
                                    class="iconfont">&#xe8d6;</i></em>
                            <span class="error-message"></span>
                            <p class="hint">@T("开始时间不能为空且不能早于当前时间")</p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("结束时间：")</dt>
                        <dd>
                            <input id="endtime" name="endtime" type="text" class="text w130" /><em class="add-on"><i
                                    class="iconfont">&#xe8d6;</i></em>
                            <span class="error-message"></span>
                            <p class="hint">
                                @T("结束时间不能为空且不能晚于") @UnixTime.ToDateTime(quota.EndTime).ToString("yyyy-MM-dd HH:mm")</p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("满即送规则：")</dt>
                        <dd>
                            <input type="hidden" id="mansong_rulecount" name="rulecount">
                            <ul id="mansong_rule_list" class="dssc-mansong-rule-list">
                            </ul>
                            <a href="javascript:void(0);" id="btn_add_rule" class="dssc-btn dssc-btn-acidblue"><i
                                    class="iconfont">&#xe6db;</i>@T("添加规则")</a>
                            <div id="div_add_rule" style="display:none;">
                                <div class="dssc-mansong-error"><span id="mansong_price_error" style="display:none;"><i
                                            class="iconfont">&#xe64c;</i>@T("规则金额不能为空且必须为数字")</span><span
                                        id="mansong_discount_error" style="display:none;"><i
                                            class="iconfont">&#xe64c;</i>@T("满减金额必须小于规则金额")</span></div>
                                <div class="dssc-mansong-rule">
                                    <span>@T("单笔订单满")&nbsp;<input id="mansong_price" type="text" class="text w50"><em
                                            class="add-on"><i class="iconfont">&#xe65c;</i></em>@T("，")</span>
                                    <span>@T("立减现金")&nbsp;<input id="mansong_discount" type="text" class="text w50"><em
                                            class="add-on"><i class="iconfont">&#xe65c;</i></em>@T("，")</span>
                                    <span>@T("送礼品")&nbsp;<a href="javascript:void(0);" id="btn_show_search_goods"
                                            class="dssc-btn"><i class="iconfont">&#xe753;</i>@T("选择礼品")</a></span>
                                    <div id="mansong_goods_item" class="gift"></div>

                                    <div id="div_search_goods" class="div-goods-select mt10" style="display: none;">
                                        <table class="search-form">
                                            <tr>
                                                <th class="w150">
                                                    <strong>@T("第一步：搜索店内商品")</strong>
                                                </th>
                                                <td class="w160">
                                                    <input id="search_goods_name" type="text w150" class="text"
                                                        name="goods_name" value="" />
                                                </td>
                                                <td class="w70 tc">
                                                    <a href="javascript:void(0);" id="btn_search_goods"
                                                        class="dssc-btn" /><i class="iconfont">&#xe718;</i>@T("搜索")</a>
                                                </td>
                                                <td class="w10"></td>
                                                <td>
                                                    <p class="hint">@T("不输入名称直接搜索将显示店内所有出售中的商品")</p>
                                                </td>
                                            </tr>
                                        </table>
                                        <a id="btn_hide_search_goods" class="close" href="javascript:void(0);">X</a>
                                        <div id="div_goods_search_result" class="search-result" style="width:739px;">
                                        </div>
                                    </div>
                                </div>
                                <div id="mansong_rule_error" style="display:none;">@T("请至少选择一种促销方式")</div>
                                <div class="mt10">
                                    <a href="javascript:void(0);" id="btn_save_rule"
                                        class="dssc-btn dssc-btn-acidblue"><i
                                            class="iconfont">&#xe64d;</i>@T("确定规则设置")</a>
                                    <a href="javascript:void(0);" id="btn_cancel_add_rule"
                                        class="dssc-btn dssc-btn-orange"><i class="iconfont">&#xe754;</i>@T("取消")</a>
                                </div>
                            </div>
                            <span class="error-message"></span>
                            <p class="hint">
                                @T("设置当单笔订单满足金额时（必填选项），减免金额（选填）或赠送的礼品（选填）；留空为不做减免金额或赠送礼品处理。")<br />@T("系统最多支持设置三组等级规则。")
                            </p>
                        </dd>
                    </dl>
                    <div class="bottom">
                        <input id="submit_button" type="submit" value='@T("提交")' class="submit">
                    </div>
                </form>
            </div>
            <script id="mansong_rule_template" type="text/html">
                <li dstype="mansong_rule_item">
                    <span>@T("单笔订单满")<strong><%=price%></strong>@T("元")， </span>
                    <span>@T("立减现金")<strong><%=discount%></strong>@T("元")， </span>
                    <%if(goods_skuid>0){%>
                    <span>@T("送礼品") <%==goods%></span>
                    <%}%>
                    <input type="hidden" name="mansong_rule[]" value="<%=price%>,<%=discount%>,<%=goods_skuid%>">
                    <a dstype="btn_del_mansong_rule" href="javascript:void(0);" class="dssc-btn-mini dssc-btn-red"><i class="iconfont">&#xe725;</i>删除</a>
                </li>
            </script>
            <script id="mansong_goods_template" type="text/html">
                <div dstype="mansong_goods" class="selected-mansong-goods">
                    <a href="<%=goods_url%>" title="<%=goods_name%>" class="goods-thumb" target="_blank">
                        <img src="<%=goods_image_url%>"/>
                    </a>
                    <input dstype="mansong_goods_skuid" type="hidden" value="<%=goods_skuid%>">
                </div><a dstype="btn_del_mansong_goods" href="javascript:void(0);" class="dssc-btn-mini dssc-btn-red"><i class="iconfont">&#xe725;</i>删除已选择的礼品</a>
            </script>
            <script src="/static/plugins/template.min.js"></script>
            <link rel="stylesheet" href="/static/plugins/js/jquery-ui-timepicker/jquery-ui-timepicker-addon.min.css">
            <script src="/static/plugins/js/jquery-ui-timepicker/jquery-ui-timepicker-addon.min.js"></script>
            <script src="/static/plugins/js/jquery-ui-timepicker/i18n/jquery-ui-timepicker-zh-CN.js"></script>
        </div>
    </div>
</div>
<script type="text/javascript">
    layui.use('element', function () {
        var element = layui.element;
    })
</script>

<script type="text/javascript">
    $(function () {
        var now = '@DateTime.Now.ToString("yyyy-MM-dd")';
        var quotaEnd = '@UnixTime.ToDateTime(quota.EndTime).ToString("yyyy-MM-dd HH:mm")';
        $('#starttime').datetimepicker({
            dateFormat: 'yy-mm-dd',
            timeFormat: 'HH:mm:ss',
            showSecond: true,
            minDate: now,
            maxDate: quotaEnd,   // 套餐到期不能超出
            onSelect: function () {
                var start = $('#starttime').datetimepicker('getDate');
                if (start) {
                    // endtime 最小必须 >= start
                    $('#endtime').datetimepicker('option', 'minDate', start);
                    // shippingtime 也至少在 start 之后（可选）
                    $('#shippingtime').datetimepicker('option', 'minDate', start);
                }
            }
        });
         $('#endtime').datetimepicker({
            dateFormat: 'yy-mm-dd',
            timeFormat: 'HH:mm:ss',
            minDate: now,            // 初始至少今天
            maxDate: quotaEnd,   // 套餐到期不能超出
            onSelect: function () {
                var end = $('#endtime').datetimepicker('getDate');
                if (end) {
                    // 限制 starttime 最大不能超过 end
                    $('#starttime').datetimepicker('option', 'maxDate', end);
                    // 发货时间如果要求必须在结束之后，可将 shippingtime 最小值改为 end
                    $('#shippingtime').datetimepicker('option', 'minDate', end);
                }
            }
        });
        

        jQuery.validator.methods.greaterThanDate = function (value, element, param) {
            var date1 = new Date(Date.parse(param.replace(/-/g, "/")));
            var date2 = new Date(Date.parse(value.replace(/-/g, "/")));
            return date1 <= date2;
        };
        jQuery.validator.methods.lessThanDate = function (value, element, param) {
            var date1 = new Date(Date.parse(param.replace(/-/g, "/")));
            var date2 = new Date(Date.parse(value.replace(/-/g, "/")));
            return date1 >= date2;
        };
        jQuery.validator.methods.greaterThanStartDate = function (value, element) {
                var start_date = $("#starttime").val();
            var date1 = new Date(Date.parse(start_date.replace(/-/g, "/")));
            var date2 = new Date(Date.parse(value.replace(/-/g, "/")));
            return date1 < date2;
        };

        //页面输入内容验证
        $("#add_form").validate({
            errorPlacement: function (error, element) {
                var error_td = element.parent('dd').children('span.error-message');
                error_td.append(error);
            },
            onfocusout: false,
            submitHandler: function (form) {
                var $btn = $('#submit_button');
                $btn.prop('disabled', true);
                var postUrl = $('#add_form').attr('action');
                var payload = $(form).serialize();

                $.ajax({
                    url: postUrl,
                    type: 'POST',
                    data: payload,
                    dataType: 'json',
                    success: function (res) {
                        // 兼容不同项目返回格式
                        if (res.success) {
                            layer.msg(res.msg || '@T("添加成功")', { icon: 1, time: 800 }, function () {
                                window.location.href = '@Url.Action("Index")';
                            });
                        } else {
                            layer.msg(res.msg || '@T("添加失败")', { icon: 2, time: 1000 });
                        }
                    },
                    error: function (xhr) {
                        var err = '@T("请求出错，请稍后重试")';
                        layer.msg(err, { icon: 2, time: 1000 });
                    },
                    complete: function () { $btn.prop('disabled', false); }
                });
                return false;
            },
            rules: {
                name: {
                    required: true
                },
                starttime: {
                    required: true,
                    greaterThanDate: now
                },
                endtime: {
                    required: true,
                    lessThanDate: quotaEnd,
                    greaterThanStartDate: true
                },
                rulecount: {
                    required: true,
                    min: 1
                }
            },
            messages: {
                name: {
                    required: '<i class="iconfont">&#xe64c;</i>@T("活动名称不能为空")'
                },
                starttime: {
                    required: '<i class="iconfont">&#xe64c;</i>@T("开始时间不能为空")',
                    greaterThanDate: '<i class="iconfont">&#xe64c;</i>@T("开始时间不能早于当前时间")'
                },
                endtime: {
                    required: '<i class="iconfont">&#xe64c;</i>@T("结束时间不能为空")',

                    lessThanDate: '<i class="iconfont">&#xe64c;</i>@T("结束时间不能晚于")' + quotaEnd,
                    greaterThanStartDate: '<i class="iconfont">&#xe64c;</i>@T("结束时间必须大于开始时间")'
                },
                rulecount: {
                    required: '<i class="iconfont">&#xe64c;</i>@T("请至少添加一条规则并确定")',
                    min: '<i class="iconfont">&#xe64c;</i>@T("请至少添加一条规则并确定")'
                }
            }
        });
        // 默认隐藏搜索框
        $("#div_search_goods").hide();

        // 展示搜索框
        $("#btn_show_search_goods").on('click', function () {
            $("#div_search_goods").show();
        })

        // 隐藏搜索框
        $("#btn_hide_search_goods").on('click', function () {
            $("#div_search_goods").hide();
        })

        // 秒杀添加规则窗口
        $('#btn_add_rule').on('click', function () {
            $('#mansong_price').val('');
            $('#mansong_discount').val('');
            $('#mansong_goods_item').html('');
            $('#mansong_price_error').hide();
            $('#mansong_rule_error').hide();
            $('#div_add_rule').show();
            $('#btn_add_rule').hide();
        });

         // 规则保存
        $('#btn_save_rule').on('click', function() {
            var mansong = {};
            mansong.price = Number($('#mansong_price').val());
            if(isNaN(mansong.price) || mansong.price <= 0) {
                $('#mansong_price_error').show();
                return false;
            } else {
                $('#mansong_price_error').hide();
            }
            mansong.discount = Number($('#mansong_discount').val());
            if(isNaN(mansong.discount) || mansong.discount < 0 || mansong.discount > mansong.price) {
                $('#mansong_discount_error').show();
                return false;
            } else {
                $('#mansong_discount_error').hide();
            }
            mansong.goods = $('#mansong_goods_item').find('[dstype="mansong_goods"]').html();
            mansong.goods_skuid = $('#mansong_goods_item').find('[dstype="mansong_goods_skuid"]').val();
            if(isNaN(mansong.goods_skuid)) {
                mansong.goods_skuid = 0;
            }
            if(mansong.discount == 0 && mansong.goods_skuid == 0) {
                $('#mansong_rule_error').show();
                return false;
            } else {
                $('#mansong_rule_error').hide();
            }
            var mansong_rule_item = template.render('mansong_rule_template', mansong);
            $('#mansong_rule_list').append(mansong_rule_item);
            close_div_add_rule();
        });

        // 删除已添加的规则
        $('#mansong_rule_list').on('click', '[dstype="btn_del_mansong_rule"]', function() {
            $(this).parents('[dstype="mansong_rule_item"]').remove();
            close_div_add_rule();
        });

        // 取消添加规则
        $('#btn_cancel_add_rule').on('click', function() {
            close_div_add_rule();
        });

        // 关闭规则添加窗口
        function close_div_add_rule() {
            var rulecount = $('#mansong_rule_list').find('[dstype="mansong_rule_item"]').length;
            if( rulecount >= 3) {
                $('#btn_add_rule').hide();
            } else {
                $('#btn_add_rule').show();
            }
            $('#div_add_rule').hide();
            $('#mansong_rulecount').val(rulecount);
        }

        // 秒杀商品选择窗口
        $('#btn_show_search_goods').on('click', function() {
            $('#div_search_goods').show();
        });

        // 点击搜索按钮进行搜索并展示商品内容
        $("#btn_search_goods").on('click', function (e) {
            e.preventDefault();
            var goodsName = $("#search_goods_name").val();
            console.log('goodsName', goodsName)

            // 通过AJAX加载SearchGoodsList视图
            $.ajax({
                url: '@Url.Action("SearchGoodsList")',
                type: 'POST',
                data: { keyword: goodsName },
                success: function (result) {
                    // 直接将返回的HTML内容显示在搜索结果区域
                    $("#div_goods_search_result").html(result);

                    // 重新绑定商品选择按钮的点击事件
                    $(document).on('click', 'a[dstype="btn_add_xianshi_goods"]', function () {
                        var goodsId = $(this).data('goods-id');
                        var skuId = $(this).data('goods-skuid');
                        var goodsName = $(this).closest('li').find('.goods-info dt').text();
                        var goodsPrice = $(this).closest('li').find('.goods-info dd').text().replace(/[^0-9.]/g, '');
                        var goodsImage = $(this).closest('li').find('.goods-thumb img').attr('src');

                        // 填充商品信息到表单
                        var goods = {};
                        goods.goods_skuid = skuId
                        goods.goods_name = goodsName
                        goods.goods_image_url = goodsImage
                        goods.goods_price = goodsPrice
                        goods.goods_url = '@Url.Action("Index", "Goods", new { area = "" })' + '/' + skuId+'.html'
                        var mansong_goods_item = template.render('mansong_goods_template', goods);
                        $('#mansong_goods_item').html(mansong_goods_item);
                        $('#div_search_goods').hide();


                        // 获取该商品的 SKU 列表并渲染表格
                        $.ajax({
                            url: '@Url.Action("GetGoodsSku")',
                            type: 'GET',
                            data: { goodsId: goodsId },
                            success: function (res) {
                                if (res && res.success && res.data && res.data.length) {
                                    var tbody = $('#sku_table tbody');
                                    tbody.empty();
                                    res.data.forEach(function (sku) {
                                        // 假设 sku 对象包含 Id, SkuName, Price
                                        var row = '<tr>' +
                                            '<td style="padding:8px;border:1px solid #e6e6e6;">' + (sku.Name) + '</td>' +
                                            '<td style="padding:8px;border:1px solid #e6e6e6;">' + (sku.Price || '') + '</td>' +
                                            '<td style="padding:8px;border:1px solid #e6e6e6;"><input type="text" name="skuPrice[' + sku.Id + ']" class="w70 text sku-price-input" value="" data-skuid="' + sku.Id + '" required/></td>' +
                                            '</tr>';
                                        tbody.append(row);
                                    });
                                    $('#sku_table_container').show();
                                } else {
                                    $('#sku_table_container').hide();
                                }
                            },
                            error: function () {
                                $('#sku_table_container').hide();
                            }
                        });

                    });
                },
                error: function () {
                    $("#div_goods_search_result").html('<div class="error-result">@T("搜索出错，请重试")</div>');
                }
            });
        })

        // 删除以选的商品
        $('#mansong_goods_item').on('click', '[dstype="btn_del_mansong_goods"]', function () {
            $('#mansong_goods_item').html('');
        });

    })
</script>