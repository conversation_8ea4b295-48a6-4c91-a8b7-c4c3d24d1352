﻿using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.DsMallUI;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Webs;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    /// <summary>
    /// 商品分类属性管理
    /// </summary>
    [AdminArea]
    public class ClassAttributesController : PekCubeAdminControllerX
    {
        [DisplayName("列表")]
        public IActionResult Index(Int64 ClassId)
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.ClassId = ClassId;
            viewModel.list = ClassAttributes.FindAllByClassIdLan(ClassId, WorkingLanguage.Id);
            return View(viewModel);
        }

        [DisplayName("新增")]
        public IActionResult Add(Int64 ClassId)
        {
            ViewBag.ClassId = ClassId;
            ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            var listClassAttributes = ClassAttributes.FindAllByClassIdLan(ClassId, WorkingLanguage.Id).Select(e => e.MappingField).ToList();
            List<String> list = new();
            for (int i = 1; i <= 20; i++)
            {
                var str = $"Field{i}";
                if(!listClassAttributes.Contains(str)) list.Add(str);
            }
            ViewBag.Filedlist = list;
            return View();
        }

        [HttpPost]
        [DisplayName("新增")]
        public IActionResult Add(Int64 ClassId,String Name,String MappingField)
        {
            if (Name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("分类属性名称不能为空！") });
            }
            if (MappingField.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("分类属性字段不能为空！") });
            }
            var NameEx = ClassAttributes.Find(ClassAttributes._.ClassId == ClassId & ClassAttributes._.Name == Name);
            if (NameEx != null)
            {
                return Prompt(new PromptModel { Message = GetResource("分类属性名称已经存在！") });
            }
            var MappingFieldEx = ClassAttributes.Find(ClassAttributes._.ClassId == ClassId & ClassAttributes._.MappingField == MappingField);
            if (MappingFieldEx != null)
            {
                return Prompt(new PromptModel { Message = GetResource("分类属性字段已经存在！") });
            }

            using (var tran = ClassAttributes.Meta.CreateTrans())
            {
                var modal = new ClassAttributes()
                {
                    ClassId = ClassId,
                    Name = Name,
                    MappingField = MappingField,
                };
                modal.Insert();

                var localizationSettings = LocalizationSettings.Current;
                if (localizationSettings.IsEnable)
                {
                    var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                    var filea = Request.Form.Files;
                    var list = filea.Count();

                    foreach (var item in Languagelist)
                    {
                        var ex = new ClassAttributesLan();
                        ex.CId = modal.Id;
                        ex.LId = item.Id;
                        ex.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                        ex.Insert();
                    }
                }

                tran.Commit();
            }
            return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index", new { ClassId = ClassId }) });
        }

        [DisplayName("编辑")]
        public IActionResult Edit(Int32 Id)
        {
            var modal = ClassAttributes.FindById(Id);
            if (modal == null)
            {
                return Content(GetResource("内容不存在"));
            }
            ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            var listClassAttributes = ClassAttributes.FindAllByClassIdLan(modal.ClassId, WorkingLanguage.Id).Select(e => e.MappingField).ToList();
            List<String> list = new();
            for (int i = 1; i <= 20; i++)
            {
                var str = $"Field{i}";
                if (!listClassAttributes.Contains(str) || str == modal.MappingField) list.Add(str);
            }
            ViewBag.Filedlist = list;
            return View(modal);
        }

        [HttpPost]
        [DisplayName("编辑")]
        public IActionResult Edit(Int32 Id, String Name, String MappingField)
        {
            var modal = ClassAttributes.FindById(Id);
            if (modal == null)
            {
                return Prompt(new PromptModel { Message = GetResource("分类属性不存在！") });
            }
            if (Name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("分类属性名称不能为空！") });
            }
            if (MappingField.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("分类属性字段不能为空！") });
            }
            var NameEx = ClassAttributes.Find(ClassAttributes._.ClassId == modal.ClassId & ClassAttributes._.Name == Name & ClassAttributes._.Id != modal.Id);
            if (NameEx != null)
            {
                return Prompt(new PromptModel { Message = GetResource("分类属性名称已经存在！") });
            }
            var MappingFieldEx = ClassAttributes.Find(ClassAttributes._.ClassId == modal.ClassId & ClassAttributes._.MappingField == MappingField & ClassAttributes._.Id != modal.Id);
            if (MappingFieldEx != null)
            {
                return Prompt(new PromptModel { Message = GetResource("分类属性字段已经存在！") });
            }

            using (var tran = ClassAttributes.Meta.CreateTrans())
            {
                modal.Name = Name;
                modal.MappingField = MappingField;
                modal.Update();

                var localizationSettings = LocalizationSettings.Current;
                if (localizationSettings.IsEnable)
                {
                    var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                    var filea = Request.Form.Files;
                    var list = filea.Count();

                    foreach (var item in Languagelist)
                    {
                        var ex = ClassAttributesLan.FindByCIdAndLId(modal.Id, item.Id);
                        if(ex == null)
                        {
                            ex = new ClassAttributesLan();
                        }
                        ex.CId = modal.Id;
                        ex.LId = item.Id;
                        ex.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                        ex.Save();
                    }
                }

                tran.Commit();
            }
            return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index", new { ClassId = modal.ClassId }) });
        }

        [DisplayName("删除")]
        public IActionResult Delete(String Ids)
        {
            var res = new DResult();
            if (Ids.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("删除失败");
                return Json(res);
            }

            ClassAttributes.Delete(ClassAttributes._.Id.In(Ids.Split(",")));
            ClassAttributesLan.Delete(ClassAttributesLan._.CId.In(Ids.Split(",")));

            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);
        }
    }

}
