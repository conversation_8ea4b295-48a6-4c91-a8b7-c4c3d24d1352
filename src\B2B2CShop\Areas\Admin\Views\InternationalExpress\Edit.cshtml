﻿@model InternationalExpress
@{
    // script
    PekHtml.AppendScriptParts(ResourceLocation.Head, "/static/plugins/js/layui/layui.js");

    // css
    PekHtml.AppendCssFileParts("/static/plugins/js/layui/css/layui.css");
    var localizationSettings = LocalizationSettings.Current;
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("修改国际快递物流")</h3>
                <h5></h5>
            </div>
        </div>
    </div>

    <form method="post" action="@Url.Action("Edit")">
        <input type="hidden" name="id" value="@Model.Id" />
        <div class="ds-default-form">
            <div class="layui-tab layui-tab-brief  Lan" lay-filter="docDemoTabBrief">
                @if (localizationSettings.IsEnable)
                {
                    <ul class="layui-tab-title">
                        <li class="layui-this">@T("标准"):</li>
                        @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                        {
                            <li>@item.DisplayName</li>
                        }
                    </ul>
                }
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <div class="ncap-form-default">
                            <dl>
                                <dt>@T("公司名称")</dt>
                                <dd><input type="text" name="name" value="@Model.Name" class="txt"></dd>
                            </dl>
                        </div>
                    </div>
                    @if (localizationSettings.IsEnable)
                    {
                        @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                        {
                            var modelLan = InternationalExpressLan.FindByEIdAndLId(Model.Id, item.Id) ?? new InternationalExpressLan();

                            <div class="layui-tab-item">
                                <div class="ncap-form-default">
                                    <dl>
                                        <dt>@T("公司名称")</dt>
                                        <dd><input type="text" name="[@item.Id].name" value="@modelLan.Name" class="txt"></dd>
                                    </dl>
                                </div>
                            </div>
                        }
                    }
                    <div class="ncap-form-default">
                        <dl>
                            <dt>@T("公司代码")</dt>
                            <dd><input type="text" name="key" value="@Model.Key" class="txt"></dd>
                        </dl>
                        <dl>
                            <dt>@T("状态")</dt>
                            <dd><input type="checkbox" name="status" @(Model.Status ? "checked" : "") class="checkbox"></dd>
                        </dl>
                        <dl>
                            <dt>@T("首字母")</dt>
                            <dd><input type="text" name="letter" value="@Model.Letter" class="txt"></dd>
                        </dl>
                        <dl>
                            <dt>@T("类型")</dt>
                            <dd>
                                <select name="oType" class="txt">
                                    <option value="1" selected="@(Model.OType == 1 ? "selected" : null)">@T("常用")</option>
                                    <option value="2" selected="@(Model.OType == 2 ? "selected" : null)">@T("不常用")</option>
                                </select>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("网址")</dt>
                            <dd><input type="text" name="url" value="@Model.Url" class="txt"></dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="btn_group">
                <button type="submit" class="btn">@T("保存")</button>
            </div>
        </div>
    </form>
</div>
