@using B2B2CShop.Entity
@using Pek.Timing
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "PreSell";
    PekHtml.AppendPageCssClassParts("html-sellerpresell-page");
    PekHtml.AppendTitleParts(T("预售活动").Text);
}
@await Html.PartialAsync("_Left")
<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li class="current"><a href="@Url.Action("Index")">@T("预售活动列表")</a></li>
            </ul>
            <a class="dssc-btn dssc-btn-green" style="right:100px" href="@Url.Action("Add")"><i class="iconfont">&#xe6db;</i>@T("新增预售")</a>
            <a class="dssc-btn dssc-btn-acidblue" href="@Url.Action("BuyPackage")" title=""><i class="iconfont">&#xe6a1;</i>@T("套餐续费")</a>
        </div>
        <div class="p20">
            <div class="alert alert-block mt10">
                @if (Model.quotaTip != "")
                {
                    <strong style="color: #F00;">@Model.quotaTip</strong>
                }
                else
                {
                    <strong>@T("当前没有可用套餐，请先购买套餐")</strong>
                }
                <ul>
                    <li>@T("1、点击购买套餐和套餐续费按钮可以购买或续费套餐")</li>
                    <li>@T("2、点击添加活动按钮可以添加预售活动，点击管理按钮可以对预售活动内的商品进行管理")</li>
                    <li>@T("3、点击删除按钮可以删除预售活动")</li>
                    <li>4、<strong style="color: red">@T("相关费用会在店铺的账期结算中扣除")</strong>。</li>
                </ul>
            </div>

            <form method="get">
                <table class="search-form">
                    <tr>
                        <td>&nbsp;</td>
                        <th>@T("状态")</th>
                        <td class="w100">
                            <select name="status">
                                <option value="">@T("-请选择-")</option>
                                <!option value="0" @(Model.status == 0?"selected":"")>@T("已取消")</!option>
                                <!option value="1" @(Model.status == 1?"selected":"")>@T("待开始")</!option>
                                <!option value="2" @(Model.status == 2?"selected":"")>@T("进行中")</!option>
                                <!option value="3" @(Model.status == 3?"selected":"")>@T("已结束")</!option>
                            </select>
                        </td>
                        <th class="w110">@T("预售商品名称")</th>
                        <td class="w160"><input type="text" class="text w150" name="name" value="@Model.name" /></td>
                        <td class="w70 tc">
                            <input type="submit" class="submit" value='@T("搜索")' />
                        </td>
                    </tr>
                </table>
            </form>
            <table class="dssc-default-table">
                <thead>
                    <tr>
                        <th class="">@T("预售商品")</th>
                        <th class="w150">@T("预售价")</th>
                        <th class="w150">@T("预售类型")</th>
                        <th class="w150">@T("预售状态")</th>
                        <th class="w200">@T("预售时间")</th>
                        <th class="w150">@T("操作")</th>
                    </tr>
                </thead>
                <tbody id="presell_list">
                    @foreach (Presell item in Model.list)
                    {
                        <tr class="bd-line">
                            <td><a href="@Url.Action("Index", "Goods", new { skuId = item.GoodsId})" target="_blank">@item.PresellGoodsName</a></td>
                            <td>@item.PresellPrice</td>
                            <td>@(item.PresellType==1?T("全款预售"):T("定金预售"))</td>
                            <td>
                                @(item.PresellState switch{
                                    0=> T("已取消"),
                                    1=> T("待开始"),
                                    2=> T("进行中"),
                                    3=> T("已结束"),
                                })
                                </td>
                            <td class="goods-time">@UnixTime.ToDateTime(item.PresellStartTime)<br>~<br>@UnixTime.ToDateTime(item.PresellEndTime)</td>
                            <td class="dscs-table-handle tc">
                            </td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="7">
                            <div class="pagination"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <ul class="pagination">
                @Html.Raw(Model.PageHtml)
            </ul>
            <form id="submit_form" action="" method="post">
                <input type="hidden" id="presell_id" name="presell_id" value="">
            </form>

            <script type="text/javascript">
                $(document).ready(function () {
                    $('[dstype="btn_end_presell"]').on('click', function () {
                        var action = "/home/<USER>/presell_end.html";
                        var presell_id = $(this).attr('data-presell-id');
                        layer.confirm('@T("您确认取消吗？")', {
                            btn: ['@T("确定")', '@T("取消")'],
                            title: false,
                        }, function () {
                            $('#submit_form').attr('action', action);
                            $('#presell_id').val(presell_id);
                            ds_ajaxpost('submit_form', 'url', "/home/<USER>/index.html");
                        });
                    });
                });
            </script>


        </div>
    </div>
</div>
