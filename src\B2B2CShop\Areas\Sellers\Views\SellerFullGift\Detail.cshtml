@using B2B2CShop.Entity
@using Pek.Timing
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "FullGift";
    PekHtml.AppendPageCssClassParts("html-sellerFullGift-page");
    PekHtml.AppendTitleParts(T("活动内容").Text);
    FullGift entity = Model.entity;
}
@await Html.PartialAsync("_Left")
<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li ><a href="@Url.Action("Index")">@T("活动列表")</a></li>
                <li class="current"><a href="#">@T("活动内容")</a></li>
            </ul>
        </div>
        <div class="p20">
            <table class="dssc-default-table">
                <thead>
                    <tr><th class="w30"></th>
                        <th class="tl">@T("活动名称")</th>
                        <th class="w250">@T("开始时间")&nbsp;-&nbsp;@T("结束时间")</th>

                        <th class="w300">@T("活动内容")</th>
                        <th class="w110">@T("状态")</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="bd-line">
                        <td></td>
                        <td class="tl"><dl class="goods-name"><dt>@entity.Name</dt></dl></td>
                        <td><p>@UnixTime.ToDateTime(entity.StartTime)</p><p>@T("至")</p><p>@UnixTime.ToDateTime(entity.EndTime)</p></td>
                        <td>
                            <ul class="dssc-mansong-rule-list">
                                @foreach (FullGiftRule item in Model.list)
                                {
                                    <li>
                                        @T("单笔订单满")<strong>@item.Price.ToString("F2")</strong>@T("元")，&nbsp;
                                        @if (item.Discount>0)
                                        {
                                             <text>@T("立减现金")<strong>@item.Discount.ToString("F2")</strong>@T("元")，&nbsp;</text>
                                        }
                                        @if (item.GoodsSku != null)
                                        {
                                            @T("送礼品")
                                            <a href="@Url.Action("Index","Goods",new {Area = "",skuId = item.SkuId})" title="@(item.GoodsName +item.GoodsSku.SpecValueDetail())" target="_blank" class="goods-thumb">
                                                <img src="@AlbumPic.FindByName(item.GoodsSku.GetDefaultGoodsImage())?.Cover" />
                                            </a>
                                        }
                                        
                                    </li>
                                }
                                
                            </ul>
                        </td>
                        <td>@(entity.State switch{
                                    1 => T("正常"),
                                    2 => T("已结束"),
                                    3 => T("管理员关闭"),
                                    _ => T("未知")
                                })</td>
                    </tr>
                <tbody>
            </table>
        </div>
    </div>
</div>