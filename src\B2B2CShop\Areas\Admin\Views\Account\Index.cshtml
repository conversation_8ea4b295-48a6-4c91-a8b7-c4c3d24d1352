﻿@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("账号同步")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("设置")</span></a></li>
                <li><a href="@Url.Action("SSOSettings")"><span>@T("SSO登录")</span></a></li>
                <li><a href="@Url.Action("QQSettings")"><span>@T("QQ 互联")</span></a></li>
                <li><a href="@Url.Action("WeiXinSettings")"><span>@T("微信公众号")</span></a></li>
                <li><a href="@Url.Action("OpenWeixinSettings")"><span>@T("微信开放平台")</span></a></li>
                <li><a href="@Url.Action("QyWeiXinSettings")"><span>@T("企业微信")</span></a></li>
                <li><a href="@Url.Action("DingSettings")"><span>@T("钉钉登录")</span></a></li>
                <li><a href="@Url.Action("GitHubSettings")"><span>@T("GitHub登录")</span></a></li>
                <li><a href="@Url.Action("BaiduSettings")"><span>@T("百度登录")</span></a></li>
                <li><a href="@Url.Action("MicrosoftSettings")"><span>@T("微软登录")</span></a></li>
            </ul>
        </div>
    </div>
    @using (Html.BeginForm("UpdateSettings", "Account", FormMethod.Post))
    {
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120">@T("自动注册")</td>
                    <td class="vatop rowform">
                        <div class="onoff">
                            <label for="AutoRegister1" class="cb-enable @(DHSetting.Current.AutoRegister ? "selected" : "")">@T("是")</label>
                            <label for="AutoRegister0" class="cb-disable @(!DHSetting.Current.AutoRegister ? "selected" : "")">@T("否")</label>
                            <input id="AutoRegister1" name="AutoRegister" value="1" type="radio" @(DHSetting.Current.AutoRegister ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                            <input id="AutoRegister0" name="AutoRegister" value="0" type="radio" @(!DHSetting.Current.AutoRegister ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        </div>
                    </td>
                    <td class="vatop tips">@T("如果本地未登录，自动注册新用户")</td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td></td>
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    }
</div>