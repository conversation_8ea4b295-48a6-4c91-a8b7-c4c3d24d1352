@using B2B2CShop.Entity
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "SpecialTopic";
    PekHtml.AppendPageCssClassParts("html-sellerspecialtopic-page");
    PekHtml.AppendTitleParts(T("参与活动").Text);
    Activity entity = Model.entity;
}
@await Html.PartialAsync("_Left")
<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li><a href="@Url.Action("Index")">@T("活动列表")</a></li>
                <li class="current"><a href="@Url.Action("Apply")">@T("参与活动")</a></li>
            </ul>
        </div>
        <div class="p20">
            <form method="GET">
                <input type="hidden" value="2" name="activity_id" />
                <table class="dssc-default-table">
                    <thead>
                        <tr>
                            <th class="w50"></th>
                            <th class="w300 tl">商品名称</th>
                            <th>售价</th>
                            <th class="w120">审核状态</th>
                            <th class="w120">操作</th>
                        </tr>
                    </thead>
                    <tbody dstype="choose_goods_list">
                        @foreach (ActivityDetail item in Model.list)
                        {
                            <tr id="<EMAIL>" class="bd-line">
                                <td>
                                    <div class="pic-thumb">
                                        <a href="@Url.Action("Index","Goods",new {Area = "",skuId = item.ItemId})"
                                           target="_blank">
                                            <img src="@AlbumPic.FindByName(item.GoodsSKU.GetDefaultGoodsImage())?.Cover">
                                        </a>
                                    </div>
                                </td>
                                <td class="tl">
                                    <dl class="goods-name">
                                        <dt><a target="_blank" href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.ItemId })">@item.ItemName</a></dt>

                                    </dl>
                                </td>
                                <td>@item.GoodsSKU.GoodsPrice</td>
                                <td> @(item.State switch{
                                        0=>T("待审核"),
                                        1=>T("通过"),
                                        _=>T("未知状态")
                                    }) 
                                    </td>
                                    <td>
                                        @if (item.State == 0)
                                        {
                                               <span>
                                                     <a class="btn-red" href="javascript:void(0);" dstype="del_choosed" data-gid="@item.Id">
                                                         <i class="iconfont">&#xe725;</i><p>@T("删除")</p>
                                                     </a>
                                             </span>
                                        }
                                    </td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="20"></td>
                        </tr>
                    </tfoot>
                </table>
            </form>

            <div nvtype="div_goods_select" class="div-goods-select">
                <table class="search-form">
                    <tr>
                        <th class="w150"><strong>@T("第一步：搜索店内商品")</strong></th>
                        <td class="w160">
                            <input dstype="search_goods_name" type="text w150" class="text" name="goods_name"
                                value="" />
                        </td>
                        <td class="w70 tc">
                            <input dstype="btn_search_goods" type="button" value='@T("搜索")' class="submit" />
                        </td>
                        <td class="w10"></td>
                        <td>
                            <p class="hint">@T("选择参加活动的商品，勾选并提交平台审核")</p>
                        </td>
                    </tr>
                </table>
                <div dstype="div_goods_search_result" class="search-result"></div>
            </div>
           
        </div>
    </div>
</div>
<script type="text/javascript">
    $(function () {
        // 默认隐藏搜索框
        $("#div_search_goods").hide();

        // 展示搜索框
        $('a[dstype="select_goods"]').click(function () {
            $('div[nvtype="div_goods_select"]').show();
        });

        // 隐藏搜索框
        $('a[dstype="btn_hide_goods_select"]').on('click', function () {
            $('div[nvtype="div_goods_select"]').hide();
        })

        // 点击搜索按钮进行搜索并展示商品内容（点击时禁用按钮，避免重复请求）
        $('input[dstype="btn_search_goods"]').on('click', function (e) {
            e.preventDefault();
            var $searchBtn = $(this);
            if ($searchBtn.prop('disabled')) return;
            $searchBtn.prop('disabled', true);
            var goodsName = $('input[dstype="search_goods_name"]').val()
            console.log('goodsName', goodsName)
            // 通过AJAX加载SearchGoodsList视图
            $.ajax({
                url: '@Url.Action("SearchGoodsList")',
                type: 'POST',
                data: { keyword: goodsName },
                success: function (result) {
                    // 直接将返回的HTML内容显示在搜索结果区域
                    $('div[dstype="div_goods_search_result"]').html(result);

                    // 重新绑定商品选择按钮的点击事件（提交按钮点击时禁用按钮，AJAX 回调再启用）
                    $(document).off('click', '#submit_button').on('click', '#submit_button', function (e) {
                        e.preventDefault(); // 阻止默认提交（如果按钮在子表单内会触发子表单提交）
                        var $btn = $(this);
                        if ($btn.prop('disabled')) return;
                        $btn.prop('disabled', true);
                        var $container = $('div[dstype="div_goods_search_result"]'); // 注入结果的容器
                        var skuIds = $container.find('input[name="item_id[]"]:checked').map(function () {
                            return $(this).val();
                        }).get();

                        if (skuIds.length === 0) {
                            layer.msg('@T("请选择商品")', { icon: 2 });
                            $btn.prop('disabled', false);
                            return;
                        }
                        console.log('skuIds:', skuIds);
                        // 以数组形式提交到服务器（这里使用原片段 form 的提交 URL，按需调整）
                         $.post('@Url.Action("AddGoods")', { 'skuIds[]': skuIds, activityId: '@entity.Id' }, function (res) {
                            // 处理返回（示例）
                            if (res && res.success) {
                                layer.msg(res.msg || '@T("申请成功")', { icon: 1,time:800 },function(){
                                    location.reload(); // 刷新页面以显示最新数据
                                });
                            } else {
                                layer.msg( res.msg || '@T("申请失败")', { icon: 2 });
                            }
                            $btn.prop('disabled', false);
                        }).fail(function () {
                            layer.msg('@T("网络错误")', { icon: 2 });   
                            $btn.prop('disabled', false);
                        }); 
                    });
                },
                error: function () {
                    $("#div_goods_search_result").html('<div class="error-result">搜索出错，请重试</div>');
                },
                complete: function () {
                    // 无论成功或失败都解锁搜索按钮
                    $searchBtn.prop('disabled', false);
                }
            });
        });

        // 删除商品：页面级委托处理，根据 tr id (row_<gid>) 删除（点击时禁用按钮，AJAX 回调再启用）
        $('tbody[dstype="choose_goods_list"]').on('click', 'a[dstype="del_choosed"]', function () {
            var $btn = $(this);
            if ($btn.prop('disabled')) return;
            $btn.prop('disabled', true); // 防重复点击
            var gid = $btn.data('gid');
            if (!gid) {
                // 兼容旧 data-param 格式：{gid:123}
                var dp = $btn.attr('data-param') || '';
                var m = dp.match(/gid\s*:\s*(\d+)/);
                if (m) gid = m[1];
            }
            console.log('del_choosed clicked, gid=', gid);
            var url = "@Url.Action("DeleteApplyGoods")";
            $.getJSON(url, { id: gid }, function (data) {
                if (data && data.success) {
                    var $row = $('#row_' + gid);
                    if ($row.length) {
                        $row.fadeOut('slow', function () { $(this).remove(); if (typeof choosed_goods === 'function') choosed_goods(); });
                    } else {
                        // 后备：在按钮父级中移除
                        $btn.closest('tr').fadeOut('slow', function () { $(this).remove(); if (typeof choosed_goods === 'function') choosed_goods(); });
                    }
                    layer.msg(data.msg, { icon: 1 });
                } else {
                    layer.msg(data.msg || '@T("删除失败")', { icon: 2 });
                }
            }).fail(function () {
                layer.msg('@T("网络错误")', { icon: 2 });
            }).always(function () {
                $btn.prop('disabled', false);
            });
        });
     
    })
</script>