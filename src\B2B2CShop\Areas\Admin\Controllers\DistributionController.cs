﻿using B2B2CShop.Common;
using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife.Data;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("会员等级折扣")]
    [Description("会员等级折扣管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/Distribution", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/Distribution", CurrentMenuName = "Distribution", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class DistributionController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("分销设置")]
        public IActionResult DistributionSetting()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.DistributionEnable = OperationConfig.GetValueByCode("DistributionEnable").ToInt();//分销开关
            viewModel.DistributionLevel = OperationConfig.GetValueByCode("DistributionLevel").ToInt();//分销等级
            viewModel.DistributionShowCommission = OperationConfig.GetValueByCode("DistributionShowCommission").ToInt();//详情页是否显示分销佣金.
            viewModel.DistributionReturnCommissionEnable = OperationConfig.GetValueByCode("DistributionReturnCommissionEnable").ToInt();//分销员返佣开关
            viewModel.DistributionAuditEnable = OperationConfig.GetValueByCode("DistributionAuditEnable").ToInt();//分销员审核开关
            viewModel.DistributionConditions = OperationConfig.GetValueByCode("DistributionConditions").ToInt();//成为分销员条件
            viewModel.DistributionBackgroundImage = OperationConfig.GetValueByCode("DistributionBackgroundImage");//分销背景图片
            viewModel.DistributionCommissionLevel1 = OperationConfig.GetValueByCode("DistributionCommissionLevel1").ToDecimal();//分销1级佣金比例%
            viewModel.DistributionCommissionLevel2 = OperationConfig.GetValueByCode("DistributionCommissionLevel2").ToDecimal();//分销2级佣金比例%
            viewModel.DistributionCommissionLevel3 = OperationConfig.GetValueByCode("DistributionCommissionLevel3").ToDecimal();//分销3级佣金比例%
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("分销设置")]
        [HttpPost]
        public IActionResult DistributionSetting(
            int DistributionEnable,
            int DistributionLevel,
            int DistributionShowCommission,
            int DistributionReturnCommissionEnable,
            int DistributionAuditEnable,
            int DistributionConditions,                  // 0=无门槛，1=按金额
            decimal DistributionConditionAmount,         // 门槛金额
            IFormFile? DistributionBackgroundImage,      // 背景图（上传优先）
            string? DistributionBackgroundImageText,     // 背景图（文本回填）
            decimal DistributionCommissionLevel1,
            decimal DistributionCommissionLevel2,
            decimal DistributionCommissionLevel3
        )
        {
            try
            {
                // 基础校验
                if (DistributionLevel is < 1 or > 3)
                    return Json(new DResult { success = false, msg = GetResource("分销级别不合法") });

                if (DistributionCommissionLevel1 is < 0 or > 100 ||
                    DistributionCommissionLevel2 is < 0 or > 100 ||
                    DistributionCommissionLevel3 is < 0 or > 100)
                    return Json(new DResult { success = false, msg = GetResource("佣金比例范围应在0-100之间") });

                // 根据级别收敛下级比例
                if (DistributionLevel < 3) DistributionCommissionLevel3 = 0;
                if (DistributionLevel < 2) DistributionCommissionLevel2 = 0;

                // 条件金额：单一配置项存储。0=无门槛，>0=金额
                var conditionValue = 0;
                if (DistributionConditions == 1)
                {
                    if (DistributionConditionAmount < 0)
                        return Json(new DResult { success = false, msg = GetResource("历史消费金额需为不小于0的数字") });
                    conditionValue = (int)Math.Round(DistributionConditionAmount, MidpointRounding.AwayFromZero);
                }
                string image = OperationConfig.GetValueByCode("DistributionBackgroundImage");//分销背景图片
                // 上传图片
                if (DistributionBackgroundImage != null && DistributionBackgroundImage.Length > 0)
                {
                    var (ok1, pcUrl, err1) = UploadHelper.ReplaceImageAsWebp(DistributionBackgroundImage, image, "Operation/DistributionActivity");
                    if (!ok1) return Json(new DResult { success = false, msg = GetResource(err1) });
                    image = pcUrl;
                }
                // 写入配置
                bool okFlag = true;
                okFlag &= OperationConfig.UpdateValueByCode("DistributionEnable", DistributionEnable.SafeString(), "分销开关");
                okFlag &= OperationConfig.UpdateValueByCode("DistributionLevel", DistributionLevel.SafeString(), "分销等级");
                okFlag &= OperationConfig.UpdateValueByCode("DistributionShowCommission", DistributionShowCommission.SafeString(), "详情页是否显示分销佣金");
                okFlag &= OperationConfig.UpdateValueByCode("DistributionReturnCommissionEnable", DistributionReturnCommissionEnable.SafeString(), "分销员返佣开关");
                okFlag &= OperationConfig.UpdateValueByCode("DistributionAuditEnable", DistributionAuditEnable.SafeString(), "分销员审核开关");
                okFlag &= OperationConfig.UpdateValueByCode("DistributionConditions", conditionValue.SafeString(), "成为分销员条件");
                okFlag &= OperationConfig.UpdateValueByCode("DistributionBackgroundImage", image, "分销背景图片");
                okFlag &= OperationConfig.UpdateValueByCode("DistributionCommissionLevel1", DistributionCommissionLevel1.SafeString(), "分销1级佣金比例%");
                okFlag &= OperationConfig.UpdateValueByCode("DistributionCommissionLevel2", DistributionCommissionLevel2.SafeString(), "分销2级佣金比例%");
                okFlag &= OperationConfig.UpdateValueByCode("DistributionCommissionLevel3", DistributionCommissionLevel3.SafeString(), "分销3级佣金比例%");

                if (!okFlag) return Json(new DResult { success = false, msg = GetResource("部分配置未找到，保存失败") });

                // 清理缓存
                OperationConfig.Meta.Cache.Clear("", true);

                return Json(new DResult { success = true, msg = GetResource("设置成功") });
            }
            catch (Exception ex)
            {
                return Json(new DResult { success = false, msg = GetResource("异常") + "：" + ex.Message });
            }
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("分销商品")]
        public IActionResult DistributionGoods(string name,int page = 1,int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.name = name;
            viewModel.list = GoodsCommon.SearchDistributionByName(name,pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("DistributionGoods"), new Dictionary<string, string> {
                { "name", name },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("分销员管理")]
        public IActionResult DistributionMember(string searchkey,int page = 1,int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.searchkey = searchkey;
            viewModel.list = Inviter.SearchByKey(searchkey, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("DistributionMember"), new Dictionary<string, string> {
                { "searchkey", searchkey },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("分销员详情")]
        public IActionResult MemberDetail(int id)
        {
            var entity = Inviter.FindById(id);
            if (entity == null)
            {
                return Prompt(new PromptModel() { Message = GetResource("未找到分销员") });
            }
            return View(entity);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("启用推广员")]
        [HttpGet]
        [HttpPost]
    public IActionResult EnableInviter(string ids)
        {
            var parts = ids.SplitAsInt(",");
            if (parts.Length == 0) return Json(new DResult { success = false, msg = GetResource("未选择任何记录") });

            var successCount = 0;
            foreach (var s in parts)
            {

                var entity = Inviter.FindById(s);
                if (entity == null) continue;

                if (entity.State != 1)
                {
                    entity.State = 1; // 已审核/启用
                    entity.Update();
                    successCount++;
                }
            }

            if (successCount == 0)
                return Json(new DResult { success = false, msg = GetResource("操作失败") });

            // 可选：清理缓存
            Inviter.Meta.Cache.Clear("");

            return Json(new DResult { success = true, msg = GetResource("操作成功") + $"，成功{successCount}条" });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("禁用推广员")]
        [HttpGet]
        [HttpPost]
    public IActionResult DisableInviter(string ids)
        {
            var parts = ids.SplitAsInt(",");
            if (parts.Length == 0) return Json(new DResult { success = false, msg = GetResource("未选择任何记录") });

            var successCount = 0;
            foreach (var s in parts)
            {

                var entity = Inviter.FindById(s);
                if (entity == null) continue;

                if (entity.State != 2)
                {
                    entity.State = 2; // 已清退/禁用
                    entity.Update();
                    successCount++;
                }
            }

            if (successCount == 0)
                return Json(new DResult { success = false, msg = GetResource("操作失败") });

            // 可选：清理缓存
            Inviter.Meta.Cache.Clear("");

            return Json(new DResult { success = true, msg = GetResource("操作成功") + $"，成功{successCount}条" });
        }


        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("调整上级")]
        public IActionResult AdjustTheSuperior()
        {
            return View();
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("分销员等级")]
        public IActionResult DistributionMemberLevel()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.list = InviterClass.FindAll();
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("添加等级")]
        public IActionResult AddMemberLevel()
        {
            return View();
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("添加等级")]
        [HttpPost]
        public IActionResult AddMemberLevel(string levelname,decimal amount)
        {
            if (levelname.IsNullOrEmpty())
                return Json(new DResult() { success = false, msg = GetResource("等级名称不能为空") });
            if (amount <= 0)
                return Json(new DResult() { success = false, msg = GetResource("等级限制必须大于0") });
            var entity = new InviterClass();
            entity.Name = levelname;
            entity.Amount = amount;
            entity.Insert();
            //添加翻译表
            if (LocalizationSettings.Current.IsEnable)
            {
                var languageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                foreach (var lang in languageList)
                {
                    // 支持两种键名，未填则回落到主名称
                    var name = (GetRequest($"[{lang.Id}].levelname")).SafeString().Trim();
                    var lan = new InviterClassLan
                    {
                        IId = entity.Id,
                        LId = lang.Id,
                        Name = name
                    };
                    lan.Insert();
                }
            }
            return Json(new DResult() { success = true, msg = GetResource("添加成功") });

        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑等级")]
        public IActionResult EditMemberLevel(int id)
        {
            var entity = InviterClass.FindById(id);
            if (entity == null) return Prompt(new PromptModel() { Message = GetResource("未找到记录") });
            return View(entity);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑等级")]
        [HttpPost]
        public IActionResult EditMemberLevel(int id, string levelname, decimal amount)
        {
            if (id <= 0) return Json(new DResult { success = false, msg = GetResource("参数错误") });
            if (levelname.IsNullOrWhiteSpace()) return Json(new DResult { success = false, msg = GetResource("等级名称不能为空") });
            if (amount <= 0) return Json(new DResult { success = false, msg = GetResource("等级限制必须大于0") });

            var entity = InviterClass.FindById(id);
            if (entity == null) return Json(new DResult { success = false, msg = GetResource("未找到记录") });

            entity.Name = levelname.Trim();
            entity.Amount = amount;
            entity.Update();

            // 更新翻译表
            if (LocalizationSettings.Current.IsEnable)
            {
                var languageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                foreach (var lang in languageList)
                {
                    var name = (GetRequest($"[{lang.Id}].levelname")).SafeString().Trim();
                    var lan = InviterClassLan.FindByIIdAndLId(entity.Id, lang.Id) ?? new InviterClassLan
                    {
                        IId = entity.Id,
                        LId = lang.Id
                    };
                    lan.Name = name;

                    if (lan.Id > 0) lan.Update();
                    else lan.Insert();
                }
            }

            InviterClass.Meta.Cache.Clear("");
            InviterClassLan.Meta.Cache.Clear("");

            return Json(new DResult { success = true, msg = GetResource("编辑成功") });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除等级")]
        public IActionResult DeleteMemberLevel(string ids)
        {
            if (ids.IsNullOrEmpty()) return Json(new DResult { success = false, msg = GetResource("未选择任何记录") });
            var count = InviterClass.DeleteByIds(ids);
            InviterClassLan.DeleteByIIds(ids);
            return Json(new DResult { success = true,msg = GetResource("删除成功")+$"共删除了{count}条记录" });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("分销员订单")]
        public IActionResult DistributionOrder(long storeId,string searchkey, int page = 1,int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.searchkey = searchkey;
            viewModel.storeId = storeId;
            viewModel.storelist = Store.FindAll();
            viewModel.list = OrderInviter.Search(storeId,searchkey, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("DistributionOrder"), new Dictionary<string, string> {
                { "searchkey", searchkey },
                { "storeId", storeId.SafeString() },
            });
            return View(viewModel);
        }
    }
}
