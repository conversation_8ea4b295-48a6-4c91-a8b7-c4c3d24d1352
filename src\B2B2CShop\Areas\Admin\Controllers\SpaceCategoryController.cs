﻿using B2B2CShop.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Webs;

using SkiaSharp;

using System.ComponentModel;
using System.Dynamic;

using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>空间管理</summary>
[DisplayName("空间管理")]
[Description("用于店铺图片空间的管理")]
[AdminArea]
[DHMenu(70, ParentMenuName = "Products", CurrentMenuUrl = "~/{area}/SpaceCategory", CurrentMenuName = "SpaceCategoryList", CurrentIcon = "&#xe72a;", LastUpdate = "20241203")]
public class SpaceCategoryController : PekCubeAdminControllerX {
    /// <summary>
    /// 相册列表
    /// </summary>
    /// <param name="key"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("相册列表")]
    public IActionResult Index(string key, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };

        var Lists = AlbumCategory.FindAllWithCache();
        viewModel.Lists = Lists;

        var list = AlbumCategory.Search(key, pages).Select(x => new { x.Id, x.Name, Count = AlbumPic.FindCount(AlbumPic._.AId == x.Id) }).ToDynamicList();
        viewModel.list = list;
        viewModel.page = page;
        viewModel.key = key;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "key", key } });
        return View(viewModel);
    }

    /// <summary>
    /// 获取是否存在该名称
    /// </summary>
    /// <param name="gc_name"></param>
    /// <param name="gc_id"></param>
    /// <returns></returns>
    public IActionResult GetByNames(string gc_name, int gc_id = -1)
    {
        var Model = AlbumCategory.FindByName(gc_name.SafeString().Trim());

        if (gc_id > 0)
        {
            if (Model != null && Model.Id != gc_id)
            {
                return Json(false);
            }
            else
            {
                return Json(true);
            }
        }
        else
        {
            if (Model != null)
            {
                return Json(false);
            }
            else
            {
                return Json(true);
            }
        }
    }

    /// <summary>
    /// 批量相册删除数据
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    //[HttpPost]
    [DisplayName("相册删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        if (Ids.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("请选择要删除的数据");
            return Json(res);
        }
        var arr = Ids.Split(",");
        var exit = arr.Where(x => x == "1").FirstOrDefault();
        if (exit != null)
        {
            res.msg = GetResource("默认相册无法被删除 请重新选则删除数据");
            return Json(res);
        }

        using (var tran1 = AlbumCategory.Meta.CreateTrans())
        {
            AlbumCategory.DelByIds(Ids.Trim(','));
            AlbumPic.DelByAIds(Ids.Trim(','));
            var List = AlbumPic.FindByAIds(Ids.Trim(','));
            foreach (var item in List)
            {
                item.Cover.AsFile().Delete();
            }

            tran1.Commit();
        }
        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 查看图片列表
    /// </summary>
    /// <param name="keyword"></param>
    /// <param name="AId"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("查看图片列表")]
    public IActionResult Imagelist(string keyword, int AId = -1, int page = 1)
    {

        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };
        var list = AlbumPic.Searchs(AId, keyword, pages).Select(x => new { Id = x.Id, Name = x.Name, CreateTime = x.CreateTime, UpdateUser = x.UpdateUser, UpdateTime = x.UpdateTime, Cover = UrlHelper.Combine(DHSetting.Current.CurDomainUrl, x.Cover), Spec = x.Spec }).ToDynamicList();
        viewModel.list = list;
        viewModel.page = page;
        viewModel.key = keyword;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "keyword", keyword }, { "AId", AId.ToString() } });
        return View(viewModel);
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg(int ItemId, int category_id)
    {
        var filea = Request.Form.Files;
        var list = filea.Count();

        var img = filea.FirstOrDefault();
        var bytes = img!.OpenReadStream().ReadBytes(img.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }
        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(img.FileName)}";
        var filepath = $"AlbumPic/{ManageProvider.User?.ID}/{filename}";
        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
        saveFileName.EnsureDirectory();
        img.SaveAs(saveFileName);
        var Models = new AlbumPic();
        using (var tran1 = AlbumPic.Meta.CreateTrans())
        {
            var image = SKImage.FromEncodedData(img.OpenReadStream());
            var Spec = image.Width + "X" + image.Height;

            Models.FileName = img.FileName;
            Models.Name = filename;
            Models.AId = category_id;
            Models.Cover = $"/{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
            Models.Spec = Spec;
            Models.Insert();
            tran1.Commit();
        }
        return Json(new { success = true, file_id = Models.Id, state = true, origin_file_name = img.FileName, file_name = filename, file_path = UrlHelper.Combine(DHSetting.Current.CurDomainUrl, Models.Cover) });
    }

    /// <summary>
    /// 图片修改名称
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片修改名称")]
    [HttpPost]
    public IActionResult ModifyName(int id, string name)
    {
        var res = new DResult();
        if (name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("名称不能为空");
            return Json(res);
        }

        var exit = AlbumPic.FindByName(name);
        if (exit != null && exit.Id != id)
        {
            res.msg = GetResource("名称已存在");
            return Json(res);
        }

        var Model = AlbumPic.FindById(id);
        if (Model == null)
        {
            res.msg = GetResource("数据不存在或已被删除");
            return Json(res);
        }
        Model.FileName = name;
        Model.Update();
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 相册图片删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    //[HttpPost]
    [DisplayName("相册图片删除")]
    public IActionResult DeletePic(string[] Ids)
    {
        string str = string.Join(",", Ids);
        var List = AlbumPic.FindByIds(str.Trim(','));
        foreach (var item in List)
        {
            item.Cover.AsFile().Delete();
        }
        AlbumPic.DelByIds(str.Trim(','));
        return Prompt(new PromptModel { Message = GetResource("删除成功"), IsOk = true });
    }

    /// <summary>
    /// 编辑相册
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑相册")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult UpdateAlbumCategory(int id)
    {
        var model = AlbumCategory.FindById(id);
        if (model == null)
        {
            return Content(GetResource("相册不存在"));
        }
        return View(model);
    }

    /// <summary>
    /// 编辑相册
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑相册")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult UpdateAlbumCategory(int Id, string Name, string Description, int SortOrder)
    {
        if (Name.IsNullOrWhiteSpace())
        {
            return Json(new DResult { msg = GetResource("相册名称不能为空"), success = false, });
        }

        if (SortOrder < 0)
        {
            return Json(new DResult { msg = GetResource("排序不能小于零"), success = false, });
        }

        var model = AlbumCategory.FindByName(Name);
        if (model != null && model.Id != Id)
        {
            return Json(new DResult { msg = GetResource("相册名称不能重复"), success = false, });
        }

        model = AlbumCategory.FindById(Id);
        if (model == null)
        {
            return Json(new DResult { msg = GetResource("相册不存在"), success = false, });
        }

        model.Name = Name;
        model.Content = Description;
        model.Sort = SortOrder;
        model.Update();

        return Json(new DResult { msg = GetResource("编辑成功"), success = true, });
    }

}
