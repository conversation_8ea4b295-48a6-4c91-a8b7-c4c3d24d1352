﻿@using Pek.IO
@{
    Brand goodBrand = Model;
}
<!-- <div>品牌名称</div>
<div>@goodBrand.Name</div>
<div>名称首字母</div>
<div>@goodBrand.Initial</div>
<div>所属分类ID</div>
<div>@goodBrand.GoodsClassId</div>
<div>所属分类</div>
<div>@goodBrand.BClass</div>
<div>品牌图片标识</div>
<div><img src="@DHSetting.Current.CurDomainUrl/@FileUtil.JoinPath(DHSetting.Current.UploadPath, $"Brand/{goodBrand.Pic}")" onerror="" width="50" height="50" /></div>
<div>展示方式</div>
<div>@(goodBrand.ShowType == 1 ? T("文字") : T("图片"))</div>
<div>是否推荐</div>
<div>@(goodBrand.Recommend ? T("是") : T("否"))</div>
<div>排序</div>
<div>@goodBrand.Sort</div> -->

<div class="page">
    <form id="brand_form" method="post" name="form1" enctype="multipart/form-data">
        <input type="hidden" name="brand_id" value="" />
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120"><label class="validation">品牌名称:</label></td>
                    <td class="vatop rowform"><input type="text" value="@goodBrand.Name" name="brandName" id="brand_name" class="txt">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="validation">名称首字母:</label></td>
                    <td class="vatop rowform"><input type="text" value="@goodBrand.Initial" name="brandInitial" id="brand_initial"
                            class="txt"></td>
                    <td class="vatop tips">商家发布商品快捷搜索品牌使用!</td>
                </tr>
                <tr class="noborder">
                    <td class="required">所属分类:</td>
                    <td class="vatop" id="gcategory">
                        <div id="gcategory" class="default_select">
                            @* 分类checkbox的值（用逗号隔开） *@
                            <input type="hidden" name="id" value="@goodBrand.Id" id="id" />
                            <!-- <input type="hidden" name="class_name" value="" class="mls_name" /> -->
                            <!-- 默认0 -->
                            <input type="hidden" name="subClassificationId" value="@goodBrand.GoodsClassId" id="subClassificationId" />
                            <span id="span">@goodBrand.BClass</span>
                            @* 编辑按钮 *@
                            <span class="err"><button type="button" style="padding: 3px 7px;margin-left: 10px;" onclick="edit_typeSelect(this)">编辑</button></span>
                            <p class="notic"></p>
                            @* <select id="selectTypeId" name="rubbish" data-index="0">
                                <option value="0">@T("所属分类")</option>
                                <option value="0">@T("全部分类")</option>
                            </select> *@
                        </div>
                       
                    </td>
                    <td class="vatop tips">选择分类，可关联大分类或更具体的下级分类。</td>
                </tr>
                <tr class="noborder">
                    <td class="required">品牌图片标识: </td>
                    <td class="vatop rowform">
                        <span class="type-file-box">
                            <input type='text' value="@FileUtil.JoinPath(DHSetting.Current.CurDomainUrl, $"{goodBrand.Pic}")" id='brand_pic' class='type-file-text' />
                                <input type='button' id="button" name="button" value='@T("上传")' class='type-file-button' />
                                <input name="brandPic" type="file" class="type-file-file" id="_pic" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                        </span>
                        @if (!string.IsNullOrWhiteSpace(goodBrand.Pic))
                        {
                             <img class="show_image" src="/static/admin/images/preview.png">
                             <div class="type-file-preview"><img src="@FileUtil.JoinPath(DHSetting.Current.CurDomainUrl, $"{goodBrand.Pic}")"></div>
                            
                        }
                    </td>
                    <td class="vatop tips">品牌LOGO尺寸要求宽度为150像素，高度为50像素、比例为3:1的图片；支持格式gif,jpg,png</td>
                </tr>
                <tr class="noborder">
                    <td class="required">展示方式: </td>
                    <td class="vatop rowform">
                        <input id="brand_showtype_0" type="radio" @(goodBrand.ShowType == 1 ? "" : "checked") value="0" style="margin-bottom:6px;"
                            name="showType" />
                        <label for="brand_showtype_0">图片</label>
                        <input id="brand_showtype_1" type="radio" value="1" style="margin-bottom:6px;"  @(goodBrand.ShowType == 1 ? "checked" : "")
                            name="showType" />
                        <label for="brand_showtype_1">文字</label>
                    </td>
                    <td class="vatop tips">
                        在&ldquo;全部品牌&rdquo;页面的展示方式，如果设置为&ldquo;图片&rdquo;则显示该品牌的&ldquo;品牌图片标识&rdquo;，如果设置为&ldquo;文字&rdquo;则显示该品牌的&ldquo;品牌名&rdquo;!
                    </td>
                </tr>
                <tr class="noborder">
                    <td class="required">是否推荐: </td>
                    <td class="vatop rowform onoff">
                        <label for="brand_recommend1" class="cb-enable  @(goodBrand.Recommend ? "selected" : "" )"
                            title="是"><span>是</span></label>
                        <label for="brand_recommend0" class="cb-disable @(goodBrand.Recommend ? "" : "selected" )" title="否"><span>否</span></label>
                        <input id="brand_recommend1" name="brandRecommend" @(goodBrand.Recommend ? "checked" : "" ) value="1" type="radio">
                        <input id="brand_recommend0" name="brandRecommend"  @(goodBrand.Recommend ? "" : "checked" ) value="0" type="radio">
                    </td>
                    <td class="vatop tips">选择被推荐的图片将在所有品牌列表页&ldquo;推荐品牌&rdquo;位置展现。</td>
                </tr>
                <tr>

                </tr>
                <tr class="noborder">
                    <td class="required">排序: </td>
                    <td class="vatop rowform"><input type="text" value="@goodBrand.Sort" name="sort" id="brand_sort"
                            class="txt"></td>
                    <td class="vatop tips">数字范围为0~255，数字越小越靠前</td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="提交" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script src="/static/plugins/mlselection.js"></script>
<script>
    function edit_typeSelect(dom){
        dom.remove()
        @* 把下面的span标签都remove *@
        if ($('#span').length>0) {
            $('#span').remove();
        }
        @* 在 subClassificationId 后面添加select *@
        $('#gcategory').append('<select id="selectTypeId" name="rubbish" data-index="0"><option value="0" data-isDefault="true">@T("所属分类")</option><option value="0" data-isDefault="true">@T("全部分类")</option></select>');
        @* 初始化分类列表 *@
        initSelection('selectTypeId')
    }
    $("#_pic").change(function () {
        $("#brand_pic").val($(this).val());
    });
    function call_back(picname) {
        $('#brand_pic').val(picname);
    }
    //按钮先执行验证再提交表单
    $(function () {
        // 编辑分类时清除分类信息
        $('.edit_gcategory').click(function () {
            $('input[name="subClassificationId"]').val('');
            $('input[name="brand_class"]').val('');
        });


        jQuery.validator.addMethod("initial", function (value, element) {
            return /^[A-Za-z0-9]$/i.test(value);
        }, "");
        $("#brand_form").validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                brand_name: {
                    required: true,
                    remote: {
                        url: "@Url.Action("ChangeName")?branch=check_brand_name",
                        type: 'get',
                        data: {
                            brand_name: function () {
                                return $('#brand_name').val();
                            },
                            id: ''
                        }
                    }
                },
                brand_initial: {
                    initial: true
                },
                brand_sort: {
                    number: true,
                    range: [0, 255]
                }
            },
            messages: {
                brand_name: {
                    required: '品牌名称不能为空',
                    remote: '该品牌名称已经存在了，请您换一个'
                },
                brand_initial: {
                    initial: '请填写首字母',
                },
                brand_sort: {
                    number: '排序仅可以为数字',
                    range: '数字范围为0~255，数字越小越靠前'
                }
            }
        });
    });
</script>