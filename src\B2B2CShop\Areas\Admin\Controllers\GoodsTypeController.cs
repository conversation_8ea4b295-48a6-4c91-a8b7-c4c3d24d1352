﻿using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>类型管理</summary>
[DisplayName("类型管理")]
[Description("用于商品类型的管理")]
[AdminArea]
[DHMenu(80, ParentMenuName = "Products", CurrentMenuUrl = "~/{area}/GoodsType", CurrentMenuName = "GoodsTypeList", CurrentIcon = "&#xe728;", LastUpdate = "20241218")]
public class GoodsTypeController : PekCubeAdminControllerX {
    /// <summary>
    /// 类型列表
    /// </summary>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("类型列表")]
    public IActionResult Index(int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };

        viewModel.list = GoodType.Searchs(pages);
        viewModel.page = page;

        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> { });
        return View(viewModel);
    }

    /// <summary>
    /// 新增商品类型
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增商品类型")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddType()
    {
        return View();
    }

    /// <summary>
    /// 新增商品类型
    /// </summary>
    /// <param name="classificationId"></param>
    /// <param name="subClassificationId"></param>
    /// <param name="typeName"></param>
    /// <param name="sort"></param>
    /// <param name="brandIds"></param>
    /// <param name="specIds"></param>
    /// <returns></returns>
    [DisplayName("新增商品类型")]
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    public IActionResult AddType(string subClassificationId, string typeName, int sort, string brandIds, string specIds)
    {
        var goodType = new GoodType();
        long classificationId = 0;
        if (subClassificationId.ToLong() > 0)
        {
            classificationId = subClassificationId.ToLong();
            goodType.GoodsClassId = classificationId;
            goodType.GoodsClassName = GoodsClass.FindById(classificationId)?.Name;
            goodType.Name = typeName;
            goodType.Sort = sort;
            goodType.Insert();
        }
        else
        {
            goodType.GoodsClassId = 0;
            goodType.GoodsClassName = "";
            goodType.Name = typeName;
            goodType.Sort = sort;
            goodType.Insert();
        }

        int[] bIds = brandIds.SplitAsInt();
        foreach (var bId in bIds)
        {
            var brandtype = new GoodTypeBrand();
            brandtype.GoodTypeId = goodType.Id;
            brandtype.BrandId = bId;
            brandtype.Insert();
        }

        int[] sIds = specIds.SplitAsInt();
        foreach (var sId in sIds)
        {
            var spectype = new GoodTypeSpec();
            spectype.GoodTypeId = goodType.Id;
            spectype.GoodSpecId = sId;
            spectype.Insert();
        }

        var attributeData = new Dictionary<int, Dictionary<string, string>>();

        foreach (var key in Request.Form.Keys)
        {
            if (key.StartsWith("at_value["))
            {
                XTrace.WriteLine(key);
                var match = System.Text.RegularExpressions.Regex.Match(key, @"at_value\[(\d+)\]\[(\w+)\]");
                if (match.Success)
                {
                    var index = match.Groups[1].Value.ToDGInt();
                    var property = match.Groups[2].Value;
                    var value = Request.Form[key];
                    if (!attributeData.ContainsKey(index))
                    {
                        attributeData[index] = new Dictionary<string, string>();
                    }
                    attributeData[index][property] = value;
                }
            }
        }

        // 按照索引从小到大排序
        var sortedAttributeData = attributeData.OrderBy(kvp => kvp.Key);

        foreach (var kvp in sortedAttributeData)
        {
            var atValues = new GoodAttribute();
            atValues.GoodTypeId = goodType.Id;

            var properties = kvp.Value;

            if (properties.TryGetValue("sort", out var sortValue))
            {
                atValues.Sort = sortValue.ToDGInt();
            }
            if (properties.TryGetValue("name", out var nameValue))
            {
                atValues.Name = nameValue;
            }
            if (properties.TryGetValue("value", out var attrValue))
            {
                atValues.AttrValue = attrValue;
            }
            if (properties.TryGetValue("show", out var showValue))
            {
                atValues.Show = showValue == "on";
            }

            atValues.Insert();

            // Updated code to handle potential null reference for `attrValue`
            var attrValuesArray = attrValue?.Split(',') ?? Array.Empty<string>();
            foreach (var value in attrValuesArray)
            {
                var attrValues = new GoodAttributeValue();
                attrValues.Name = value.Trim();
                attrValues.GoodAttributeId = atValues.Id;
                attrValues.GoodTypeId = goodType.Id;
                attrValues.Insert();
            }
        }

        return MessageTip(GetResource("创建成功"));
    }

    /// <summary>
    /// 修改商品类型
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("修改商品类型")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditType(int id)
    {
        var goodType = GoodType.FindById(id);
        var brandtype = GoodTypeBrand.FindAllByGoodTypeId(id);
        var spectype = GoodTypeSpec.FindAllByGoodTypeId(id);
        var goodAttribute = GoodAttribute.FindAllByGoodTypeId(id);
        ViewBag.GoodAttribute = goodAttribute;
        ViewBag.GoodTypeBrand = brandtype;
        ViewBag.GoodTypeSpec = spectype;
        return View(goodType);
    }

    /// <summary>
    /// 修改商品类型
    /// </summary>
    /// <param name="id"></param>
    /// <param name="classificationId"></param>
    /// <param name="subClassificationId"></param>
    /// <param name="typeName"></param>
    /// <param name="sort"></param>
    /// <param name="brandIds"></param>
    /// <param name="specIds"></param>
    /// <returns></returns>
    [DisplayName("修改商品类型")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult EditType(int id, string subClassificationId, string typeName, int sort, string brandIds, string specIds)
    {
        var goodType = GoodType.FindById(id);
        if (goodType==null)
        {
            return Prompt(new PromptModel { Message = GetResource("商品类型不存在") });
        }
        long classificationId = 0;
        if (subClassificationId.ToLong() > 0)
        {
            classificationId = subClassificationId.ToLong();
            goodType.GoodsClassId = classificationId;
            var goodsClass = GoodsClass.FindById(classificationId);
            goodType.GoodsClassName = goodsClass?.Name;
            goodType.Name = typeName;
            goodType.Sort = sort;
            goodType.Update();
        }
        else
        {
            goodType.GoodsClassId = 0;
            goodType.GoodsClassName = "";
            goodType.Name = typeName;
            goodType.Sort = sort;
            goodType.Update();
            return MessageTip(GetResource("修改成功"));
        }

        //去重
        int[] bIds = brandIds.SplitAsInt();
        var existingBrandIds = GoodTypeBrand.FindAllByGoodTypeId(goodType.Id).Select(bt => bt.BrandId).ToList();
        var newBrandIds = bIds.Except(existingBrandIds).ToList();
        var removedBrandIds = existingBrandIds.Except(bIds).ToList();

        foreach (var bId in newBrandIds)
        {
            var brandtype = new GoodTypeBrand();
            brandtype.GoodTypeId = goodType.Id;
            brandtype.BrandId = bId;
            brandtype.Insert();
        }

        foreach (var bId in removedBrandIds)
        {
            var brandtype = GoodTypeBrand.FindAllByGoodTypeId(goodType.Id).FirstOrDefault(bt => bt.BrandId == bId);
            if (brandtype != null)
            {
                brandtype.Delete();
            }
        }

        int[] sIds = specIds.SplitAsInt();
        var existingSpecIds = GoodTypeSpec.FindAllByGoodTypeId(goodType.Id).Select(st => st.GoodSpecId).ToList();
        var newSpecIds = sIds.Except(existingSpecIds).ToList();
        var removedSpecIds = existingSpecIds.Except(sIds).ToList();

        foreach (var sId in newSpecIds)
        {
            var spectype = new GoodTypeSpec();
            spectype.GoodTypeId = goodType.Id;
            spectype.GoodSpecId = sId;
            spectype.Insert();
        }

        foreach (var sId in removedSpecIds)
        {
            var spectype = GoodTypeSpec.FindAllByGoodTypeId(goodType.Id).FirstOrDefault(st => st.GoodSpecId == sId);
            if (spectype != null)
            {
                spectype.Delete();
            }
        }

        var attributeData = new Dictionary<int, Dictionary<string, string>>();
        var newAttributeData = new Dictionary<int, Dictionary<string, string>>();

        foreach (var key in Request.Form.Keys)
        {
            if (key.StartsWith("ed_value["))
            {
                XTrace.WriteLine(key);
                var match = System.Text.RegularExpressions.Regex.Match(key, @"ed_value\[(\d+)\]\[(\w+)\]");
                if (match.Success)
                {
                    var index = match.Groups[1].Value.ToDGInt();
                    var property = match.Groups[2].Value;
                    var value = Request.Form[key];

                    if (!attributeData.ContainsKey(index))
                    {
                        attributeData[index] = new Dictionary<string, string>();
                    }
                    attributeData[index][property] = value;
                }
            }
            else if (key.StartsWith("at_value["))
            {
                XTrace.WriteLine(key);
                var match = System.Text.RegularExpressions.Regex.Match(key, @"at_value\[(\d+)\]\[(\w+)\]");
                if (match.Success)
                {
                    var index = match.Groups[1].Value.ToDGInt();
                    var property = match.Groups[2].Value;
                    var value = Request.Form[key];

                    if (!newAttributeData.ContainsKey(index))
                    {
                        newAttributeData[index] = new Dictionary<string, string>();
                    }
                    newAttributeData[index][property] = value;
                }
            }
        }

        // 按照索引从小到大排序
        var sortedAttributeData = attributeData.OrderBy(kvp => kvp.Key);
        var sortedNewAttributeData = newAttributeData.OrderBy(kvp => kvp.Key);

        // 处理现有的属性数据
        foreach (var kvp in sortedAttributeData)
        {
            var atValues = GoodAttribute.FindById(kvp.Key);
            if (atValues==null)
            {
                continue;
            }
            var properties = kvp.Value;

            if (properties.TryGetValue("del", out var delValue))
            {
                if (delValue == "on")
                {
                    atValues.Delete();
                    var goodAttributeValue = GoodAttributeValue.FindAllByGoodAttributeId(kvp.Key);
                    goodAttributeValue.Delete();
                    continue;
                }
            }
            if (properties.TryGetValue("sort", out var sortValue))
            {
                atValues.Sort = sortValue.ToDGInt();
            }
            if (properties.TryGetValue("name", out var nameValue))
            {
                atValues.Name = nameValue;
            }
            if (properties.TryGetValue("show", out var showValue))
            {
                atValues.Show = showValue == "on";
            }

            atValues.Save();
        }

        // 处理新增的属性数据
        foreach (var kvp in sortedNewAttributeData)
        {
            var atValues = new GoodAttribute();
            atValues.GoodTypeId = goodType.Id;

            var properties = kvp.Value;

            if (properties.TryGetValue("sort", out var sortValue))
            {
                atValues.Sort = sortValue.ToDGInt();
            }
            if (properties.TryGetValue("name", out var nameValue))
            {
                atValues.Name = nameValue;
            }
            if (properties.TryGetValue("value", out var attrValue))
            {
                atValues.AttrValue = attrValue;
            }
            if (properties.TryGetValue("show", out var showValue))
            {
                atValues.Show = showValue == "on";
            }

            atValues.Insert();

            // 将attrValue按,分割，分为多条插入
            var attrValuesArray = attrValue?.Split(',') ?? Array.Empty<string>();
            foreach (var value in attrValuesArray)
            {
                var attrValues = new GoodAttributeValue();
                attrValues.Name = value.Trim();
                attrValues.GoodAttributeId = atValues.Id;
                attrValues.GoodTypeId = goodType.Id;
                attrValues.Insert();
            }
        }

        return MessageTip(GetResource("修改成功"));
    }

    /// <summary>
    /// 编辑商品属性
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("编辑商品属性")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditAttribute(int id)
    {
        var goodAttribute = GoodAttribute.FindById(id);
        var goodAttributeValue = GoodAttributeValue.FindAllByGoodAttributeId(id);
        ViewBag.GoodAttributeValue = goodAttributeValue;
        return View(goodAttribute);
    }

    /// <summary>
    /// 编辑商品属性
    /// </summary>
    /// <param name="id"></param>
    /// <param name="attrName"></param>
    /// <param name="sort"></param>
    /// <param name="show"></param>
    /// <returns></returns>
    [DisplayName("编辑商品属性")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult EditAttribute(int id, string attrName, int sort, int show)
    {
        var goodAttribute = GoodAttribute.FindById(id);
        if (goodAttribute==null)
        {
            return Prompt(new PromptModel { Message = GetResource("商品属性不存在") });
        }
        goodAttribute.Name = attrName;
        goodAttribute.Sort = sort;
        goodAttribute.Show = show == 1;

        var attrValueData = new Dictionary<int, Dictionary<string, string>>();
        var newAttrValueData = new Dictionary<int, Dictionary<string, string>>();

        foreach (var key in Request.Form.Keys)
        {
            if (key.StartsWith("ed_value["))
            {
                XTrace.WriteLine(key);
                var match = System.Text.RegularExpressions.Regex.Match(key, @"ed_value\[(\d+)\]\[(\w+)\]");
                if (match.Success)
                {
                    var index = match.Groups[1].Value.ToDGInt();
                    var property = match.Groups[2].Value;
                    var value = Request.Form[key];

                    if (!attrValueData.ContainsKey(index))
                    {
                        attrValueData[index] = new Dictionary<string, string>();
                    }
                    attrValueData[index][property] = value;
                }
            }
            else if (key.StartsWith("at_value["))
            {
                XTrace.WriteLine(key);
                var match = System.Text.RegularExpressions.Regex.Match(key, @"at_value\[(\d+)\]\[(\w+)\]");
                if (match.Success)
                {
                    var index = match.Groups[1].Value.ToDGInt();
                    var property = match.Groups[2].Value;
                    var value = Request.Form[key];

                    if (!newAttrValueData.ContainsKey(index))
                    {
                        newAttrValueData[index] = new Dictionary<string, string>();
                    }
                    newAttrValueData[index][property] = value;
                }
            }
        }

        // 按照索引从小到大排序
        var sortedAttrValueData = attrValueData.OrderBy(kvp => kvp.Key);
        var sortedNewAttrValueData = newAttrValueData.OrderBy(kvp => kvp.Key);

        // 处理现有的属性值数据
        foreach (var kvp in sortedAttrValueData)
        {
            var attrValues = GoodAttributeValue.FindById(kvp.Key);
            if (attrValues==null)
            {
                return Prompt(new PromptModel { Message = GetResource("商品属性不存在") });
            }
            var properties = kvp.Value;

            if (properties.TryGetValue("del", out var delValue))
            {
                if (delValue == "on")
                {
                    attrValues.Delete();
                    continue;
                }
            }
            if (properties.TryGetValue("sort", out var sortValue))
            {
                attrValues.Sort = sortValue.ToDGInt();
            }
            if (properties.TryGetValue("name", out var nameValue))
            {
                attrValues.Name = nameValue;
            }

            attrValues.Save();
        }

        // 处理新增的属性值数据
        foreach (var kvp in sortedNewAttrValueData)
        {
            var attrValues = new GoodAttributeValue();
            attrValues.GoodAttributeId = id; // 假设id是GoodAttribute的Id
            attrValues.GoodTypeId = goodAttribute.GoodTypeId;

            var properties = kvp.Value;

            if (properties.TryGetValue("sort", out var sortValue))
            {
                attrValues.Sort = sortValue.ToDGInt();
            }
            if (properties.TryGetValue("name", out var nameValue))
            {
                attrValues.Name = nameValue;
            }

            attrValues.Insert();
        }

        //查找属性值表的值，更新属性表中以逗号分割的值汇总
        var attrValueList = GoodAttributeValue.FindAllByGoodAttributeId(id);
        var attrValue = attrValueList.Select(e => e.Name).Join(",");
        goodAttribute.AttrValue = attrValue;
        goodAttribute.Update();

        return MessageTip(GetResource("修改成功"));
    }

    /// <summary>
    /// 查询商品分类
    /// </summary>
    /// <param name="pid"></param>
    /// <returns></returns>
    [DisplayName("查询商品分类")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetGoodsClass(string pid)
    {
        var goodsClass = GoodsClass.FindAllByParentId(pid.ToLong());

        return Json(goodsClass.Select(gc => new { Id = gc.Id.ToString(), gc.Name }));
    }

    /// <summary>
    /// 获取分类下品牌和规格
    /// </summary>
    /// <param name="classId"></param>
    /// <returns></returns>
    [DisplayName("获取分类下品牌和规格")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetSpecAndBrand(string classId)
    {
        var brand0 = Brand.FindAllByGoodsClassId(0);
        var brand = Brand.FindAllByGoodsClassId(classId.ToLong());
        var spec0 = GoodSpec.FindAllByGoodsClassId(0);
        var spec = GoodSpec.FindAllByGoodsClassId(classId.ToLong());//可能三级

        var brandDict = new Dictionary<string, object>
        {
            ["0"] = brand0,
            [classId] = brand
        };

        //加入全部分类的spec0，可能存在多级分类的spec1,spec2，一定存在的当前分类的spec
        var specDict = new Dictionary<string, object>
        {
            ["0"] = spec0,
        };

        //规格不仅获取全部和最细分类下的，还要获取最细的上层，甚至上上层才到一级，先获取父类
        if (classId.ToLong() > 0)
        {
            var goodsClass = GoodsClass.FindById(classId.ToLong());//假设三级
                                                                   // 如果存在二级分类
            if (goodsClass==null)
            {
                return Prompt(new PromptModel { Message = GetResource("商品分类不存在") });
            }
            if (goodsClass.ParentId > 0)
            {
                var goodsClass2 = GoodsClass.FindById(goodsClass.ParentId);
                if (goodsClass2 == null)
                {
                    return Prompt(new PromptModel { Message = GetResource("商品分类不存在") });
                }
                // 如果存在一级分类
                if (goodsClass2.ParentId > 0)
                {
                    var goodsClass1 = GoodsClass.FindById(goodsClass2.ParentId);
                    if (goodsClass1 == null)
                    {
                        return Prompt(new PromptModel { Message = GetResource("商品分类不存在") });
                    }
                    var spec1 = GoodSpec.FindAllByGoodsClassId(goodsClass1.Id);
                    specDict[goodsClass1.Id.ToString()] = spec1;
                }

                var spec2 = GoodSpec.FindAllByGoodsClassId(goodsClass2.Id);
                specDict[goodsClass2.Id.ToString()] = spec2;
            }
        }

        //最细的当前分类
        specDict[classId] = spec;

        return Json(new { brand = brandDict, spec = specDict });
    }

    /// <summary>
    /// 删除商品类型
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [DisplayName("删除商品类型")]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult Delete(string ids)
    {
        var res = new DResult();
        GoodType.DelByIds(ids);
        GoodType.Meta.Cache.Clear("", true);

        GoodTypeSpec.DelByTypeIds(ids);
        GoodTypeSpec.Meta.Cache.Clear("", true);

        GoodTypeBrand.DelByTypeIds(ids);
        GoodTypeBrand.Meta.Cache.Clear("", true);

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
}
