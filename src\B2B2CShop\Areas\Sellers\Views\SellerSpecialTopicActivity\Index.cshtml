@using B2B2CShop.Entity
@using Pek.Timing
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "SpecialTopic";
    PekHtml.AppendPageCssClassParts("html-sellerspecialtopic-page");
    PekHtml.AppendTitleParts(T("活动列表").Text);
}
@await Html.PartialAsync("_Left")

<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li class="current"><a href="@Url.Action("Index")">@T("活动列表")</a></li>
            </ul>
        </div>
        <div class="p20">
            <table class="dssc-default-table">
                <thead>
                    <tr>
                        <th class="w20">&nbsp;</th>
                        <th class="tl w200">@T("活动主题")</th>
                        <th class="tl">@T("活动说明")</th>
                        <th class="w150">@T("开始时间")</th>
                        <th class="w150">@T("结束时间")</th>
                        <th class="w90">@T("操作")</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (Activity item in Model.list)
                    {
                        <tr>
                            <td></td>
                            <td class="tl"><a target="_blank" href="#">@item.Title</a></td>
                            <td class="tl">@item.Description</td>
                            <td class="goods-time">@UnixTime.ToDateTime(item.StartDate)</td>
                            <td class="goods-time">@UnixTime.ToDateTime(item.EndDate)</td>
                            <td class="dscs-table-handle">
                                <span>
                                    <a id="a_2" href="@Url.Action("Apply", new { aId = item.Id})" class="btn-green">
                                        <i class="iconfont">&#xe731;</i>
                                        <p>@T("参与活动")</p>
                                    </a>
                                </span>
                            </td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="20"><div class="pagination"></div></td>
                    </tr>
                </tfoot>
            </table>
            <ul class="pagination">
                @Html.Raw(Model.PageHtml)
            </ul>
        </div>
    </div>
</div>