@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <table class="ds-default-table">
        <thead>
            <tr>
                <th>@T("会员名")</th>
                <th>@T("参与时间")</th>
                <th>@T("是否中奖")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (MarketActivityLog item in Model.list)
            {
                <tr>
                    <td>@item.MemberName</td>
                    <td>@(UnixTime.ToDateTime(item.LogTime))</td>
                    <td>@(item.Win == 1 ? T("中奖") : T("未中奖"))</td>
                </tr>
            }
        </tbody>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>