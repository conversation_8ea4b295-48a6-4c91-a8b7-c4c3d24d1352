﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("分销设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("DistributionSetting")"><span>@T("分销设置")</span></a></li>
                <li><a href="@Url.Action("DistributionGoods")" class="current"><span>@T("分销商品")</span></a></li>
                <li><a href="@Url.Action("DistributionMember")"><span>@T("分销员管理")</span></a></li>
                <li><a href="@Url.Action("DistributionMemberLevel")"><span>@T("分销员等级")</span></a></li>
                <li><a href="@Url.Action("DistributionOrder")"><span>@T("分销员订单")</span></a></li>
            </ul>
        </div>
    </div>
    <div class="fixed-empty"></div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("商品名称")</dt>
                <dd><input type="text" value="" name="name" id="name" class="txt"></dd>
            </dl>
            <div class="btn_group">
                <a href="javascript:document.formSearch.submit();" id="dssubmit" class="btn " title='@T("查询")'>@T("查询")</a>
            </div>
        </div>
    </form>

    <table class="ds-default-table">
            <thead>
            <tr class="thead">
                <th class="w80"></th>
                <th class="">@T("商品名称")</th>
                <th class="w180">@T("佣金比例")</th>
                <th class="w80">@T("价格")</th>
                <th class="w80">@T("已分销件数")</th>
                <th class="w100">@T("已分销金额")</th>
                <th class="w100">@T("已分销佣金")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (GoodsCommon item in Model.list)
            {
                <tr class="hover edit">
                    <td><div class="pic-thumb"><img src="@(AlbumPic.FindByName(item.GoodsImage ?? ""))" onload="javascript:ResizeImage(this,56,56);" /></div></td>
                    <td>@item.Name</td>
                    <td>
                        <p>@T("佣金比例")@item.InviterRatio %</p>
                    </td>
                    <td>@item.GoodsPrice</td>
                    <td>@item.InviterTotalQuantity</td>
                    <td>@item.InviterTotalAmount</td>
                    <td>@item.InviterAmount</td>
                </tr>
            }
        </tbody>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>