@using B2B2CShop.Entity
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "DiscountSuit";
    PekHtml.AppendPageCssClassParts("html-sellerdiscountsuit-page");
    PekHtml.AppendTitleParts(T("�Ż���װ").Text);
}
@await Html.PartialAsync("_Left")

<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li class="current"><a href="@Url.Action("Index")">@T("活动列表")</a></li>
            </ul>
            <a class="dssc-btn dssc-btn-green" href="@Url.Action("Add")" style="right:100px"><i
                    class="iconfont">&#xe6db;</i>@T("添加活动")</a>
            <a class="dssc-btn dssc-btn dssc-btn-acidblue" href="@Url.Action("BuyPackage")"><i
                    class="iconfont">&#xe6a1;</i>@T("套餐续费")</a>

        </div>
        <div class="p20">
            <!-- 有可用套餐，发布活动 -->
            <div class="alert alert-block mt10">
                @if (Model.quotaTip != "")
                {
                    <strong style="color: #F00;">@Model.quotaTip</strong>
                }
                else
                {
                    <strong>@T("当前没有可用套餐，请先购买套餐")</strong>
                }
                <ul>
                    <li>@T("1、点击购买套餐或续费套餐可以购买或续费套餐")</li>
                    <li>@T("2、")<strong style="color: red">@T("相关费用会在店铺的账期结算中扣除")</strong>@T("。")</li>
                    <li>@T("3、您最多可以发布")<strong>@Model.releaseLimit</strong>@T("个优惠套装。")</li>

                </ul>
            </div>
            <form method="get">
                <table class="search-form">
                    <tr>
                        <td>&nbsp;</td>
                        <th>@T("活动状态")</th>
                        <td class="w100">
                            <select name="state">
                                <option value='-1'>@T("全部状态")</option>
                                <!option value='0' @(Model.state == 0 ? "selected" : "")>@T("关闭")</!option>
                                <!option value='1' @(Model.state == 1 ? "selected" : "")>@T("开启")</!option>
                            </select>
                        </td>
                        <th class="w110">@T("活动名称")</th>
                        <td class="w160"><input type="text" class="text w150" name="name" value="@Model.name" /></td>
                        <td class="w70 tc">
                            <input type="submit" class="submit" value='@T("搜索")' />
                        </td>
                    </tr>
                </table>
            </form>

            <table class="dssc-default-table">
                <thead>
                    <tr>
                        <th class="w10"></th>
                        <th class="w50"></th>
                        <th class="tl">@T("活动名称")</th>
                        <th class="w180">@T("优惠套装价格")</th>
                        <th class="w180">@T("商品数量")</th>
                        <th class="w90">@T("状态")</th>
                        <th class="w110">@T("操作")</th>
                    </tr>
                    <tr>
                        <td class="w30 tc"><input type="checkbox" id="all" class="checkall" /></td>
                        <td colspan="20">
                            <label for="all">@T("全选")</label>
                            <a href="javascript:void(0);" class="dssc-btn-mini" ds_type="batchbutton"
                               uri="@Url.Action("Delete")" name="ids"
                                confirm='@T("您确定要删除吗?")'><i class="iconfont">&#xe725;</i>@T("删除")</a>
                        </td>
                    </tr>
                </thead>
                <tbody>
                    @foreach (Bundling item in Model.list)
                    {
                        <tr class="bd-line" id="<EMAIL>">
                            <td><input type="checkbox" class="checkitem tc" value="@item.Id" /></td>
                            <td>
                                <div class="pic-thumb">
                    
                                    <a href="@Url.Action("Index","Goods",new {Area = "",skuId = item.DefaultGoods.GoodsSkuId})" target='_blank'>
                                        <img src="@AlbumPic.FindByName(item.DefaultGoods.GoodsSku.GetDefaultGoodsImage())?.Cover" />
                                    </a>
                                </div>
                            </td>
                            <td class="tl">
                                <dl class="goods-name">
                                    <dt>
                                        <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.DefaultGoods.GoodsSkuId })" target="_blank">@item.Name</a>
                                    </dt>
                                </dl>
                            </td>
                            <td class="goods-price">@item.DiscountPrice</td>
                            <td class="">@item.GoodsCount</td>
                            <td>@(item.State==1?T("开启"):T("关闭"))</td>
                            <td class="dscs-table-handle">
                                <span>
                                    <a href="@Url.Action("Manage",new {id = item.Id})"
                                       class="btn-blue">
                                        <i class="iconfont">&#xe734;</i>
                                        <p>@T("管理")</p>
                                    </a>
                                </span> <span>
                                    <a class="btn-red" href='javascript:void(0);'
                                       onclick="ds_ajaxget_confirm('@Url.Action("Delete", new { ids = item.Id })','@T("您确定要删除吗?")','remove','@item.Id');">
                                        <i class="iconfont">&#xe725;</i>
                                        <p>@T("删除")</p>
                                    </a>
                                </span>
                            </td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr>
                        <th class="tc"><input type="checkbox" id="all" class="checkall" /></th>
                        <th colspan="20">
                            <label for="all">@T("全选")</label>
                            <a href="javascript:void(0);" class="dssc-btn-mini" ds_type="batchbutton"
                               uri="@Url.Action("Delete")" name="ids"
                                confirm='@T("您确定要删除吗?")'><i class="iconfont">&#xe725;</i>@T("删除")</a>
                        </th>
                    </tr>
                    <tr>
                        <td colspan="20">
                            <div class="pagination"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <ul class="pagination">
                @Html.Raw(Model.PageHtml)
            </ul>

        </div>
    </div>
</div>