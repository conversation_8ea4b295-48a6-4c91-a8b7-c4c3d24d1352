﻿@using B2B2CShop.Entity
@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{

    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "Bargain";
    PekHtml.AppendPageCssClassParts("html-sellerbargain-page");
    PekHtml.AppendTitleParts(T("砍价记录").Text);
    BargainOrder entity = Model.entity;
}
@await Html.PartialAsync("_Left")

<div class="seller_right">
    <div class="seller_items">
        <ul>
            <li><a href="@Url.Action("")">@T("砍价活动列表")</a></li>
            <li class="current"><a href="@Url.Action("BargainsLog", new { orderId = entity.Id })">@T("砍价记录")</a></li>
        </ul>

    </div>
    <div class="p20">


        <table class="dssc-default-table">
            <thead>
                <tr>
                    <th class="w80">@T("砍价者用户名")</th>
                    <th class="w80">@T("砍掉价格")</th>
                    <th class="w80">@T("砍价时间")</th>
                </tr>
            </thead>
            <tbody>
                @if (Model.list.Count > 0)
                {
                    foreach (BargainLog item in Model.list)
                    {
                        <tr class="hover">
                            <td>@item.MemberName</td>
                            <td>@item.Price</td>
                            <td>@(UnixTime.ToDateTime(item.AddTime))</td>
                        </tr>
                    }
                }
                else
                {
                    <tr>
                        <td class="norecord" colspan="20">
                            <div class="warning-option">
                                <i class="iconfont">&#xe64c;</i><span>@T("暂无符合条件的数据记录")</span>
                            </div>
                        </td>
                    </tr>
                }
                
            </tbody>
            <tfoot>
            </tfoot>
        </table>


    </div>
</div>
