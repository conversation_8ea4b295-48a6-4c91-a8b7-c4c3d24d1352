﻿@model List<MenuTree>
@{
    Layout = null;
    var user = ViewBag.User as IUser ?? User.Identity as IUser;
    var adminarea = DHSetting.Current.AdminArea.ToLower();

    var localizationSettings = LocalizationSettings.Current;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>@T(NewLife.Common.SysConfig.Current.DisplayName)</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="~/static/admin/css/admin.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/static/plugins/js/jqueryui/jquery-ui.min.css" asp-append-version="true">
    <script src="~/static/plugins/jquery214.min.js" asp-append-version="true"></script>
    <script src="~/static/plugins/jquery.validate.min.js" asp-append-version="true"></script>
    <script src="~/static/plugins/jquery.cookie.js" asp-append-version="true"></script>
    <script src="~/static/plugins/common.js" asp-append-version="true"></script>
    <script src="~/static/admin/js/admin.js" asp-append-version="true"></script>
    <script src="~/static/plugins/js/jqueryui/jquery-ui.min.js" asp-append-version="true"></script>
    <script src="~/static/plugins/js/jqueryui/jquery.ui.datepicker-zh-CN.js" asp-append-version="true"></script>
    <script src="~/static/plugins/perfect-scrollbar.min.js" asp-append-version="true"></script>
    <script src="~/static/plugins/layer/layer.js" asp-append-version="true"></script>
    <script src="~/static/plugins/js/layui/layui.js" asp-append-version="true"></script>
    <link rel="stylesheet" href="~/static/plugins/js/layui/css/layui.css" asp-append-version="true"></link>
    <script type="text/javascript">
        var HOMESITEURL = "@DHSetting.Current.CurDomainUrl";
        var ADMINSITEURL = "/@adminarea";
    </script>
</head>
<body>
    <div id="append_parent"></div>
    <div id="ajaxwaitid"></div>

    <div class="admincp-header">
        <div class="logo">
            <img src="~/static/admin/images/backlogo.png" />
        </div>
        <div class="navbar">
            <ul class="fl" style="float:left;">
                @foreach (var menu in Model)
                {
                    <li id="<EMAIL>()">
                        <a href="javascript:void(0)" onclick="openItem('@menu.Name.ToLower()')">@menu.DisplayName</a>
                    </li>
                }
            </ul>
            <ul class="fr" style="float:right">
                @* xavier 菜单 *@
                 <style>
                    .xavierMenu{
                        position: relative;
                        color: white;
                    }
                    .xavier{
                        position: absolute;
                        top: 0;
                        left: -155px;
                        padding: 10px 20px;
                        display: none;
                        @* border: 1px solid red; *@
                    }
                    .xavier>div{
                        width: 100px;
                        padding: 10px;
                        background-color: #5bdee3;
                        border-bottom: 1px solid white;
                        position: relative;
                        
                    }
                    @* .xavier>div:first-child{border-radius: 0px 11px 0px 0px;} *@
                    .xavier>div:first-child::after{
                        content: '▲';
                        position: absolute;
                        top: 0px;
                        right: -11px;
                        color: #5bdee3;
                        transform: rotateZ(85deg);
                        font-size: 20px;
                        z-index: -1;
                    }
                    .xavierTitle{cursor: pointer;}
                    .xavierMenu:hover .xavier{display: block;}
                    .xavier>div:hover{background-color:#33d7dc!important}
                </style>
                <li>
                    <span>@T("您好,")@user?.Name</span>
                    <div class="sub-meun">
                        <dl style="overflow: unset !important;">
                            <dd><a href="/" target="_blank"> <i class="layui-icon">&#xe68e;</i> @T("访问首页")</a></dd>
                            <dd>
                                <a href="javascript:dsLayerConfirm('@Url.Action("ClearCache", "Home", new { area = adminarea } )','@T("确定清理全部缓存?")')" 
                                target="main-frame"> <i class="layui-icon">&#xe640;</i> @T("清除缓存")</a>
                            </dd>
                            @* <dd class="xavierMenu">
                                测试
                                <div class="xavier">
                                    <div>asdsadsa</div>
                                    <div>asdsadsa</div>
                                </div>
                            </dd> *@
                            <dd class="xavierMenu">
                                <span class="xavierTitle"> <i class="layui-icon">&#xe7ae;</i> Language</span>
                                <div class="xavier">
                                   @foreach (var item in LanguageList)
                                    {
                                        <div><a href='@Url.Action("DashBoard", new { lang = item.UniqueSeoCode })'>@item.DisplayName</a></div>
                                    }
                                </div>
                            </dd>
                            <dd><a href="@Url.Action("ModifyPw", "Home", new { area = adminarea } )" target="main-frame"><i class="iconfont">&#xe67b;</i>@T("修改密码")</a></dd>
                            <dd><a href="javascript:dsLayerConfirm('@Url.Action("Index", "Logout", new { area = adminarea, ReturnState = 0, r = Url.Action("Index", "Login", new { area = adminarea }) } )','@T("您确定退出吗?")', 'logout')"><i class="iconfont">&#xe70c;</i>@T("安全退出")</a></dd>
                        </dl>
                    </div>
                </li>
                <li style="width: 50px;opacity: 0;"></li>
                @* 原版写法
                @if (localizationSettings.IsEnable)
                {
                    <li>
                        <span>Language</span>
                        <div class="sub-meun">
                            <dl>
                                @foreach (var item in Languagelist)
                                {
                                    <dd><a href='@Url.Action("DashBoard", new { lang = item.UniqueSeoCode })'>@item.DisplayName</a></dd>
                                }
                            </dl>
                        </div>
                    </li>
                }
                <li><a href="javascript:dsLayerConfirm('@Url.Action("ClearCache", "Home", new { area = adminarea } )','@T("确定清理全部缓存?")')" target="main-frame">@T("清除缓存")</a></li>
                <li><a href="/" target="_blank">@T("访问首页")</a></li> *@
            </ul>
        </div>
    </div>

    <div class="admincp-container">
        <div class="admincp-container-left">
            <div id="mainMenu">
                @{
                    var i = 0;
                }
                @foreach (var menu in Model)
                {
                    i++;
                    <ul id="<EMAIL>()" @(i == 1 ? Html.Raw("") : Html.Raw("style=\"display:none\""))>
                        @if (menu.Children != null && menu.Children.Count > 0)
                        {
                            @foreach (var item in menu.Children)
                            {
                                var url = item.Url.Replace($"/{adminarea}/", "");
                                var urlArray = url.Split('/');

                                var action = "";
                                var controller = "";

                                if (urlArray.Length == 1)
                                {
                                    action = "index";
                                }
                                else
                                {
                                    action = urlArray[1];
                                }
                                controller = urlArray[0];

                                <li id="left_@(menu.Name.ToLower())@(controller)@(action)"><a href="javascript:void(0)" onclick="openItem('@action,@controller,@menu.Name.ToLower()')"><i class="iconfont">@Html.Raw(item.Icon)</i>@item.DisplayName</a></li>
                            }
                        }
                    </ul>
                }
            </div>

        </div>
        <div class="admincp-container-right">
            <div class="top-border"></div>
            <iframe src="@Url.Action("Index", "Main")" id="main-frame" name="main-frame" style="overflow: visible; width: 100%; height:100%;" frameborder="0" scrolling="yes" onload="window.parent"></iframe>
        </div>
    </div>
    <script>
        $(function () {
            $('#welcome,dashboard,dashboard').addClass('active');
            if ($.cookie('now_location_controller') != null) {
                openItem($.cookie('now_location_action') + ',' + $.cookie('now_location_controller') + ',' + $.cookie('now_location_module'));
            } else {
                $('#mainMenu>ul').first().css('display', 'block');
                //第一次进入后台时，默认定到欢迎界面
                $('#item_welcome').addClass('selected');
                $('#workspace').attr('src', ADMINSITEURL + '@Url.Action("Index", "Main")');
            }
            $('#iframe_refresh').click(function () {
                var fr = document.frames ? document.frames("workspace") : document.getElementById("workspace").contentWindow;
                fr.location.reload();
            });
        });


        function openItem(args) {
            spl = args.split(',');
            action = spl[0];
            try {
                controller = spl[1];
                module = spl[2];
            }
            catch (ex) {
            }
            if (typeof (controller) == 'undefined') {
                var module = args;
            }
            //顶部导航样式处理
            $('.actived').removeClass('actived');
            $('#nav_' + module).addClass('actived');
            //清除左侧样式
            $('.selected').removeClass('selected');

            //show
            $('#mainMenu ul').css('display', 'none');
            $('#sort_' + module).css('display', 'block');
            if (typeof (controller) == 'undefined') {
                //顶部菜单事件
                html = $('#sort_' + module + '>li').first().html();
                str = html.match(/openItem\('(.*)'\)/ig);
                arg = str[0].split("'");
                spl = arg[1].split(',');
                action = spl[0];
                controller = spl[1];
                module = spl[2];
                first_obj = $('#sort_' + module + '>li').first();
                $(first_obj).addClass('selected');
            } else {
                //左侧菜单事件
                //location
                $.cookie('now_location_module', module);
                $.cookie('now_location_controller', controller);
                $.cookie('now_location_action', action);
                $("#left_" + module + controller + action).addClass('selected');

            }

            if (action == "index") {
                src = ADMINSITEURL + '/' + controller;
            }
            else {
                src = ADMINSITEURL + '/' + controller + '/' + action;
            }

            $('#main-frame').attr('src', src);
        }
    </script>
</body>
</html>