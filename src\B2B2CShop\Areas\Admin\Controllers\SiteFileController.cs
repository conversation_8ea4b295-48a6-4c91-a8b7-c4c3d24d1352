﻿using Microsoft.AspNetCore.Mvc;

using NewLife;
using Pek;
using Pek.DsMallUI;
using Pek.IO;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;

using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>
/// 系统文件管理
/// </summary>
[DisplayName("系统文件")]
[Description("系统文件管理")]
[AdminArea]
[DHMenu(23, ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/SiteFile", CurrentMenuName = "SiteFileList", CurrentIcon = "&#xe63e;", LastUpdate = "20241207")]
public class SiteFileController : PekCubeAdminControllerX {
    /// <summary>
    /// 系统文件管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("系统文件管理")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>系统文件列表</summary>
    /// <param name="Path">文件夹路径。默认为空，即根目录</param>
    /// <returns></returns>
    [DisplayName("系统文件列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetList(String Path)
    {
        DirectoryInfo DirectoryPath;
        if (Path.IsNullOrWhiteSpace())
        {
            var dir = AppContext.BaseDirectory;
            DirectoryPath = dir.AsDirectory();
        }
        else
        {
            DirectoryPath = Path.AsDirectory();
        }

        var list = FileSystemObject.GetDirectoryInfos(DirectoryPath.FullName, FsoMethod.All).OrderBy(e => e.type).ThenBy(e => e.name);

        return Json(new
        {
            code = 0,
            msg = "success",
            count = list.Count(),
            images = list.Select(e =>
            {
                return e;
            })
        });
    }

    /// <summary>
    /// 系统日志列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("系统日志列表")]
    public IActionResult GetInfo(String Path)
    {
        //if (string.IsNullOrEmpty(Path) || !Path.StartsWith("Log"))
        //{
        //    return BadRequest("路径不合法，必须以 'Log' 开头");
        //}
        if (string.IsNullOrEmpty(Path))
        {
            return BadRequest("路径不合法");
        }

        var File = Path.AsFile();

        var Content = File.ReadString(true);

        ViewBag.Content = Content;
        return View();
    }

    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("下载系统日志")]
    public IActionResult Download(string path)
    {
        if (string.IsNullOrEmpty(path) || !path.StartsWith("Log"))
        {
            return BadRequest("路径不合法，必须以 'Log' 开头");
        }

        var file = path.AsFile();
        if (!file.Exists)
        {
            return NotFound("文件不存在");
        }
        var fileName = file.Name;
        var contentType = "application/octet-stream";

        // 打开文件流，指定共享模式为 ReadWrite
        var fs = new FileStream(file.FullName, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
        return File(fs, contentType, fileName);
    }
}
