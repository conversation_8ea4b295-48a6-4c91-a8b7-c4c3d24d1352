
@{       // 页面标题
    PekHtml.AddTitleParts(T("定时任务").Text);
}

<style >
    thead{
        text-align:center;
    }
    .statusedit{
        margin-left:20px;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3 class="ny-panel-title">@("定时任务")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("Add")"><span>@T("新增")</span></a></li>
            </ul>
  
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("名称/显示名")</dt>
                <input type="text" id="key" name="key">
             
            </dl>
            <!-- <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")" onclick="Search()">
            </div> -->
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr style="text-align:center;">
                <th class="w24"></th>
                <th>@T("编号")</th>
                <th>@T("名称")</th>
                <th>@T("显示名")</th>
                <th>@T("Cron表达式")</th>
                <th>@T("是否启用")</th>
                <th>@T("最后时间")</th>
                <th>@T("下一次时间")</th>
                <th>@T("更新者")</th>
                <th>@T("更新时间")</th>
                <th>@T("更新地址")</th>
                <th style="text-align:center;">@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.list)
            {
            <tr id="<EMAIL>">

                <td><input type="checkbox" class="checkitem" value="@item.Id" /></td>
                <td>@item.Id</td>
                <td>@item.Name</td>
                <td>@item.DisplayName</td>
                <td>@item.Cron</td>
                <td>
                    <div class="onoff" data-id="@item.Id">
                        <label for="State1" onclick="State1('@item.Id')" class="cb-enable @(item.Enable?"selected":"")">是</label>
                        <label for="State0" onclick="State0('@item.Id')"  class="cb-disable @(!item.Enable?"selected":"")">否</label>
                        <input id="State1" name="State1" value="true" data-id="@item.Id"  type="radio" @(item.Enable ? "checked" : "")>
                        <input id="State0" name="State0" value="false" data-id="@item.Id" type="radio" @(!item.Enable ? "checked" : "")>
                    </div>
                </td>
                <td>
            
                   @if (item.LastTime != null && item.LastTime.ToString()[0] != '0'){
                        @item.LastTime
                   }
                    
                </td>
                <td>               
                    @if (item.NextTime != null && item.NextTime.ToString()[0] != '0')
                    {
                        @item.NextTime
                    }
                </td>
                <td>@item.UpdateUser</td>
                <td>@item.UpdateTime</td>
                <td>@item.UpdateIP</td>
                <td>
                    <a class="dsui-btn-edit"  href="@Url.Action("Edit",new { Id=item.Id})"><i class="iconfont"></i>@T("编辑")</a>
                    <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { Ids = item.Id })','您确定要删除吗?',@item.Id)" class="dsui-btn-del">
                        <i class="iconfont"></i>@T("删除")
                    </a>
                    <a class="dsui-btn-add" onclick="onExecute('@item.Id')">@T("立即执行")</a>
                </td>
            </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom"><span>@T("全选")</span></label>
                     &nbsp;&nbsp;<a href="javascript:;" class="btn btn-small" onclick="delete_multy()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>

</div>

<script asp-location="Footer">

        // 防抖函数
        function debounce(func, delay) {
            let timeoutId;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(context, args);
                }, delay);
            };
        }

        // 01.监听input输入进行筛选数据
        $('#key').on('input', debounce((data)=>{
            document.getElementById('formSearch').submit();
        }, 300));
        
        // 02-1.使用jQuery来监听选择框的选择事件
        // 哥哥的label 挡住了是否
        function State1(id) {
            changeCheckboxStatus(id,true)
        }
        // 02-2.哥哥的label 挡住了是否
        function State0(id) {

            changeCheckboxStatus(id,false)
            
        }
        // 改变选择状态
        const changeCheckboxStatus = (Id,status)=>{
            $.post("@Url.Action("ModifyState")",{Id,status},function (res) {
                if (res.success) {
                    if (status) {
                        layui.layer.msg('启用成功')
                    }else{
                        layui.layer.msg('已取消')
                    }
                    return;
                }
                layui.layer.msg(res.msg)
                debugger;
            }).fail(function(err) {
                console.log('选择失败！',err);
                // 在这里处理错误请求或者代码报错
               layui.layer.msg('选择失败！')
            });
        }   

        // 立即执行-api
        const onExecute = (Id)=>{
            $.get("@Url.Action("ExecuteNow")",{Id},function (res) {
                if (res.success) {
                    layui.layer.msg(res.msg, { time: 1000 }, function() {
                        location.reload();
                    });
                    return;
                }
                debugger;
            }).fail(function(err) {
                console.log('下发失败！',err);
                // 在这里处理错误请求或者代码报错
               layui.layer.msg('下发失败！')
            });
        } 
        // s删除多个
        const delete_multy = () =>{
            let dom = $('.checkitem')
            let Ids = ''
            for (let i = 0; i < dom.length; i++) {
                if (dom[i].checked) {
                    console.log($(dom[i]).val());
                    Ids += (  $(dom[i]).val() +','  ) 
                }
                
            }
            if (Ids == '') {
                layui.layer.msg('您尚未选择任意项',{icon:5,time:3000})
                return;
            }
            // 一切ok就调用删除
            _uri = "@Url.Action("Delete")?Ids=" + Ids;
            dsLayerConfirm(_uri, '您确定要删除吗?');
            
            
        }
</script>