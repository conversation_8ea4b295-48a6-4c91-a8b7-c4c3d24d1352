﻿<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("国际快递物流")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="/biz/InternationalExpress" class="current"><span>@T("管理")</span></a></li>
                <li><a href="/biz/InternationalExpress/Add"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>

    <form method="get" action="@Url.Action("Index")">
        <div class="ds-search-form">
            <dl>
                <dt>@T("公司名称")</dt>
                <dd><input type="text" name="name" value="@Model.name" class="txt"></dd>
            </dl>
            <dl>
                <dt>@T("公司代码")</dt>
                <dd><input type="text" name="code" value="@Model.code" class="txt"></dd>
            </dl>
            <div class="btn_group">
                <button type="submit" class="btn">@T("搜索")</button>
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th></th>
                <th>@T("公司ID")</th>
                <th>@T("公司名称")</th>
                <th>@T("公司代码")</th>
                <th>@T("状态")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (InternationalExpress item in Model.list)
            {
                <tr>
                    <th><input type="checkbox" class="checkitem" value="@item.Id"></th>
                    <td>@item.Id</td>
                    <td>@item.Name</td>
                    <td>@item.Key</td>
                    <td>
                        @if (item.Status)
                        {
                            <p>@T("启用")</p>
                        }
                        else
                        {
                            <p class="red">@T("禁用")</p>
                        }
                    </td>
                    <td>
                        <a href="@Url.Action("Edit", new { Id= item.Id })" class="dsui-btn-edit">
                            <i class="iconfont"></i>@T("编辑")
                        </a>
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete")?ids=' + @item.Id + '','@T("您确定要删除吗?")')" class="dsui-btn-del">
                            <i class="iconfont"></i>@T("删除")
                        </a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small"
                                   onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>

<script asp-location="Footer">
    function submit_delete(items) {
        _uri = "@Url.Action("Delete")?ids=" + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
    }
</script>
