﻿@{
    MerchantMaterial merchantMaterial = Model;
}
@* @merchantMaterial.CreateIP
@merchantMaterial.CreateTime
@merchantMaterial.Id
@merchantMaterial.Name
@merchantMaterial.Remark
@merchantMaterial.UpdateIP
@merchantMaterial.UpdateTime
@merchantMaterial.CreateUser
@merchantMaterial.CreateUserID
@merchantMaterial.Enabled
@merchantMaterial.UpdateUser
@merchantMaterial.UpdateUserID
@merchantMaterial.Store
@merchantMaterial.StoreId
@merchantMaterial.StoreName *@
<style>
    .w80{width: 80px;}
</style>
<div class="page">
        <table border="0" cellpadding="0" cellspacing="0" class="ds-default-table">
       <thead>
           <tr>
               <th colspan="20">@T("物料信息")</th>
           </tr>
       </thead>
       <tbody>
           <tr>
               <th class="w80">@T("名称")</th>
               <td colspan="20">@merchantMaterial.Name</td>
           </tr>
           <tr>
               <th class="w80">@T("重量")</th>
               <td colspan="20">@merchantMaterial.Weight kg</td>
           </tr>
           <tr>
               <th class="w80">@T("体积")</th>
                <td colspan="20">@merchantMaterial.Volume cm³ @T("长*宽*高") (@merchantMaterial.Length cm * @merchantMaterial.Width cm * @merchantMaterial.Height cm)</td>
           </tr>
           <tr>
               <th class="w80">@T("成本价")</th>
               <td colspan="20">@merchantMaterial.CostPrice</td>
           </tr>
           <tr>
               <th class="w80">@T("备注")</th>
               <td colspan="20">@merchantMaterial.Remark</td>
           </tr>
           <tr>
               <th class="w80">@T("店铺名称")</th>
               <td colspan="20">@merchantMaterial.StoreName</td>
           </tr>
            <tr>
               <th class="w80">@T("是否启用")</th>
               <td colspan="20">@(merchantMaterial.Enabled ? T("是") : T("否"))</td>
           </tr>
            <tr>
               <th class="w80">@T("创建者")</th>
               <td colspan="20">@merchantMaterial.CreateUser</td>
           </tr>
           <tr>
               <th class="w80">@T("创建日期")</th>
               <td colspan="20">@merchantMaterial.UpdateTime</td>
            </tr>
            <tr>
                <th class="w80">@T("物料图片")</th>
                <td colspan="20">
                    <div class="material-image-container">
                        @{
                            var images = merchantMaterial.Images?.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList() ?? new List<string>();
                            if (images.Count > 0)
                            {
                                foreach (var item in images)
                                {
                                    var imageUrl = AlbumPic.FindByNameAndSId(item, Model.StoreId)?.Cover;
                                    if (imageUrl != null)
                                    {
                                        <a href="@imageUrl" data-lightbox="material-images" class="">
                                            <img src="@imageUrl" alt="@Model.Name" style="max-width: 100px; max-height: 100px;">
                                        </a>
                                    }
                                }
                            }
                        }
                    </div>
                    <p style="margin: 10px; color: #999;">@T("点击图片进行预览")</p>
                </td>
            </tr>
       </tbody>
   </table>
</div>
<link rel="stylesheet" href="~/static/plugins/js/jquery.lightbox/css/lightbox.min.css">
<script src="~/static/plugins/js/jquery.lightbox/js/lightbox.min.js"></script>
<script>

</script>