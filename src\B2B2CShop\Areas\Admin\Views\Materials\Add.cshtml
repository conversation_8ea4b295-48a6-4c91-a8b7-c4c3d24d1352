﻿@{
    // script
    PekHtml.AppendScriptParts(ResourceLocation.Head, "/static/plugins/js/layui/layui.js");
    IEnumerable<SelectListItem> storeList = Model.storeList;
    // css
    PekHtml.AppendCssFileParts("/static/plugins/js/layui/css/layui.css");
}
<style asp-location="true">
    .red {
        color: red
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("新增-物料")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("Add")" class="current"><span>@T("新增")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="post" action="@Url.Action("Add")" id="doc_form">
        <div class="ds-default-form">
            <dl>
                <dt><span class="red">*</span>@T("物料名称")</dt>
                <dd><input type="text" name="name" class="txt"></dd>
            </dl>
            <dl>
                <dt>@T("所属店铺")</dt>
                <dd>
                    <select id="store" name="storeId">
                        <!option value="0">
                            @T("默认")
                        </!option>
                        @foreach (SelectListItem item in storeList)
                        {
                            <!option value="@item.Value" @(item.Selected ? "selected" : "")>
                                @item.Text
                            </!option>
                        }
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("备注")</dt>
                <dd><textarea class="layui-textarea" name="remark"></textarea></dd>
            </dl>
            <div class="btn_group">
                <button type="submit" class="btn">@T("保存")</button>
            </div>
        </div>
    </form>
</div>
<script asp-location="Footer">
     $('#doc_form').validate({
        errorPlacement: function (error, element) {
            // console.log();
            error.appendTo(element.parent().parent().find('dd:first'));
        },
         rules: {
             name : {
                required: true
             },
        },
        messages: {
            name : {
                required: '@T("物料名称不能为空")'
            },
        }
    });
</script>
