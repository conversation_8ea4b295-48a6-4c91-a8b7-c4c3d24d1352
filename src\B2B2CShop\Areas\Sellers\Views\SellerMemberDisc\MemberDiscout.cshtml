@using B2B2CShop.Entity
@using B2B2CShop.Entity
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "MemberDisc";
    PekHtml.AppendPageCssClassParts("html-sellermemberdisc-page");
    PekHtml.AppendTitleParts(T("会员等级折扣").Text);
    Store store = Model.store;
    var mgDiscounts = store.MgDiscountDto;
}
@await Html.PartialAsync("_Left")


<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li class="current"><a href="@Url.Action("MemberDiscout")">@T("设置会员等级折扣")</a></li>
                <li><a href="@Url.Action("GoodsDiscout")">@T("设置商品会员等级折扣")</a></li>
            </ul>
            <a class="dssc-btn dssc-btn-acidblue" href="@Url.Action("BuyPackage")" title=""><i
                    class="iconfont">&#xe6a1;</i>@T("购买套餐")</a>

        </div>
        <div class="p20">
            <div class="alert alert-block mt10">
                @if (Model.quotaTip != "")
                {
                    <strong style="color: #F00;">@Model.quotaTip</strong>
                }
                else
                {
                    <strong>@T("当前没有可用套餐，请先购买套餐")</strong>
                }
                <ul>
                    <li>@T("1、点击购买套餐和套餐续费按钮可以购买或续费套餐")</li>
                    <li>@T("2、点击添加活动按钮可以添加会员等级折扣活动，点击管理按钮可以对会员等级折扣活动内的商品进行管理")</li>
                    <li>@T("3、点击删除按钮可以删除会员等级折扣活动")</li>
                    <li>@T("4、")<strong style="color: red">@T("相关费用会在店铺的账期结算中扣除")</strong>@T("。")</li>
                </ul>
            </div>




            <div class="dssc-form-default">
                <form id="add_form" action="@Url.Action("SetMemberDiscout")" method="post"
                    enctype="multipart/form-data">
                    <dl>
                        <dt><i class="required">*</i>@T("是否开启会员折扣")</dt>
                        <dd>
                            <input type="radio" name="state" value="0" @(!store.MgDiscountState ? "checked" : "") />
                            <label>@T("否")</label>
                            <input type="radio" name="state" value="1" @(store.MgDiscountState ? "checked" : "") />
                            <label>@T("是")</label>
                        </dd>
                    </dl>
                    @foreach (GradeModel item in Model.membergrades)
                    {
                        var discount = mgDiscounts.FirstOrDefault(p => p.GradeId == item.level)?.Discount ?? 10;
                        <dl>
                            <dt><i class="required">*</i>@item.level_name</dt>
                            <dd>
                                <input class="w60 text" name="[@item.level].discount" type="text" value="@discount"
                                    maxlength="30" />
                                <span>@T("折")</span>
                                <p class="hint">@T("数值为0.1至10之间,设置为10表示不享受折扣")</p>
                            </dd>
                        </dl>
                    }
                    <div class="bottom">
                        <input type="submit" class="submit" value='@T("提交")'>
                    </div>
                </form>
            </div>

            <script>
                $(function () {
                    $('#add_form').validate({
                        errorPlacement: function (error, element) {
                            var error_td = element.parents('dd');
                            error_td.append(error);
                        },
                        submitHandler: function (form) {
                            var $form = $("#add_form");
                            var $btn = $("#submit_button");
                            $btn.prop('disabled', true);
                            $.ajax({
                                url: $form.attr('action'),
                                type: 'POST',
                                data: $form.serialize(),
                                success: function (res) {
                                    if (res && res.success) {
                                        var ok = res.msg || '@T("设置成功")';
                                        layer.msg(ok, { icon: 1, time: 800 }, function () {
                                            location.reload();
                                        });
                                    } else {
                                        var em = (res && res.msg) || '@T("设置失败")';
                                        if (window.layer) { layer.msg(em, { icon: 2 }); } else { alert(em); }
                                    }
                                },
                                error: function () {
                                    if (window.layer) { layer.msg('@T("网络错误")', { icon: 2 }); } else { alert('@T("网络错误")'); }
                                },
                                complete: function () { $btn.prop('disabled', false); }
                            });
                        }
                    });
                });
            </script>


        </div>
    </div>
</div>