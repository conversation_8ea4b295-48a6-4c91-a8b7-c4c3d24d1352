﻿@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("经验值管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="/index.php/admin/exppoints/index.html" class="current"><span>经验值管理</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("ExpSetting")','@T("规则设置")')"><span>@T("规则设置")</span></a></li>
                <li><a href="javascript:dsLayerOpen('/index.php/admin/exppoints/edit.html','经验值调整')"><span>经验值调整</span></a></li>
            </ul>
        </div>
    </div>

    <form method="get" name="formSearch" id="formSearch" action="">
        <div class="ds-search-form">
            <dl>
                <dt>会员名称</dt>
                <dd><input type="text" name="mname" class="txt" value=''></dd>
            </dl>
            <dl>
                <dt>添加时间</dt>
                <dd>
                    <input type="text" id="stime" name="stime" class="txt date" value="">
                    <label>~</label>
                    <input type="text" id="etime" name="etime" class="txt date" value="">
                </dd>
            </dl>
            <dl>
                <dd>
                    <select name="stage">
                        <option value="">操作阶段</option>
                        <option value="login">会员登录</option>
                        <option value="comments">商品评论</option>
                        <option value="order">订单消费</option>
                        <option value="system">系统调整</option>
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>操作描述</dt>
                <dd><input type="text" id="description" name="description" class="txt2" value=""></dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="查询" />
                <a href="/index.php/admin/exppoints/index.html" class="btn btn-default" title="取消">取消</a>
                <a class="btn btn-default" href="javascript:export_xls('/index.php/admin/exppoints/export_step1.html')">导出Excel</a>
            </div>
        </div>
    </form>

    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="提示相关设置操作时应注意的要点">操作提示</h4>
            <span id="explanationZoom" title="收起提示" class="arrow"></span>
        </div>
        <ul>
            <li>经验值管理，展示了会员经验值增减情况的详细情况，经验值前无符号表示增加，有符号“-”表示减少</li>
        </ul>
    </div>
    <table class="ds-default-table">
        <thead>
            <tr>
                <th>会员名称</th>
                <th>经验值</th>
                <th>添加时间</th>
                <th>操作阶段</th>
                <th>操作描述</th>
            </tr>
        </thead>
        <tbody>
            <tr class="no_data">
                <td colspan="15">没有符合条件的记录</td>
            </tr>
        </tbody>
    </table>
</div>
<script asp-location="Footer">
    $(function () {
        $('#stime').datepicker({dateFormat: 'yy-mm-dd',onSelect:function(dateText,inst){
            var year2 = dateText.split('-') ;
            $('#etime').datepicker( "option", "minDate", new Date(parseInt(year2[0]),parseInt(year2[1])-1,parseInt(year2[2])+1) );
        }});
        $('#etime').datepicker({dateFormat: 'yy-mm-dd',onSelect:function(dateText,inst){
            var year1 = dateText.split('-') ;
            $('#stime').datepicker( "option", "maxDate", new Date(parseInt(year1[0]),parseInt(year1[1])-1,parseInt(year1[2])-1) );
        }});
    });
</script>