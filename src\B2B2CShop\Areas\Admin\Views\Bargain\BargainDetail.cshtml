﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    Bargain entity = Model.entity;
}
<div class="page">

    <form method="get" name="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("状态")</dt>
                <dd>
                    <select name="status">
                        <option value="">@T("请选择")</option>
                        <!option value="0" @(Model.status == 0 ? "selected" : "")>@T("砍价取消")</!option>
                        <!option value="1" @(Model.status == 1 ? "selected" : "")>@T("砍价中")</!option>
                        <!option value="2" @(Model.status == 2 ? "selected" : "")>@T("砍价成功")</!option>
                        <！option value="3" @(Model.status == 3 ? "selected" : "")>@T("砍价失败")</！option>
                    </select>
                    <input name="bargainId" value="@entity.Id" type="hidden" />
                </dd>
            </dl>
            <div class="btn_group">
                <a href="javascript:document.formSearch.submit();" class="btn" title='@T("查询")'>@T("查询")</a>
            </div>
        </div>
    </form>
    <!-- 列表 -->
    <form id="list_form" method="post">
        <table class="ds-default-table">
            <thead>
                <tr class="thead">
                    <th width="80">@T("砍价名称")</th>
                    <th width="80">@T("发起人")</th>
                    <th width="80">@T("需砍多少次")</th>
                    <th width="80">@T("砍价次数")</th>
                    <th width="80">@T("砍价后价格")</th>
                    <th width="180">@T("砍价时间")</th>
                    <th width="80">@T("操作")</th>
                </tr>
            </thead>
            <tbody id="treet1">
                @if (Model.list.Count > 0)
                {
                    foreach (BargainOrder item in Model.list)
                    {
                        <tr class="hover">
                            <td>@item.Name</td>
                            <td>@item.InitiatorName</td>
                            <td>@item.TotalCuts</td>
                            <td>@item.Times</td>
                            <td>@item.CurrentPrice</td>
                            <td>@(UnixTime.ToDateTime(item.StartTime)+"-"+ UnixTime.ToDateTime(item.EndTime))</td>
                            <td><a href="javascript:dsLayerOpen('@Url.Action("BargainsLog",new {orderId = item.Id})','@T("砍价记录")','800px','400px')" class="dsui-btn-view"><i class="iconfont"></i>@T("砍价记录")</a></td>
                        </tr>
                    }
                }
                else
                {
                    <tr class="no_data">
                        <td colspan="7">@T("没有符合条件的记录")</td>
                    </tr>
                }
            </tbody>
        </table>
        <ul class="pagination">
            @Html.Raw(Model.PageHtml)
        </ul>
    </form>

</div>