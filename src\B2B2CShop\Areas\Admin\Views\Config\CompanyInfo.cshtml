﻿@using B2B2CShop.Common
@{
    // script
    PekHtml.AppendScriptParts(ResourceLocation.Head, "/static/plugins/js/layui/layui.js");

    // css
    PekHtml.AppendCssFileParts("/static/plugins/js/layui/css/layui.css");
}
<style asp-location="true">
    .layui-tab-title {
        width: 95%;
        margin: 0 auto;
    }

    .layui-tab.layui-tab-brief.Lan {
        box-shadow: 0 0 5px #AAA inset;
        box-shadow: 0 0 5px #AAA;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("企业信息设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("站点设置")</span></a></li>
                <li><a href="@Url.Action("Verified")"><span>实名认证配置</span></a></li>
                @*<li><a href="/index.php/admin/config/dump.html"><span>防灌水设置</span></a></li>
                    <li><a href="/index.php/admin/config/im.html"><span>站内IM设置</span></a></li>*@
                <li><a href="@Url.Action("Auto")"><span>@T("自动执行时间设置")</span></a></li>
                <li><a href="@Url.Action("CompanyInfo")" class="current"><span>@T("企业信息设置")</span></a></li>
            </ul>
        </div>
    </div>
    @using (Html.BeginForm("UpdateCompanyInfo", "Config", FormMethod.Post, new { enctype = "multipart/form-data", name = "form1" }))
    {
        <div class="layui-tab layui-tab-brief  Lan" lay-filter="docDemoTabBrief">
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="ncap-form-default">
                        <dl>
                            <dt>@T("公司成立时间")</dt>
                            <dd>
                                <input name="EstablishmentTime" value="@Settings.Current.EstablishmentTime" class="input-txt" type="text">
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("工厂面积")</dt>
                            <dd>
                                <input name="FactoryArea" value="@Settings.Current.FactoryArea" class="input-txt" type="text">
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("研发工程师")</dt>
                            <dd>
                                <input name="RDEngineer" value="@Settings.Current.RDEngineer" class="input-txt" type="text">
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("员工")</dt>
                            <dd>
                                <input name="Employee" value="@Settings.Current.Employee" class="input-txt" type="text">
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("全球企业用户")</dt>
                            <dd>
                                <input name="Customer" value="@Settings.Current.Customer" class="input-txt" type="text">
                            </dd>
                        </dl>
                        <dl>
                            <dt></dt>
                            <dd><a href="JavaScript:void(0);" class="btn" onclick="document.form1.submit()">@T("确认提交")</a></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    }
</div>