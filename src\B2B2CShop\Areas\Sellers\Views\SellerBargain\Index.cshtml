@using B2B2CShop.Entity
@using Pek.Timing
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "Bargain";
    PekHtml.AppendPageCssClassParts("html-sellerbargain-page");
    PekHtml.AppendTitleParts(T("砍价管理").Text);
}
@await Html.PartialAsync("_Left")

<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li class="current"><a href="@Url.Action("Index")">@T("砍价活动列表")</a></li>
            </ul>
            <a class="dssc-btn dssc-btn-green" style="right:100px" href="@Url.Action("Add")"><i class="iconfont">&#xe6db;</i>@T("新增砍价")</a>
            <a class="dssc-btn dssc-btn-acidblue" href="@Url.Action("BuyPackage")" title=""><i class="iconfont">&#xe6a1;</i>@T("套餐续费")</a>
        </div>
        <div class="p20">
            <div class="alert alert-block mt10">
                @if (Model.quotaTip != "")
                {
                    <strong style="color: #F00;">@Model.quotaTip</strong>
                }
                else
                {
                    <strong>@T("当前没有可用套餐，请先购买套餐")</strong>
                }
                <ul>
                    <li>@T("1、点击购买套餐和套餐续费按钮可以购买或续费套餐")</li>
                    <li>@T("2、点击添加活动按钮可以添加砍价_活动，点击管理按钮可以对砍价_活动内的商品进行管理")</li>
                    <li>@T("3、点击删除按钮可以删除砍价_活动")</li>
                    <li>4、<strong style="color: red">@T("相关费用会在店铺的账期结算中扣除")</strong>。</li>
                </ul>
            </div>

            <form method="get">
                <table class="search-form">
                    <tr>
                        <td>&nbsp;</td>
                        <th>@T("状态")</th>
                        <td class="w100">
                            <select name="state">
                                <option value="-1">@T("-请选择-")</option>
                                <!option value="0" @(Model.state == 0 ? "selected" : "")>@T("已取消")</!option>
                                <!option value="1" @(Model.state == 1 ? "selected" : "")>@T("待开始")</!option>
                                <!option value="2" @(Model.state == 2 ? "selected" : "")>@T("进行中")</!option>
                                <!option value="3" @(Model.state == 3 ? "selected" : "")>@T("已结束")</!option>
                            </select>
                        </td>
                        <th class="w110">@T("砍价名称")</th>
                        <td class="w160"><input type="text" class="text w150" name="name" value="@Model.name" /></td>
                        <td class="w70 tc">
                            <input type="submit" class="submit" value="@T("搜索")" />
                        </td>
                    </tr>
                </table>
            </form>
            <table class="dssc-default-table">
                <thead>
                    <tr>
                        <th class="">@T("砍价名称")</th>
                        <th class="">@T("砍价商品")</th>
                        <th class="w100">@T("砍价状态")</th>
                        <th class="w100">@T("商品底价")</th>
                        <th class="w100">@T("限购数量")</th>
                        <th class="w100">@T("砍价有效期")</th>
                        <th class="w150">@T("共多少刀砍至底价")</th>
                        <th class="w150">@T("每刀最多可砍金额")</th>
                        <th class="w200">@T("砍价时间")</th>
                        <th class="w100">@T("操作")</th>
                    </tr>
                </thead>
                <tbody id="bargain_list">
                    @foreach (Bargain item in Model.list)
                    {
                        <tr class="bd-line">
                            <td>@item.Name</td>
                            <td><a href="#" target="_blank">@item.BargainGoodsName</a></td>
                            <td>@(item.State == 0 ? T("已取消"):item.State == 1?T("未开始"):item.State == 2?T("进行中"):T("已结束") )</td>
                            <td>@item.FloorPrice</td>
                            <td>@item.LimitQuantity</td>
                            <td>@item.TimeLimitHour @T("小时")</td>
                            <td>@item.TotalCuts</td>
                            <td>@item.MaxCutAmount</td>
                            <td class="goods-time">@UnixTime.ToDateTime(item.StartTime)<br>~<br>@UnixTime.ToDateTime(item.EndTime)</td>
                            <td class="dscs-table-handle tc">
                                <span>
                                    <a href="@Url.Action("Detail",new { bargainId = item.Id})"
                                       class="btn-green">
                                        <i class="iconfont">&#xe734;</i>
                                        <p>@T("砍价详情")</p>
                                    </a>
                                </span>
                            </td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="7">
                            <div class="pagination"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <ul class="pagination">
                @Html.Raw(Model.PageHtml)
            </ul>
            <form id="submit_form" action="" method="post">
                <input type="hidden" id="bargain_id" name="bargain_id" value="">
            </form>

           @*  <script type="text/javascript">
                $(document).ready(function () {
                    $('[dstype="btn_end_bargain"]').on('click', function () {
                        var action = "/home/<USER>/bargain_end.html";
                        var bargain_id = $(this).attr('data-bargain-id');
                        layer.confirm('您确认取消吗？', {
                            btn: ['确定', '取消'],
                            title: false,
                        }, function () {
                            $('#submit_form').attr('action', action);
                            $('#bargain_id').val(bargain_id);
                            ds_ajaxpost('submit_form', 'url', "/home/<USER>/index.html");
                        });
                    });
                });
            </script> *@


        </div>
    </div>
</div>