﻿using Aop.Api.Domain;
using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("满即送")]
    [Description("满即送管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/FullGift", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/FullGift", CurrentMenuName = "FullGiftList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class FullGiftController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("满即送列表")]
        public IActionResult Index(string name, long storeId, int status, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };

            viewModel.list = FullGift.Search(name, storeId, status, pages);
            viewModel.storelist = Store.FindAll();
            viewModel.name = name;
            viewModel.storeId = storeId;
            viewModel.status = status;
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {
                { "name", name },
                { "storeId", storeId.SafeString() },
                { "status", status.SafeString() },
            });
            return View(viewModel);
        }
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("满即送详情")]
        public IActionResult FullGiftDetail(long id)
        {
            var entity = FullGift.FindById(id);
            if (entity == null) return Prompt(new PromptModel { Message = GetResource("找不到活动记录")});
            dynamic viewModel = new ExpandoObject();
            viewModel.entity = entity;
            viewModel.rules = FullGiftRule.FindAllByFullGiftId(id);
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐管理")]
        public IActionResult FullGiftPackage(long storeId, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "UpdateTime",
                Desc = true
            };
            viewModel.storelist = Store.FindAll();
            viewModel.storeId = storeId;
            viewModel.list = FullGiftQuota.Search(storeId, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("FullGiftPackage"), new Dictionary<string, string> {
                { "storeId", storeId.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        public IActionResult FullGiftSetting()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.price = OperationConfig.GetValueByCode("FullGiftPackagePrice").ToDecimal();//满即送套餐价格
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        [HttpPost]
        public IActionResult PackageSet(decimal price)
        {
            // 参数校验
            if (price < 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("价格不能为负数") });
            }

            var flag = OperationConfig.UpdateValueByCode("FullGiftPackagePrice", price.SafeString(), "满即送套餐价格");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到满即送套餐价格配置") });
            }

            OperationConfig.Meta.Cache.Clear("", true);

            return Json(new DResult() { success = true, msg = GetResource("设置成功") });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("取消活动")]
        public IActionResult CancelFullGift(long id)
        {
            var entity = FullGift.FindById(id);
            if (entity==null ) return Json(new DResult() { success = false, msg = GetResource("未找到活动记录")});
            if (entity.State!=2)
                if (entity == null) return Json(new DResult() { success = false, msg = GetResource("当前状态不能取消") });

            entity.State = 3;
            entity.Update();
            FullGift.Meta.Cache.Clear("", true);
            return Json(new DResult() { success = true, msg = GetResource("取消活动成功") });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除活动")]
        public IActionResult DelFullGift(string ids)
        {
            var count = FullGift.DeleteByIds(ids);
            FullGiftRule.DeleteByFIds(ids);
            if (count>0)
            {
                return Json(new DResult() { success = true, msg = GetResource("取消活动成功") });
            }
            else
            {
                return Json(new DResult() { success = false, msg = GetResource("取消活动失败") });
            }
        }

    }
}
