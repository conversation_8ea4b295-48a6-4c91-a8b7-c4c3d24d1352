﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    GroupBuy entity = Model.entity;
}
<div class="page">
    <form method="get" name="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("状态")</dt>
                <dd>
                    <select name="status">
                        <option value="-1">@T("请选择...")</option>
                        <!option value="0" @(Model.status == 0 ? "selected" : "")>@T("拼团取消")</!option>
                        <!option value="1"  @(Model.status == 1 ? "selected" : "")>@T("参团中")</!option>
                        <!option value="2"  @(Model.status == 2 ? "selected" : "")>@T("拼团成功")</!option>
                    </select>
                    <input name="pintuan_id" value="8" type="hidden" />
                </dd>
            </dl>
            <div class="btn_group">
                <a href="javascript:document.formSearch.submit();" class="btn" title='@T("查询")'>@T("查询")</a>
            </div>
        </div>
        <div class="explanation" id="explanation">
            <div class="title" id="checkZoom">
                <h4 title='@T("提示相关设置操作时应注意的要点")'>@T("拼团信息")</h4>
                <span id="explanationZoom" title='@T("收起提示")' class="arrow"></span>
            </div>
            <ul>
                <li>@T("拼团名称：") @entity.Name</li>
                <li>@T("拼团时间：") @(UnixTime.ToDateTime(entity.StartTime) + "~" + UnixTime.ToDateTime(entity.EndTime))</li>
                <li>@T("商品原价：") @entity.GoodsPrice</li>
                <li>@T("成团折扣：") @entity.Discount @T("折")</li>
                <li>@T("成团人数：") @entity.LimitNumber @T("人")</li>
                <li>@T("购买限制：") @entity.LimitQuantity @T("个")</li>
                <li>@T("成团时限：") @entity.LimitHour @T("小 时")</li>
            </ul>
        </div>
    </form>



    <!-- 列表 -->
    <form id="list_form" method="post">
        <table class="ds-default-table">
            <thead>
                <tr class="thead">
                    <th width="300"><span>@T("开团时间")</span></th>
                    <th width="240"><span>@T("成团状态")</span></th>
                    <th class="align-center" width="120"><span>@T("己参团人数/人")</span></th>
                    <th class="align-center" width="120"><span>@T("团长用户编号")</span></th>
                    <th class="align-center" width="120"><span>@T("拼团订单")</span></th>
                </tr>
            </thead>
            <tbody id="treet1">
                @if (Model.list.Count > 0)
                {
                    foreach (GroupBuyGroup item in Model.list)
                    {
                        var orders = (Model.orderlist as List<GroupBuyOrder>).Where(e => e.GroupBuyGroupId == item.Id);

                        <tr class="hover">
                            <td>@(UnixTime.ToDateTime(item.StartTime))</td>
                            <td>
                                @(item.State switch
                                {
                                    0 => T("拼团取消"),
                                    1 => T("参团中"),
                                    2 => T("拼团成功"),
                                    _ => T("未知"),
                                })
                        (@item.LimitHour @T("小时")) @item.LimitNumber @T("人"))
                    </td>
                    <td>@item.Joined / @item.LimitNumber</td>
                    <td>@(UserE.FindByID(item.HeadId)?.Code ?? item.HeadId.SafeString())</td>
                    <td>
                        @foreach (GroupBuyOrder order in orders)
                                {
                                    <a href="javascript(0)" target="_blank">@order.OrderSn</a>
                                    <br />
                                }
                            </td>
                        </tr>
                    }
                }
                else
                {
                    <tr class="no_data">
                        <td colspan="16">@T("没有符合条件的记录")</td>
                    </tr>
                }
            </tbody>
        </table>
    </form>

</div>