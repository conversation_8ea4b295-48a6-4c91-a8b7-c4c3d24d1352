﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@model Inviter
@{
}

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("分销设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("DistributionSetting")"><span>@T("分销设置")</span></a></li>
                <li><a href="@Url.Action("DistributionGoods")"><span>@T("分销商品")</span></a></li>
                <li><a href="@Url.Action("DistributionMember")" class="current"><span>@T("分销员管理")</span></a></li>
                <li><a href="@Url.Action("DistributionMemberLevel")" ><span>@T("分销员等级")</span></a></li>
                <li><a href="@Url.Action("DistributionOrder")" ><span>@T("分销员订单")</span></a></li>
            </ul>
        </div>
    </div>
    <div class="fixed-empty"></div>  
    <table class="search-form">
        <tbody>
            <tr><td>@T("会员名：")@Model.Member?.Name</td></tr>
            <tr><td>@T("分销员等级：")@Model.InviterClass?.Name</td></tr>
            <tr><td>@T("上级：")</td></tr>
            <tr><td>@T("申请时间：")@(UnixTime.ToDateTime(Model.ApplyTime))</td></tr>
            <tr><td>@T("分销商品数：")@Model.GoodsQuantity</td></tr>
            <tr><td>@T("分销商品金额：")@Model.GoodsAmount</td></tr>
            <tr><td>@T("已分销佣金：")@Model.TotalAmount</td></tr>
        </tbody>
    </table>
<div class="fixed-bar">
        <div class="item-title">
            <ul class="tab-base ds-row" id="member_level">
        <li><a href="javascript:getMemberList(1)" class="current"><span>@T("一级成员")</span></a></li>
        <li><a href="javascript:getMemberList(2)"><span>@T("二级成员")</span></a></li>
        <li><a href="javascript:getMemberList(3)"><span>@T("三级成员")</span></a></li>
    </ul>
        </div>
    </div>
    <div id="member_list_wrap"></div>
</div>
<script>
    $(function(){
        getMemberList(1)
    })
    function getMemberList(type){
        $('#member_level li a').removeClass('current');
        $('#member_level li:eq('+(type-1)+') a').addClass('current');
        $('#member_list_wrap').load(ADMINSITEURL+'/Inviter/memberlist?member_id=364419&type='+type);
    }
</script>
