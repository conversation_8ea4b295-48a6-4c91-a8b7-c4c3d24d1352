@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "DiscountSuit";
    PekHtml.AppendPageCssClassParts("html-sellerdiscountsuit-page");
    PekHtml.AppendTitleParts(T("套餐续费").Text);
}
@await Html.PartialAsync("_Left")


<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li><a href="@Url.Action("Index")">@T("活动列表")</a></li>
                <li><a href="@Url.Action("BuyPackage")">@T("套餐续费")</a></li>
            </ul>

        </div>
        <div class="p20">

            <div class="dssc-form-default">
                <form id="add_form" action="@Url.Action("BuyPackage")" method="post">
                    <dl>
                        <dt><i class="required">*</i>@T("套餐购买数量：")</dt>
                        <dd>
                            <input id="quantity" name="quantity" type="text"
                                class="text w50" /><em class="add-on">@T("月")</em><span></span>
                            <p class="hint">@T("购买单位为月(30天)，一次最多购买12个月，购买后立即生效，即可发布优惠套装活动。")</p>
                            <p class="hint">@T("每月您需要支付") @Model.price @T("元。")</p>
                            <p class="hint"><strong style="color: red">@T("相关费用会在店铺的账期结算中扣除")</strong></p>
                        </dd>
                    </dl>
                    <div class="bottom">
                        <input id="submit_button" type="submit" value='@T("提交")' class="submit">
                    </div>
                </form>
            </div>

            <script type="text/javascript">
                $(document).ready(function () {
                    //页面输入内容验证
                    $("#add_form").validate({
                        errorPlacement: function (error, element) {
                            var error_td = element.parent('dd').children('span');
                            error_td.append(error);
                        },
                        submitHandler: function (form) {
                            var unit_price = @Model.price;
                            var quantity = $("#quantity").val();
                            var price = unit_price * quantity;
                            var msg = "@T("确认购买？您总共需要支付")" + price + "@T("元")";
                            var $form = $("#add_form");
                            var $btn = $("#submit_button");
                            layer.confirm(msg, {
                                btn: ["@T("确定")", "@T("取消")"],
                                title: false,
                            }, function () {
                                $btn.prop('disabled', true);
                                $.ajax({
                                    url: $form.attr('action'),
                                    type: 'POST',
                                    data: $form.serialize(),
                                    success: function (res) {
                                        if (res && res.success) {
                                            var ok = res.msg || '@T("购买成功")';
                                            if (window.layer) { layer.msg(ok, { icon: 1, time: 800 }, function () { window.location.href = '@Url.Action("Index")'; }); }
                                            else { alert(ok); window.location.href = '@Url.Action("Index")'; }
                                        } else {
                                            var em = (res && res.msg) || '@T("操作失败")';
                                            if (window.layer) { layer.msg(em, { icon: 2 }); } else { alert(em); }
                                        }
                                    },
                                    error: function () {
                                        if (window.layer) { layer.msg('@T("网络错误")', { icon: 2 }); } else { alert('@T("网络错误")'); }
                                    },
                                    complete: function () { $btn.prop('disabled', false); }
                                });
                            });
                        },
                        rules: {
                            quantity: {
                                required: true,
                                digits: true,
                                min: 1,
                                max: 12
                            }
                        },
                        messages: {
                            quantity: {
                                required: '<i class="iconfont">&#xe64c;</i>' + '@T("不能为空，且必须为1~12之间的整数")',
                                digits: '<i class="iconfont">&#xe64c;</i>' + '@T("不能为空，且必须为1~12之间的整数")',
                                min: '<i class="iconfont">&#xe64c;</i>' + '@T("不能为空，且必须为1~12之间的整数")',
                                max: '<i class="iconfont">&#xe64c;</i>' + '@T("不能为空，且必须为1~12之间的整数")'
                            }
                        }
                    });
                });
            </script>


        </div>
    </div>
</div>