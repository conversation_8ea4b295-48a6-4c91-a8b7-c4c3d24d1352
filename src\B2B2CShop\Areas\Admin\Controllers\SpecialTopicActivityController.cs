﻿using B2B2CShop.Common;
using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using iTextSharp.text.pdf.codec;
using iTextSharp.text.pdf.parser.clipper;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.Recommendations;
using MimeKit.Tnef;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Timing;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("专题活动")]
    [Description("专题活动管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/SpecialTopicActivity", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/SpecialTopicActivity", CurrentMenuName = "SpecialTopicActivityList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class SpecialTopicActivityController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("专题活动列表")]
        public IActionResult Index(string title, DateTime startTime, DateTime endTime, int status = -1, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Sort",
                Desc = false
            };
            viewModel.title = title;
            viewModel.status = status;
            viewModel.startTime = startTime;
            viewModel.endTime = endTime;
            viewModel.list = Activity.Search(title, status, startTime, endTime, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {
                { "startTime", startTime.SafeString() },
                { "endTime", endTime.SafeString() },
                { "status", status.SafeString() },
                { "title", title },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增活动")]
        public IActionResult AddActivity()
        {
            return View();
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增活动")]
        [HttpPost]
        public IActionResult AddActivity(string title, IFormFile activity_banner, IFormFile activity_banner_mobile, string desc, DateTime startdate, DateTime enddate, int sort, int state)
        {
            // 基础校验
            if (title.IsNullOrWhiteSpace())
                return Json(new DResult { success = false, msg = GetResource("活动标题不能为空") });
            if (activity_banner == null || activity_banner.Length <= 0)
                return Json(new DResult { success = false, msg = GetResource("请上传PC端活动图片") });
            if (activity_banner_mobile == null || activity_banner_mobile.Length <= 0)
                return Json(new DResult { success = false, msg = GetResource("请上传移动端活动图片") });
            if (startdate >= enddate)
                return Json(new DResult { success = false, msg = GetResource("开始时间必须早于结束时间") });
            if (desc.IsNullOrWhiteSpace())
                return Json(new DResult { success = false, msg = GetResource("活动描述不能为空") });

            // 保存图片为 WebP
            var (ok1, pcUrl, err1) = UploadHelper.SaveImageAsWebp(activity_banner, "Operation/SpecialTopicActivity");
            if (!ok1) return Json(new DResult { success = false, msg = GetResource(err1) });

            var (ok2, mbUrl, err2) = UploadHelper.SaveImageAsWebp(activity_banner_mobile, "Operation/SpecialTopicActivity");
            if (!ok2) return Json(new DResult { success = false, msg = GetResource(err2) });

            // 入库
            var entity = new Activity
            {
                Title = title.Trim(),
                Banner = pcUrl,
                BannerMobile = mbUrl,
                Description = desc.Trim(),
                StartDate = UnixTime.ToTimestamp(startdate),
                EndDate = UnixTime.ToTimestamp(enddate),
                Sort = sort,
                State = state,
            };
            entity.Insert();
            // 添加翻译表数据
            if (LocalizationSettings.Current.IsEnable)
            {
                var languageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                foreach (var item in languageList)
                {
                    // 处理多语言图片
                    var lan = new ActivityLan();
                    lan.AId = entity.Id;
                    lan.LId = item.Id;
                    lan.Title = (GetRequest($"[{item.Id}].title")).SafeString().Trim();
                    lan.Description = (GetRequest($"[{item.Id}].desc")).SafeString().Trim();
                    var langImage1 = Request.Form.Files[$"[{item.Id}].activity_banner"];
                    if (langImage1 != null && langImage1.Length > 0)
                    {
                        var (lanok1, lanpcUrl, lanerr1) = UploadHelper.SaveImageAsWebp(langImage1, "Operation/SpecialTopicActivity");
                        if (ok1) lan.Banner = lanpcUrl;
                    }
                    var langImage2 = Request.Form.Files[$"[{item.Id}].activity_banner_mobile"];
                    if (langImage2 != null && langImage2.Length > 0)
                    {
                        var (lanok2, lanmbUrl, lanerr2) = UploadHelper.SaveImageAsWebp(langImage2, "Operation/SpecialTopicActivity");
                        if (ok2) lan.BannerMobile = lanmbUrl;
                    }
                    lan.Insert(); ;
                }
            }
            return Json(new DResult { success = true, msg = GetResource("添加成功") });
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("编辑活动")]
        public IActionResult EditActivity(long id)
        {
            var entity = Activity.FindById(id);
            if (entity == null)
                return Prompt(new PromptModel { Message = GetResource("未找到该活动") });

            dynamic viewModel = new ExpandoObject();
            viewModel.entity = entity;
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("编辑活动")]
        [HttpPost]
        public IActionResult EditActivity(long id, string title, IFormFile activity_banner, IFormFile activity_banner_mobile, string desc, DateTime startdate, DateTime enddate, int sort, int state)
        {
            // 基础校验
            if (id <= 0)
                return Json(new DResult { success = false, msg = GetResource("参数错误") });
            if (title.IsNullOrWhiteSpace())
                return Json(new DResult { success = false, msg = GetResource("活动标题不能为空") });
            if (startdate >= enddate)
                return Json(new DResult { success = false, msg = GetResource("开始时间必须早于结束时间") });
            if (desc.IsNullOrWhiteSpace())
                return Json(new DResult { success = false, msg = GetResource("活动描述不能为空") });

            var entity = Activity.FindById(id);
            if (entity == null)
                return Json(new DResult { success = false, msg = GetResource("未找到该活动") });

            // 按需更新主图（PC/Mobile）=> 使用替换方法（保存新图并删除旧图）
            if (activity_banner != null && activity_banner.Length > 0)
            {
                var (ok, url, err) = UploadHelper.ReplaceImageAsWebp(activity_banner, entity.Banner, "Operation/SpecialTopicActivity");
                if (!ok) return Json(new DResult { success = false, msg = GetResource(err) });
                entity.Banner = url;
            }
            if (activity_banner_mobile != null && activity_banner_mobile.Length > 0)
            {
                var (ok, url, err) = UploadHelper.ReplaceImageAsWebp(activity_banner_mobile, entity.BannerMobile, "Operation/SpecialTopicActivity");
                if (!ok) return Json(new DResult { success = false, msg = GetResource(err) });
                entity.BannerMobile = url;
            }

            // 更新主表
            entity.Title = title.Trim();
            entity.Description = desc.Trim();
            entity.StartDate = UnixTime.ToTimestamp(startdate);
            entity.EndDate = UnixTime.ToTimestamp(enddate);
            entity.Sort = sort;
            entity.State = state;
            entity.Update();

            // 更新/插入多语言翻译
            if (LocalizationSettings.Current.IsEnable)
            {
                var languageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                foreach (var item in languageList)
                {
                    var lan = ActivityLan.FindByAIdAndLId(entity.Id, item.Id) ?? new ActivityLan { AId = entity.Id, LId = item.Id };

                    // 基本文本
                    lan.Title = (GetRequest($"[{item.Id}].title")).SafeString().Trim();
                    lan.Description = (GetRequest($"[{item.Id}].desc")).SafeString().Trim();

                    // 多语言图片（可选）
                    var langImage1 = Request.Form.Files[$"[{item.Id}].activity_banner"];
                    if (langImage1 != null && langImage1.Length > 0)
                    {
                        var (ok, url, err) = UploadHelper.ReplaceImageAsWebp(langImage1, lan.Banner, "Operation/SpecialTopicActivity");
                        if (ok) lan.Banner = url;
                    }
                    var langImage2 = Request.Form.Files[$"[{item.Id}].activity_banner_mobile"];
                    if (langImage2 != null && langImage2.Length > 0)
                    {
                        var (ok, url, err) = UploadHelper.ReplaceImageAsWebp(langImage2, lan.BannerMobile, "Operation/SpecialTopicActivity");
                        if (ok) lan.BannerMobile = url;
                    }

                    if (lan.Id > 0) lan.Update();
                    else
                    {
                        lan.Insert();
                    }
                }
            }

            return Json(new DResult { success = true, msg = GetResource("编辑成功") });
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("更改排序")]
        [HttpPost]
        public IActionResult ChangeSort(long id, int sort)
        {
            if (id <= 0)
                return Json(new DResult { success = false, msg = GetResource("参数错误") });
            var entity = Activity.FindById(id);
            if (entity == null)
                return Json(new DResult { success = false, msg = GetResource("未找到该活动") });
            entity.Sort = sort;
            entity.Update();
            return Json(new DResult { success = true, msg = GetResource("操作成功") });
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("删除活动")]
        public IActionResult DelActivity(long id)
        {
            if (id <= 0)
                return Json(new DResult { success = false, msg = GetResource("参数错误") });
            var entity = Activity.FindById(id);
            if (entity == null)
                return Json(new DResult { success = false, msg = GetResource("未找到该活动") });
            // 删除图片
            UploadHelper.DeleteImage(entity.Banner);
            UploadHelper.DeleteImage(entity.BannerMobile);
            // 删除多语言图片
            if (LocalizationSettings.Current.IsEnable)
            {
                var lanList = ActivityLan.FindAllByAId(entity.Id);
                foreach (var item in lanList)
                {
                    if (item.Banner != null && item.Banner.IsNotNullAndWhiteSpace())
                        UploadHelper.DeleteImage(item.Banner);
                    if (item.BannerMobile != null && item.BannerMobile.IsNotNullAndWhiteSpace())
                        UploadHelper.DeleteImage(item.BannerMobile);
                    item.Delete();
                }
            }
            entity.Delete();
            Activity.Meta.Cache.Clear("", true);
            return Json(new DResult { success = true, msg = GetResource("操作成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("处理申请")]
        public IActionResult ActivityApply(long id, long storeId, string name, int state = -1, int page = 1, int limit = 10)
        {
            var entity = Activity.FindById(id);
            if (entity == null)
                return Prompt(new PromptModel { Message = GetResource("未找到该活动") });
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.entity = entity;
            viewModel.storeId = storeId;
            viewModel.name = name;
            viewModel.state = state;
            viewModel.storelist = Store.FindAll();
            viewModel.list = ActivityDetail.Search(id, storeId, name, state, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("ActivityApply"), new Dictionary<string, string> {
                { "storeId", storeId.SafeString() },
                { "name", name.SafeString() },
                { "state", state.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("通过申请")]
        [HttpPost]
        public IActionResult PassApply(string ids)
        {
            var idList = ids.Split(",");
          
            if (idList.Length == 0) return Json(new DResult { success = false, msg = GetResource("未选择任何记录") });
            var successCount = 0;
            foreach (var id in idList)
            {
                var entity = ActivityDetail.FindById(id.ToLong());
                if (entity == null) continue;
                if (entity.State != 0 && entity.State != 3) continue; // 仅待审核或再次申请可处理

                entity.State = 1;
                entity.Update();
                successCount++;
            }

            if (successCount == 0)
                return Json(new DResult { success = false, msg = GetResource("当前状态无法处理") });

            return Json(new DResult { success = true, msg = GetResource("操作成功") + $"，成功{successCount}条" });
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("拒绝申请")]
        [HttpPost]
        public IActionResult RefuseApply(string ids)
        {
            var idList = ids.Split(",");
            
            if (idList.Length == 0) return Json(new DResult { success = false, msg = GetResource("未选择任何记录") });
            var successCount = 0;
            foreach (var id in idList)
            {
                var entity = ActivityDetail.FindById(id.ToLong());
                if (entity == null) continue;
                if (entity.State != 0 && entity.State != 3) continue; // 仅待审核或再次申请可处理

                entity.State = 2;
                entity.Update();
                successCount++;
            }
            if (successCount == 0)
                return Json(new DResult { success = false, msg = GetResource("当前状态无法进行操作") });

            return Json(new DResult { success = true, msg = GetResource("操作成功") + $"，成功{successCount}条" });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除申请")]
        public IActionResult DelApply(string ids)
        {
            var count = ActivityDetail.DeleteByIds(ids);
            if (count > 0)
            {
                return Json(new DResult
                {
                    success = true,
                    msg = GetResource($"删除成功")
                });
            }
            else
            {   
                return Json(new DResult
                {
                    success = false,
                    msg = GetResource($"删除失败")
                });
            }
            
        }
    }
}
