﻿<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("意见反馈")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("意见反馈")</span></a></li>
            </ul>
        </div>
    </div>
    <!-- 操作说明 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="@T("提示相关设置操作时应注意的要点")">@T("操作提示")</h4>
            <span id="explanationZoom" title="@T("收起提示")" class="arrow"></span>
        </div>
        <ul>
            <li>@T("这里显示会员的意见反馈信息。")</li>
        </ul>
    </div>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th></th>
                <th>@T("意见反馈ID")</th>
                <th>@T("反馈内容")</th>
                <th>@T("会员名")</th>
                <th>@T("时间")</th>
                <th>@T("来自")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (FeedBack item in Model.list)
            {
                <tr>
                    <th><input type="checkbox" class="checkitem" value="@item.Id"></th>
                    <td>@item.Id</td>
                    <td>@item.Content</td>
                    <td>@item.UName</td>
                    <td>@item.CreateTime</td>
                    <td>
                        @if (item.FType == 1)
                        {
                            @T("手机")
                        }
                        else if (item.FType == 2)
                        {
                            @T("PC")
                        }
                    </td>
                    <td>
                        <a href="javascript:;"
                           onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { ids = item.Id })','@T("您确定要删除吗?")')"
                           class="dsui-btn-del">
                            <i class="iconfont"></i>@T("删除")
                        </a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small"
                                   onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>

</div>
<script asp-location="Footer">
    function submit_delete(items) {
        _uri = "@Url.Action("Delete")?ids=" + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
    }
</script>
