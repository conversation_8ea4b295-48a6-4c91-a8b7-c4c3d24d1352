﻿@* <div>商品类型ID</div>
<div>@item.Id</div>
<div>类型名称</div>
<div>@item.Name</div>
<div>商品分类ID</div>
<div>@item.GoodsClassId</div>
<div>商品分类ID</div>
<div>@item.GoodsClassName</div> *@
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("类型管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("类型管理")</span></a></li>
                <li><a href="@Url.Action("AddType")"><span>@T("新增类型")</span></a>
                </li>
            </ul>
        </div>
    </div>
    <!-- 操作说明 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="@T("提示相关设置操作时应注意的要点")">@T("操作提示")</h4>
            <span id="explanationZoom" title="@T("收起提示")" class="arrow"></span>
        </div>
        <ul>
            <li>@T("添加商品分类时需选择类型和商品分类进行关联，商户在添加对应分类下的商品时，会显示对应的规格和属性。")</li>
        </ul>
    </div>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th></th>
                <th>@T("商品类型ID")</th>
                <th>@T("类型名称")</th>
                <th>@T("商品分类ID")</th>
                <th>@T("商品分类名称")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (GoodType item in Model.list)
            {
                <tr>
                    <th><input type="checkbox" class="checkitem" value="@item.Id"></th>
                    <td>@item.Id</td>
                    <td>@item.Name</td>
                    <td>@item.GoodsClassId</td>
                    <td>@item.GoodsClassName</td>
                    <td>
                        <a href="@Url.Action("EditType", new { id= item.Id })" class="dsui-btn-edit"><i
                                class="iconfont"></i>@T("编辑")</a>

                        <a href="javascript:;"
                            onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { ids = item.Id })','@T("您确定要删除吗?")')"
                            class="dsui-btn-del">
                            <i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small"
                        onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>

</div>
<script asp-location="Footer">
    function submit_delete(items) {
        _uri = "@Url.Action("Delete")?ids=" + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
    }
</script>
