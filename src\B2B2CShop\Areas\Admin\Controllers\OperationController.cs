﻿using Microsoft.AspNetCore.Mvc;

using Pek.DsMallUI;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;

using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>营销中心</summary>
[DisplayName("营销中心")]
[Description("用于平台的营销方法管理")]
[AdminArea]
[DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/Operation", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/Operation", CurrentMenuName = "OperationList", CurrentIcon = "&#xe734;", LastUpdate = "20241203")]
public class OperationController : PekCubeAdminControllerX {

    /// <summary>
    /// 营销中心管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("营销中心管理")]
    public IActionResult Index()
    {
        return View();
    }
    
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("基本设置")]
    public IActionResult Setting()
    {
        return View();
    }

}
