﻿@using Pek.Timing
@model Advertising
@{
    var localizationSettings = LocalizationSettings.Current;
}
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>
<style asp-location="true">
    .page {
    min-height: 415px
    }

    .layui-tab-brief > .layui-tab-title .layui-this {
    color: #419DFD !important;
    }

    .layui-tab-brief > .layui-tab-more li.layui-this:after,
    .layui-tab-brief > .layui-tab-title .layui-this:after {
    border-bottom: 2px solid #419DFD !important;
    }

    .layui-tab-content {
    padding: 0px !important;
    }

    .bd {
    border: 2px solid red;
    }
</style>
<div class="page">
    <form id="goods_class_form" action="@Url.Action("EditAdvertising")" enctype="multipart/form-data" method="post">
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">标准</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li data="@item.Id">@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td class="required w120">
                                    <label class="gc_name validation">@T("广告名称"):</label>
                                </td>
                                <td>
                                    <input type="text" name="Title" class="txt w200" value="@Model.Title" required>
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td class="required w120">
                                    <label class="gc_name">@T("图片上传"):</label>
                                </td>
                                <td>
                                    <span class="type-file-box">
                                        <input type='text' name='adv_pic' id='adv_pic' class='type-file-text' value="@Model.PicturePath"/>
                                        <input type='button' id="button" name="button" value='@T("上传")' class='type-file-button' />
                                        <input name="advPic" type="file" class="type-file-file" id="_pic" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                                    </span>
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td class="required w120">
                                    <label class="gc_name">@T("链接地址"):</label>
                                </td>
                                <td>
                                    <input type="text" name="Link" class="txt w200" value="@Model.Link">
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        var advLan = AdvertisingLan.FindByAIdAndLId(Model.Id,item.Id);
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td class="required w120">
                                            <label class="gc_name">@T("广告名称"):</label>
                                        </td>
                                        <td>
                                            <input type="text" name="[@item.Id].Title" id="[@item.Id].Title" class="txt w200" value="@advLan.Title">
                                        </td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="required w120">
                                            <label class="gc_name">@T("图片上传"):</label>
                                        </td>
                                        <td>
                                            <span class="type-file-box">
                                                <input type='text' name='[@item.Id]._pic' id='[@item.Id].brand_pic' class='type-file-text' value="@advLan.PicturePath" />
                                                <input type='button' id="[@item.Id].button" name="[@item.Id].button" value='@T("上传")' class='type-file-button' />
                                                <input name="[@item.Id].advPic" type="file" class="type-file-file" id="[@item.Id].advPic" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                                            </span>
                                        </td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="required w120">
                                            <label class="gc_name">@T("链接地址"):</label>
                                        </td>
                                        <td>
                                            <input type="text" name="[@item.Id].Link" id="[@item.Id].Link" class="txt w200" value="@advLan.Link">
                                        </td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
                <table class="ds-default-table">
                    <tbody>
                        <tr class="noborder">
                            <td class="required w120">
                                <label class="gc_name validation">@T("所属广告位"):</label>
                            </td>
                            <td>
                                <select name="AdvPositionId" id="AdvPositionId" class="select" style="width:200px">
                                    <!option value="">@T("请选择")</!option>
                                    @foreach (var advPosition in ViewBag.AdvPositions)
                                    {
                                        if (Model.APId==advPosition.Id)
                                        {

                                            <!option value="@advPosition.Id" selected>@advPosition.Name</!option>
                                        }
                                        else
                                        {
                                            <!option value="@advPosition.Id">@advPosition.Name</!option>
                                        }
                                    }
                                </select>
                            </td>
                        </tr>
                        <tr class="noborder">
                            <td class="required w120">
                                <label class="gc_name validation">@T("开始时间"):</label>
                            </td>
                            <td>
                                <input class="w120" type="text" id="StartTime" name="StartTime" autocomplete="off" value="@(UnixTime.ToDateTime(Model.StartTime).ToString("yyyy-MM-dd"))" required>
                            </td>
                        </tr>
                        <tr class="noborder">
                            <td class="required w120">
                                <label class="gc_name validation">@T("结束时间"):</label>
                            </td>
                            <td>
                                <input class="w120" type="text" id="EndTime" name="EndTime" autocomplete="off" value="@(UnixTime.ToDateTime(Model.EndTime).ToString("yyyy-MM-dd"))" required>
                            </td>
                        </tr>
                        <tr class="noborder">
                            <td class="required w120">
                                <label class="gc_name">@T("排序"):</label>
                            </td>
                            <td>
                                <input id="Sort" name="Sort" class="input-txt" type="number" step="1" min="0" value="@Model.Sort">
                            </td>
                        </tr>
                        <tr class="noborder">
                            <td class="required w120">
                                <label class="gc_name ">@T("背景颜色"):</label>
                            </td>
                            <td>
                                <div id="color1"></div>
                                <input type="hidden" maxlength="20" value="@Model.BgColor" name="BgColor" class="txt" id="BgColor">
                            </td>
                        </tr>
                        <tr class="tfoot">
                            <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
</div>
<script asp-location="Footer">
    $("#_pic").change(function () {
        $("#adv_pic").val($(this).val());
    });
     // 多语言图片
   $("input[type='file'][id$='.advPic']").each(function(){
        var langId = $(this).attr('id').replace('.advPic', '');
        $(this).on('change', function(){
            // 用属性选择器，兼容特殊字符
            $("[id='" + langId + ".brand_pic']").val($(this).val());
        });
    });
    function call_back(picname) {
        $('#advPic').val(picname);
        $('#view_img').attr('src', 'http://b2b2c.h.com/uploads/home/<USER>/' + picname);
    }
    //颜色选择
    layui.use('colorpicker',function(){
        var colorpicker = layui.colorpicker;
         colorpicker.render({
             elem:'#color1',
             color: '@Model?.BgColor',
             predefine:true,
             done:function(color){
                 $("#BgColor").val(color);
             }
         })
    })
</script>

<script>
    $(document).ready(function () {
        $('#AdvPositionId').select2({
            placeholder: "@T("请选择")",
            allowClear: true
        });
    });
</script>
<script type="text/javascript">
    layui.use('laydate', function(){
        var upload = layui.upload;
        var layer = layui.layer;
        var element = layui.element;
        var $ = layui.$;
        var laydate = layui.laydate;

        laydate.render({
            elem: '#StartTime',
            type: 'date'
        });

        laydate.render({
            elem: '#EndTime',
            type: 'date'
        });
    })

</script>