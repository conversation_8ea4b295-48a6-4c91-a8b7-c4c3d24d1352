@using B2B2CShop.Entity
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "RecommendedBooth";
    PekHtml.AppendPageCssClassParts("html-sellerrecommendedbooth-page");
    PekHtml.AppendTitleParts(T("�Ƽ�չλ").Text);
}
@await Html.PartialAsync("_Left")
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li class="current"><a href="@Url.Action("Index")">@T("推荐展位")</a></li>
            </ul>
            <a class="dssc-btn dssc-btn-green" href="javascript:void(0);" dstype="select_goods" style="right:100px"><i
                    class="iconfont">&#xe6db;</i>@T("添加商品")</a>
            <a class="dssc-btn dssc-btn dssc-btn-acidblue" href="@Url.Action("BuyPackage")"><i
                    class="iconfont">&#xe6a1;</i>@T("套餐续费")</a>
        </div>
        <div class="p20">
            <!-- 有可用套餐，发布活动 -->
            <div class="alert alert-block mt10">
                @if (Model.quotaTip != "")
                {
                    <strong style="color: #F00;">@Model.quotaTip</strong>
                }
                else
                {
                    <strong>@T("当前没有可用套餐，请先购买套餐")</strong>
                }
                <ul>
                    <li>@T("1、点击购买套餐或续费套餐可以购买或续费套餐")</li>
                    <li>@T("2、")<strong style="color: red">@T("相关费用会在店铺的账期结算中扣除")</strong>@T("。")</li>
                    <li>@T("3、被推荐商品将在该商品所在的分类及其上级分类的商品列表左侧随机出现。")</li>
                    <li>@T("4、您最多可以推荐10个商品。")</li>
                </ul>
            </div>
            <div nvtype="div_goods_select" class="div-goods-select" style="display: none;">
                <table class="search-form">
                    <tr>
                        <th class="w150"><strong>@T("第一步：搜索店内商品")</strong></th>
                        <td class="w160"><input dstype="search_goods_name" type="text w150" class="text"
                                name="goods_name" value="" /></td>
                        <td class="w70 tc">
                            <input dstype="btn_search_goods" type="button" value='@T("搜索")' class="submit" />
                        </td>
                        <td class="w10"></td>
                        <td>
                            <p class="hint">@T("不输入名称直接搜索将显示店内所有出售中的商品")</p>
                        </td>
                    </tr>
                </table>
                <div dstype="div_goods_search_result" class="search-result"></div>
                <a dstype="btn_hide_goods_select" class="close" href="javascript:void(0);">X</a>
            </div>

            <table class="dssc-default-table">
                <thead>
                    <tr>
                        <th class="w10"></th>
                        <th class="w50"></th>
                        <th class="tl">@T("商品名称")</th>
                        <th class="w180">@T("价格")</th>
                        <th class="w110">@T("操作")</th>
                    </tr>
                </thead>

                <tbody dstype="choose_goods_list">
                    <tr dstype="tr_no_promotion" style="@(Model.list.Count > 0 ? "display:none;" : "")">
                        <td colspan="20" class="norecord">
                            <div class="no-promotion"><i class="zw"></i><span>@T("推荐展位列表暂无内容，请选择添加平台推荐展位商品。")</span>
                            </div>
                        </td>
                    </tr>
                    @foreach (BoothGoods item in Model.list)
                    {

                        <tr class="bd-line" id="<EMAIL>">
                            <td></td>
                            <td>
                                <div class="pic-thumb">
                                    <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })"
                                        target="black">
                                        <img src="@(AlbumPic.FindByName(item.GoodsSku.GetDefaultGoodsImage())?.Cover)" />
                                    </a>
                                </div>
                            </td>
                            <td class="tl">
                                <dl class="goods-name">
                                    <dt><a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })"
                                            target="_blank">@item.GoodsName @item.GoodsSku.SpecValueDetail()</a></dt>
                                </dl>
                            </td>
                            <td class="goods-price">@item.GoodsSku.GoodsPrice.ToString("F2")</td>
                            <td class="dscs-table-handle">
                                <span>
                                    <a class="btn-red" href='javascript:void(0);' dstype="del_choosed" data-gid="@item.Id">
                                        <i class="iconfont">&#xe725;</i>
                                        <p>@T("删除")</p>
                                    </a>
                                </span>
                            </td>
                        </tr>
                    }

                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    layui.use('element', function () {
        var element = layui.element;
    })
</script>

<script type="text/javascript">
    $(function () {
        // 默认隐藏搜索框
        $("#div_search_goods").hide();

        // 展示搜索框
        $('a[dstype="select_goods"]').click(function () {
            $('div[nvtype="div_goods_select"]').show();
        });

        // 隐藏搜索框
        $('a[dstype="btn_hide_goods_select"]').on('click', function () {
            $('div[nvtype="div_goods_select"]').hide();
        })

        // 点击搜索按钮进行搜索并展示商品内容
        $('input[dstype="btn_search_goods"]').on('click', function (e) {
            e.preventDefault();
            var goodsName = $('input[dstype="search_goods_name"]').val()
            console.log('goodsName', goodsName)
            // 通过AJAX加载SearchGoodsList视图
            $.ajax({
                url: '@Url.Action("SearchGoodsList")',
                type: 'POST',
                data: { keyword: goodsName },
                success: function (result) {
                    // 直接将返回的HTML内容显示在搜索结果区域
                    $('div[dstype="div_goods_search_result"]').html(result);

                    // 重新绑定商品选择按钮的点击事件
                    $(document).on('click', 'a[dstype="btn_add_xianshi_goods"]', function () {
                        var skuId = $(this).data('goods-skuid');

                        // 获取该商品的 SKU 列表并渲染表格
                        $.ajax({
                            url: '@Url.Action("ChooseGoods")',
                            type: 'GET',
                            data: { skuId: skuId },
                            success: function (res) {
                                if (res && res.success) {
                                    layer.msg(res.msg || '@T("添加成功")', { icon: 1, time: 800 }, function () {
                                        // 使用返回的 res.data 构造行并插入
                                        try {
                                            var d = res.data;
                                            var pic = (d.Img && d.Img.Cover) ? d.Img.Cover : '';
                                            var tr = '<tr class="bd-line" id="row_' + d.Id + '">' +
                                                '<td></td>' +
                                                '<td><div class="pic-thumb"><a target="_blank" href="' + (d.Url || '#') + '"><img src="' + pic + '"/></a></div></td>' +
                                                '<td class="tl"><dl class="goods-name"><dt><a target="_blank" href="' + (d.Url || '#') + '">' + (d.GoodsName || '') + '</a></dt></dl></td>' +
                                                '<td class="goods-price">' + (d.Price || '') + '</td>' +
                                                '<td class="dscs-table-handle"><span><a class="btn-red" href="javascript:void(0);" dstype="del_choosed" data-gid="' + d.Id + '"><i class="iconfont">&#xe725;</i><p>@T("删除")</p></a></span></td>' +
                                                '</tr>';
                                            $('tbody[dstype="choose_goods_list"]').append(tr);
                                        } catch (e) {
                                            console.error('插入返回数据时发生错误', e);
                                        }
                                        // 验证是否已经选择商品
                                        if (typeof choosed_goods === 'function') choosed_goods();
                                    });
                                } else {
                                    layer.msg(res.msg || '@T("添加失败")', { icon: 2, time: 800 });
                                }
                            },
                            error: function () {
                            }
                        });

                    });
                },
                error: function () {
                    $("#div_goods_search_result").html('<div class="error-result">搜索出错，请重试</div>');
                }
            });
        })

        // 删除商品：页面级委托处理，根据 tr id (row_<gid>) 删除
        $('tbody[dstype="choose_goods_list"]').on('click', 'a[dstype="del_choosed"]', function () {
            var $btn = $(this);
            var gid = $btn.data('gid');
            if (!gid) {
                // 兼容旧 data-param 格式：{gid:123}
                var dp = $btn.attr('data-param') || '';
                var m = dp.match(/gid\s*:\s*(\d+)/);
                if (m) gid = m[1];
            }
            var url = "@Url.Action("Delete")";
            $.getJSON(url, { id: gid }, function (data) {
                if (data && data.success) {
                    var $row = $('#row_' + gid);
                    if ($row.length) {
                        $row.fadeOut('slow', function () { $(this).remove(); if (typeof choosed_goods === 'function') choosed_goods(); });
                    } else {
                        // 后备：在按钮父级中移除
                        $btn.closest('tr').fadeOut('slow', function () { $(this).remove(); if (typeof choosed_goods === 'function') choosed_goods(); });
                    }
                    layer.msg(data.msg,{icon:1});
                } else {
                    layer.msg(data.msg || '@T("删除失败")',{icon:2});
                }
            }).fail(function () {
                layer.msg('@T("网络错误")',{icon:2});
            });
        });
        // 验证是否已经选择商品（显示或隐藏无数据行）
        function choosed_goods() {
            var $tbody = $('tbody[dstype="choose_goods_list"]');
            // 真实数据行使用 class bd-line
            var realCount = $tbody.children('tr.bd-line').length;
            if (realCount === 0) {
                $('tr[dstype="tr_no_promotion"]').show();
            } else {
                $('tr[dstype="tr_no_promotion"]').hide();
            }
        }
    })
</script>