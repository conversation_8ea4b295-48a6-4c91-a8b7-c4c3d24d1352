<style>
    tr.disable {
        -webkit-opacity: 0.3;
        /* Netscape and Older than Firefox 0.9 */
        -moz-opacity: 0.3;
        /* Safari 1.x (pre WebKit!) 老式khtml内核的Safari浏览器*/
        -khtml-opacity: 0.3;
        /* IE9 + etc...modern browsers */
        opacity: .3;
        /* IE 4-9 */
        filter: alpha(opacity=30);
        /*This works in IE 8 & 9 too*/
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
        /*IE4-IE9*/
        filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=30);
    }

    textarea {
        padding: 5px;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("类型管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("类型管理")</span></a></li>
                <li><a class="current"><span>@T("新增类型")</span></a></li>
            </ul>
        </div>
    </div>
    <form class="" id="type_form" method="post">
        <div class="ncap-form-default">
            <dl>
                <dt></dt>
                <dd>
                    <div id="gcategory" class="default_select">
                        @* 分类checkbox的值（用逗号隔开） *@
                        <input type="hidden" name="class_id" value="0" class="mls_id" id="class_id" />
                        <!-- <input type="hidden" name="class_name" value="" class="mls_name" /> -->
                        <!-- 默认0 -->
                        <input type="hidden" name="subClassificationId" value="0" id="subClassificationId" />
                    </div>
                </dd>
            </dl>
            <dl>
                <dt>@T("类型名称")</dt>
                <dd>
                    <input type="text" name="typeName" id="type_name" value="" />
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>

            <dl>
                <dt>@T("类型排序")</dt>
                <dd>
                    <input type="text" name="type_sort" id="type_sort" value="255" />
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl data-brand="title">
                <dt>@T("选择关联品牌")</dt>
                <dd>
                </dd>
            </dl>
            <dl data-spec="title">
                <dt>@T("选择关联规格")</dt>
                <dd>
                </dd>
            </dl>
        </div>



        <table class="ds-default-table">
            <thead>
                <tr>
                    <th width="100">@T("排序")</th>
                    <th width="200">@T("属性名称")</th>
                    <th>@T("属性可选值")</th>
                    <th width="100">@T("显示")</th>
                    <th width="100">@T("操作")</th>
                </tr>
            </thead>
            <tbody id="tr_model">
                <tr></tr>
                <tr data-attr="new">
                    <td><input type="text" class="form-control" name="at_value[0][sort]" value="0" /></td>
                    <td><input type="text" class="form-control" name="at_value[0][name]" value="" /></td>
                    <td><textarea rows="5" cols="80" class="form-control" name="at_value[0][value]"></textarea></td>
                    <td><input type="checkbox" name="at_value[0][show]" checked="checked" /></td>
                    <td>
                        <a onclick="remove_tr($(this));" href="JavaScript:void(0);">@T("删除")</a>
                    </td>
                </tr>
            </tbody>
            <tbody>
                <tr>
                    <td colspan="20">
                        <a id="add_type" class="btn-add-nofloat marginleft" href="JavaScript:void(0);">
                            <span>@T("添加一个属性")</span> </a>
                    </td>
                </tr>
            </tbody>
            <input type="hidden" name="brandIds">
            <input type="hidden" name="specIds">
        </table>
        <input class="btn" type="submit" value="@T(" 提交")" />
    </form>
</div>
<!--载入-->
<script src="/static/plugins/mlselection.js"></script>
<script>
    // function firstSelectChange(e) {
    //     $('#subClassificationId').val(e.value)
    //     $('#class_id').val(e.value)
    //     console.log($('#subClassificationId').val());
    // //    const select = $(e)
    // //     console.log(select.nextAll('select'))
    // }
    $(function () {
        @* 在 subClassificationId 后面添加select *@
        $('#gcategory').append('<select id="selectTypeId" name="rubbish" data-index="0"><option value="0" data-isDefault="true">@T("所属分类")</option><option value="0" data-isDefault="true">@T("全部分类")</option></select>');
        initSelection('selectTypeId')
        $(function () {
            var i = 1;
            var tr_model = '<tr data-attr="new">' +
                '<td><input type="text" class="form-control" name="at_value[key][sort]" value="0"/></td>' +
                '<td><input type="text" class="form-control" name="at_value[key][name]" value="" /></td>' +
                '<td><textarea rows="5" cols="80" class="form-control" name="at_value[key][value]"></textarea></td>' +
                '<td><input type="checkbox" name="at_value[key][show]" checked="checked"/></td>' +
                '<td><a onclick="remove_tr($(this));" href="JavaScript:void(0);">@T("删除")</a></td>' +
                '</tr>';
            $("#add_type").click(function () {
                $('#tr_model > tr:last').after(tr_model.replace(/key/g, i));
                i++;
            });
        });
        handleTypeClick()
        $('#gcategory').click(handleTypeClick);
        $('#type_form').submit(function () {
            $('tr[data-attr=old].disable').find('[data-attr=del]').prop('checked', true);
            $('tr[data-attr=new].disable').find('input').prop('disabled', true);
        });
    });

    function handleTypeClick() {
        $.getJSON("/biz/GoodsType/GetSpecAndBrand" + "?type_id=0&classId=" + $('[name=class_id]').val(), function (data) {
            // console.log(data);
            // 规格
            $('[data-spec=content]').remove();
            var s_list = data.spec;
            var html = '';
            for (var i in s_list) {
                var html_spec = '';
                var spec = s_list[i];
                for (var j in spec) {
                    // console.log(spec[j]);
                    html_spec += '<span style="margin-right:10px;"><input type="checkbox" id="spec_' + spec[j].Id + '" value="' + spec[j].Id + '" name="spec_id" ' + ((spec[j].checked == '1') ? 'checked="checked"' : "") + ' />' +
                        '<label style="margin-left:3px;" for="spec_' + spec[j].Id + '">' + spec[j].Name + '</label></span>';
                }
                if (i == 0) {
                    html += '<dl data-spec="content">' +
                        '<dt>' + '</dt>' +
                        '<dd>' +
                        html_spec +
                        '</dd>' +
                        '</dl>';
                } else {
                    html += (s_list[i].length > 0 ? '<dl data-spec="content">' +
                        '<dt>' + s_list[i][0]?.GoodsClassName + '</dt>' +
                        // 检查 '<dt>' + (s_list.length>0?s_list[i][0]?.GoodsClassName:'') + '</dt>' +
                        '<dd>' +
                        html_spec +
                        '</dd>' +
                        '</dl>' : '')

                }
            }
            $('[data-spec=title]').after(html);

            // 品牌
            $('[data-brand=content]').remove();
            var b_list = data.brand;
            var html = '';
            for (var i in b_list) {
                var html_brand = '';
                var brand = b_list[i];
                for (var j in brand) {
                    html_brand += '<span style="margin-right:10px;"><input type="checkbox" id="brand_' + brand[j].Id + '" value="' + brand[j].Id + '" name="brand_id" ' + ((brand[j].checked == '1') ? 'checked="checked"' : "") + ' />' +
                        '<label style="margin-left:3px;" for="brand_' + brand[j].Id + '">' + brand[j].Name + '</label></span>';
                }
                if (i == 0) {
                    html += '<dl data-brand="content">' +
                        '<dt>' + '</dt>' +
                        '<dd>' +
                        html_brand +
                        '</dd>' +
                        '</dl>';
                } else {
                    html += (b_list[i].length > 0 ? '<dl data-brand="content">' +
                        '<dt>' + b_list[i][0]?.GoodsClassName + '</dt>' +
                        '<dd>' +
                        html_brand +
                        '</dd>' +
                        '</dl>' : '')
                }

            }
            $('[data-brand=title]').after(html);
        })

    }
    function remove_tr(o) {
        if (o.parents('tr:first').hasClass('disable')) {
            o.parents('tr:first').removeClass('disable');
            o.text('@T("删除")');
        } else {
            o.parents('tr:first').addClass('disable');
            o.text('@T("还原")');
        }

    };

    $(document).ready(function () {
        $("#type_form").validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.nextAll('span.err'));
            },
            submitHandler: function (form) {
                // 阻止表单默认提交行为
                event.preventDefault();
                const brandChecked = $('input[name="brand_id"]:checked')
                const specChecked = $('input[name="spec_id"]:checked')
                var brandIds = '';
                var specIds = '';
                brandChecked.each((index, item) => {
                    console.log(item.value);
                    brandIds += item.value + ','
                })
                specChecked.each((index, item) => {
                    specIds += item.value + ','
                })
                $('input[name="brandIds"]').val(brandIds)
                $('input[name="specIds"]').val(specIds)
                form.submit();
            },
            rules: {
                type_name: {
                    required: true
                },
                type_sort: {
                    required: true,
                    number: true,
                    range: [0, 255]
                },
            },
            messages: {
                type_name: {
                    required: '@T("请输入类型名称")',
                },
                type_sort: {
                    required: '@T("排序不能为空")',
                    number: '@T("排序值只能为数字")',
                    range: '@T("数字范围为0~255，数字越小越靠前")'
                },
            }
        });
    });
</script>