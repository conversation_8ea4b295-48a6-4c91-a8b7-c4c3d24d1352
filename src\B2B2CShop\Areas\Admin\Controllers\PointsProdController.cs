﻿using B2B2CShop.Common;
using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using Flurl;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCube.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Timing;
using System.ComponentModel;
using System.Dynamic;
using System.Text.Json;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("礼品管理")]
    [Description("积分礼品管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/PointsProd", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/PointsProd", CurrentMenuName = "PointsProdList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class PointsProdController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("积分礼品列表")]
        public IActionResult Index(string name ,string state,int page = 1,int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            int show = -1;
            int recommend = -1;
            switch (state)
            {
                case "show":
                    show = 1;
                    break;
                case "nshow":
                    show = 0;
                    break;
                case "commend":
                    recommend = 1;
                    break;
            }
            viewModel.name = name;
            viewModel.state = state;
            viewModel.list = PointsGoods.Search(name, show, recommend, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> {
                { "name", name },
                { "state", state.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增礼品列表")]
        public IActionResult AddPointsProd()
        {
            ViewBag.gradelist = JsonSerializer.Deserialize<List<GradeModel>>(DHSetting.Current.MemberGrade);
            return View();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name">名称</param>
        /// <param name="keywords">关键词</param>
        /// <param name="description">描述</param>
        /// <param name="pgoods_body">礼品描述</param>
        /// <param name="goodsprice">商品价格</param>
        /// <param name="goodspoints">兑换积分</param>
        /// <param name="goodstag">标签</param>
        /// <param name="goodsserial">礼品编号</param>
        /// <param name="goodsstorage">库存</param>
        /// <param name="islimit">是否限制领取数量</param>
        /// <param name="limitnum">限制领取数量</param>
        /// <param name="islimittime">是否限制领取时间</param>
        /// <param name="starttime">开始时间</param>
        /// <param name="endtime">结束时间</param>
        /// <param name="limitgrade">限制等级</param>
        /// <param name="showstate">显示状态</param>
        /// <param name="commendstate">推荐状态</param>
        /// <param name="forbidreason">禁用原因</param>
        /// <param name="goodsImage">商品图片</param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增礼品列表")]
        [HttpPost]
        public IActionResult AddPointsProd(string name,string keywords,string description,string pgoods_body,decimal goodsprice,int goodspoints,string goodstag,string goodsserial, int goodsstorage,int islimit,int limitnum,int islimittime,DateTime starttime,DateTime endtime,int limitgrade,int showstate,int commendstate,string forbidreason,int sort,IFormFile goodsImage)
        {
            try
            {
                if (name.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = GetResource("礼品名称为空") });
                if (goodsprice < 0) return Json(new DResult() { success = false, msg = GetResource("商品价格不能为空") });
                if (goodspoints < 0) return Json(new DResult() { success = false, msg = GetResource("兑换积分不能为空") });
                if (goodsstorage < 0) return Json(new DResult() { success = false, msg = GetResource("库存不能为空") });
                if (goodsserial.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = GetResource("礼品编号不能为空") });
                if (islimittime == 1 && starttime >= endtime)
                    return Json(new DResult() { success = false, msg = GetResource("开始时间必须小于结束时间") });
                string imagepath = "";
                if (goodsImage != null && goodsImage.Length>0)
                {
                    var (ok, url, err) = UploadHelper.SaveImageAsWebp(goodsImage, "Operation/PointProdGoods");
                    if (ok)
                    {
                        imagepath = url;
                    }
                    else
                    {
                        return Json(new DResult() { success = false, msg = GetResource(err) });
                    }
                }
                var entity = new PointsGoods();
                entity.Name = name;
                entity.Price = goodsprice;
                entity.Points = goodspoints;
                entity.Image = imagepath;
                entity.Tag = goodstag;
                entity.Serial = goodsserial;
                entity.Storage = goodsstorage;
                entity.Show = showstate==1;
                entity.Recommend = commendstate == 1;
                entity.AddTime = UnixTime.ToTimestamp();
                entity.Keywords = keywords;
                entity.Description = description;
                entity.Body = pgoods_body;
                entity.State = 0;
                entity.IsLimit = islimit == 1;
                entity.LimitNum = limitnum;
                entity.IsLimitTime = islimittime == 1;
                entity.LimitMGrade = limitgrade;
                entity.StartTime = islimittime == 1 ? UnixTime.ToTimestamp(starttime) : 0;
                entity.EndTime = islimittime == 1 ? UnixTime.ToTimestamp(endtime) : 0;
                entity.Sort = sort;
                entity.Insert();
                //添加翻译表
                if (LocalizationSettings.Current.IsEnable)
                {
                    var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                    foreach (var item in LanguageList)
                    {
                        var lan = new PointsGoodsLan();
                        lan.PId = entity.Id;
                        lan.LId = item.Id;
                        lan.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                        lan.Keywords = (GetRequest($"[{item.Id}].keywords")).SafeString().Trim();
                        lan.Description = (GetRequest($"[{item.Id}].description")).SafeString().Trim();
                        lan.Tag = (GetRequest($"[{item.Id}].goodstag")).SafeString().Trim();
                        lan.Body = (GetRequest($"[{item.Id}].pgoods_body")).SafeString().Trim();
                        var lanImage = Request.Form.Files[$"[{item.Id}].goodsImage"];
                        if (lanImage != null && lanImage.Length > 0)
                        {
                            var (lanok, lanpcUrl, lanerr) = UploadHelper.SaveImageAsWebp(lanImage, "Operation/PointProdGoods");
                            if (lanok)
                            {
                                lan.Image = lanpcUrl;
                            }
                        }
                        lan.Insert();
                    }
                }
            }
            catch (Exception ex)
            {
                return Json(new DResult() { success = false, msg = GetResource("异常：") + ex.Message });
            }

            return Json(new DResult() { success = true, msg = GetResource("添加成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑礼品列表")]
        public IActionResult EditPointsProd(long id)
        {
            var entity = PointsGoods.FindById(id);
            if (entity == null) return Prompt(new PromptModel() { Message = GetResource("记录不存在") });
            ViewBag.gradelist = JsonSerializer.Deserialize<List<GradeModel>>(DHSetting.Current.MemberGrade);
            return View(entity);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑礼品列表")]
        [HttpPost]
        public IActionResult EditPointsProd(long id, string name, string keywords, string description, string pgoods_body, decimal goodsprice, int goodspoints, string goodstag, string goodsserial, int goodsstorage, int islimit, int limitnum, int islimittime, DateTime starttime, DateTime endtime, int limitgrade, int showstate, int commendstate, string forbidreason, int sort, IFormFile goodsImage)
        {
            try
            {
                var entity = PointsGoods.FindById(id);
                if (entity == null) return Json(new DResult { success = false, msg = GetResource("记录不存在") });

                // 基本校验（与新增保持一致）
                if (name.IsNullOrEmpty()) return Json(new DResult { success = false, msg = GetResource("礼品名称为空") });
                if (goodsprice < 0) return Json(new DResult { success = false, msg = GetResource("商品价格不能为空") });
                if (goodspoints < 0) return Json(new DResult { success = false, msg = GetResource("兑换积分不能为空") });
                if (goodsstorage < 0) return Json(new DResult { success = false, msg = GetResource("库存不能为空") });
                if (goodsserial.IsNullOrEmpty()) return Json(new DResult { success = false, msg = GetResource("礼品编号不能为空") });

                if (islimittime == 1 && starttime >= endtime)
                    return Json(new DResult { success = false, msg = GetResource("限制时间时，开始时间必须小于结束时间") });
                // 可选图片上传（不传则保留原图）
                if (goodsImage != null && goodsImage.Length > 0)
                {
                    var (ok, url, err) = UploadHelper.ReplaceImageAsWebp(goodsImage,entity.Image, "Operation/PointProdGoods");
                    if (!ok) return Json(new DResult { success = false, msg = GetResource(err) });
                    entity.Image = url;
                }

                // 更新主表
                entity.Name = name;
                entity.Price = goodsprice;
                entity.Points = goodspoints;
                entity.Tag = goodstag;
                entity.Serial = goodsserial;
                entity.Storage = goodsstorage;
                entity.Show = showstate == 1;
                entity.Recommend = commendstate == 1;
                entity.Keywords = keywords;
                entity.Description = description;
                entity.Body = pgoods_body;
                entity.IsLimit = islimit == 1;
                entity.LimitNum = limitnum;
                entity.IsLimitTime = islimittime == 1;
                entity.StartTime = entity.IsLimitTime ? UnixTime.ToTimestamp(starttime) : 0;
                entity.EndTime = entity.IsLimitTime ? UnixTime.ToTimestamp(endtime) : 0;
                entity.LimitMGrade = limitgrade;
                entity.Sort = sort;

                entity.Update();

                // 翻译表：按语言 Upsert
                if (LocalizationSettings.Current.IsEnable)
                {
                    var languageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                    foreach (var lang in languageList)
                    {
                        var lan = PointsGoodsLan.FindByPIdAndLId(entity.Id, lang.Id) ?? new PointsGoodsLan
                        {
                            PId = entity.Id,
                            LId = lang.Id
                        };

                        lan.Name = (GetRequest($"[{lang.Id}].name")).SafeString().Trim();
                        lan.Keywords = (GetRequest($"[{lang.Id}].keywords")).SafeString().Trim();
                        lan.Description = (GetRequest($"[{lang.Id}].description")).SafeString().Trim();
                        lan.Tag = (GetRequest($"[{lang.Id}].goodstag")).SafeString().Trim();
                        lan.Body = (GetRequest($"[{lang.Id}].pgoods_body")).SafeString().Trim();

                        // 语言图片独立上传（可选）
                        var lanImage = Request.Form.Files[$"[{lang.Id}].goodsImage"];
                        if (lanImage != null && lanImage.Length > 0)
                        {
                            var (lanok, lanUrl, lanerr) = UploadHelper.ReplaceImageAsWebp(lanImage,lan.Image, "Operation/PointProdGoods");
                            if (!lanok) return Json(new DResult { success = false, msg = GetResource(lanerr) });
                            lan.Image = lanUrl;
                        }

                        if (lan.Id > 0) lan.Update();
                        else lan.Insert();
                    }
                }

                return Json(new DResult { success = true, msg = GetResource("编辑成功") });
            }
            catch (Exception ex)
            {
                return Json(new DResult { success = false, msg = GetResource("异常：") + ex.Message });
            }
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("上/下架礼品")]
        [HttpPost]
        public IActionResult SetShowState(long id, int state)
        {
            var entity = PointsGoods.FindById(id);
            if (entity == null) return Json(new DResult { success = false, msg = GetResource("记录不存在") });
            entity.Show = state == 1;
            entity.Update();
            PointsGoods.Meta.Cache.Clear("", true);
            return Json(new DResult { success = true, msg = GetResource("操作成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("设置推荐")]
        [HttpPost]
        public IActionResult SetRecommendState(long id, int state)
        {
            var entity = PointsGoods.FindById(id);
            if (entity == null) return Json(new DResult { success = false, msg = GetResource("记录不存在") });
            entity.Recommend = state == 1;
            entity.Update();
            PointsGoods.Meta.Cache.Clear("", true);
            return Json(new DResult { success = true, msg = GetResource("操作成功") });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除礼品")]
        public IActionResult DelPointsProd(string ids)
        {
            var list = PointsGoods.FindAllBypIds(ids);
            if (list.Count<=0)
                return Json(new DResult { success = false, msg = GetResource("请选择记录") });

            int count = 0;
            foreach (var item in list)
            {
                UploadHelper.DeleteImage(item.Image??"");
                item.Delete();
                count++;
            }
            var lanlist = PointsGoodsLan.FindAllBypIds(ids);
            foreach (var item in lanlist)
            {
                UploadHelper.DeleteImage(item.Image ?? "");
                item.Delete();
            }
            if (count>0)
            {
                PointsGoods.Meta.Cache.Clear("", true);
                return Json(new DResult { success = true, msg = GetResource("成功删除")+count+GetResource("条记录") });
            }
            return Json(new DResult { success = false, msg = GetResource("未删除有效记录") });
        }

    }
}
