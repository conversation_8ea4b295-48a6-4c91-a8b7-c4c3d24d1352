﻿using B2B2CShop.Dto;
using B2B2CShop.Entity;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using Pek;
using Pek.DsMallUI;
using Pek.Models;
using Pek.NCube;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    /// <summary>
    /// 电话区号管理
    /// </summary>
    [DisplayName("区号管理")]
    [Description("区号管理")]
    [AdminArea]
    [DHMenu(77, ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/TelAreaCode", CurrentMenuName = "TelAreaCode", CurrentIcon = "&#xe720;", LastUpdate = "20241205")]
    public class TelAreaCodeController : PekCubeAdminControllerX
    {
        public IActionResult Index(string key)
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.key = key;
            if (key.IsNotNullOrEmpty())
            {
                viewModel.list = TelAreaCode.Search(key,null);
            }
            else
            {
                viewModel.list = TelAreaCode.FindAll();
            }
            return View(viewModel);
        }
        [EntityAuthorize(PermissionFlags.Insert)]
        [DisplayName("新增区号页面")]
        public IActionResult AddAreaCode()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.CountryList = Country.FindAll().Select(e => new CountryDto { Id = e.Id, Name = CountryLan.FindNameByCIdAndLId(e.Id, 1, true), TwoLetterIsoCode = e.TwoLetterIsoCode }).ToList();
            return View(viewModel);
        }
        [EntityAuthorize(PermissionFlags.Insert)]
        [HttpPost]
        [DisplayName("新增区号接口")]
        public IActionResult AddAreaCode(string areacode, string description , string CountryId)
        {
            if(areacode.IsNullOrEmpty()) return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
            if(description.IsNullOrEmpty()) return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
            var model = new TelAreaCode
            {
                AreaCode = areacode,
                Description = description,
                CountryId = CountryId
            };
            model.Insert();
            return MessageTip(GetResource("保存成功"));
        }
        [EntityAuthorize(PermissionFlags.Insert)]
        [DisplayName("编辑区号页面")]
        public IActionResult EditAreaCode(int id)
        {
            if(id.IsNull()) return Prompt(new PromptModel { Message = GetResource("区号ID为空") });
            var telareacode = TelAreaCode.FindById(id);
            if(telareacode.IsNull()) return Prompt(new PromptModel { Message = GetResource("找不到记录") });
            dynamic viewModel = new ExpandoObject();
            viewModel.telareacode = telareacode;
            viewModel.CountryList = Country.FindAll().Select(e => new CountryDto { Id = e.Id, Name = CountryLan.FindNameByCIdAndLId(e.Id, 1, true), TwoLetterIsoCode = e.TwoLetterIsoCode }).ToList();
            return View(viewModel);
        }
        [EntityAuthorize(PermissionFlags.Insert)]
        [HttpPost]
        [DisplayName("编辑区号接口")]
        public IActionResult EditAreaCode(int id,string areacode, string description, string CountryId)
        {
            if (areacode.IsNullOrEmpty()) return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
            if (description.IsNullOrEmpty()) return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
            var telareacode = TelAreaCode.FindById(id);
            if (telareacode==null)
            {
                return Prompt(new PromptModel { Message = GetResource("区号信息为空") });
            }
            telareacode.AreaCode = areacode;
            telareacode.Description = description;
            telareacode.CountryId = CountryId;
            telareacode.Update();
            return MessageTip(GetResource("保存成功"));
        }
        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除")]
        public IActionResult Delete(string ids)
        {
            var res = new DResult();

            if (ids.IsNullOrEmpty()) return Prompt(new PromptModel { Message = GetResource("区号ID为空") });
            var models = TelAreaCode.FindAll(TelAreaCode._.Id.In(ids.SplitAsInt(",")));
            int index = 0;
            foreach (var model in models)
            {
                if (model==null) continue;
                var count = BuyerAddress.FindCount(BuyerAddress._.AreaCode == model.AreaCode);
                if (count > 0) continue;
                model.Delete();
                index++;
            }
            res.success = true;
            res.code = 10000;
            res.msg = GetResource("成功删除记录条数") + "：" + index;
            return Json(res);
        }
    }
}
