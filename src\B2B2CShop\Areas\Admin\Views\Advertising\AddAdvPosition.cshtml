﻿@{
    var localizationSettings = LocalizationSettings.Current;
}
<style asp-location="true">
    .page {
        min-height: 415px
    }

    .layui-tab-brief > .layui-tab-title .layui-this {
        color: #419DFD !important;
    }

        .layui-tab-brief > .layui-tab-more li.layui-this:after,
        .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #419DFD !important;
        }

    .layui-tab-content {
        padding: 0px !important;
    }

    .bd {
        border: 2px solid red;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("广告管理")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("广告位")</span></a></li>
                <li><a href="@Url.Action("AdvertisingView")"><span>@T("广告")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("新增广告位")</span></a></li>
            </ul>
        </div>
    </div>

    <form id="goods_class_form" action="@Url.Action("AddAdvPosition")" enctype="multipart/form-data" method="post">
        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">
                <table class="ds-default-table">
                    <tbody>
                        <tr class="noborder">
                            <td class="required w60">
                                <label class="gc_name validation">@T("名称"):</label>
                            </td>
                            <td>
                                <input type="text" name="Name" class="txt w400" required>
                            </td>
                            <td class="vatop tips"></td>
                        </tr>
                        <tr class="noborder">
                            <td class="required w60">
                                <label class="gc_name">@T("简介"):</label>
                            </td>
                            <td>
                                <textarea style="min-height:150px;" name="Introduction" class="textarea h60 w400"></textarea>
                            </td>
                        </tr>
                        <tr class="noborder">
                            <td class="required w60">
                                <label class="gc_name validation">@T("宽度"):</label>
                            </td>
                            <td>
                                <input id="firstWeight" name="Width" class="input-txt w60" type="number" step="1" min="1" required>
                            </td>
                        </tr>
                        <tr class="noborder">
                            <td class="required w60">
                                <label class="gc_name validation">@T("高度"):</label>
                            </td>
                            <td>
                                <input id="firstWeight" name="Height" class="input-txt w60" type="number" step="1" min="1" required>
                            </td>
                        </tr>
                        <tr class="tfoot">
                            <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
</div>
<script asp-location="Footer">
    //颜色选择
    layui.use('colorpicker',function(){
        var colorpicker = layui.colorpicker;
         colorpicker.render({
             elem:'#color1',
             predefine:true,
             done:function(color){
                 $("#Color").val(color);
             }
         })
    })
</script>
