﻿@using B2B2CShop.Dto
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("会员等级折扣")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("店铺折扣")</span></a></li>
                <li><a href="@Url.Action("GoodsDiscount")" ><span>@T("商品折扣")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("MemberDiscountSetting ")','@T("折扣设置")')"><span>@T("折扣设置")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("店铺名称")</dt>
                <dd><input type="text" value="@Model.name" name="name" id="name" class="txt"></dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value='@T("搜索")'>
            </div>
        </div>
    </form>


    <table class="ds-default-table">
        <thead>
            <tr class="thead">
                <th class="w200">@T("店铺名称")</th>
                <th class="w120">@T("店铺用户名")</th>
                <th class="w120">@T("卖家账号")</th>
                <th class="w120">@T("店铺折扣")</th>
                <th class="align-center w120">@T("开启会员折扣")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (Store item in Model.list)
            {
                <tr class="hover edit">
                    <td>
                        <a href="@Url.Action("ProviderDetail","Supplier",new {Area = "",id = item.Id})" target="_blank">@item.Name</a>
                    </td>
                    <td>@item.UName</td>
                    <td>@item.SellerName</td>
                    <td>
                        @foreach (MgDiscountDto diccount in item.MgDiscountDto)
                        {
                            @(diccount.GradeName +"/" +diccount.Discount + T("折")) <br/>
                        }
                    </td>
                    <td class="align-center w72">@(item.MgDiscountState?T("开启"):T("关闭"))</td>
                </tr>
            }
        </tbody>
    </table>
        <ul class="pagination">
            @Html.Raw(Model.PageHtml)
        </ul>
</div>
