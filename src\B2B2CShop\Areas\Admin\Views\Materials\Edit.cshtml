﻿@{
    // script
    PekHtml.AppendScriptParts(ResourceLocation.Head, "/static/plugins/js/jquery-file-upload/jquery.fileupload.js");

    // css
    PekHtml.AppendScriptParts(ResourceLocation.Footer, "/static/plugins/ueditor/lang/zh-cn/zh-cn.js");
    PekHtml.AppendScriptParts(ResourceLocation.Footer, "/static/plugins/ueditor/ueditor.all.min.js");
    PekHtml.AppendScriptParts(ResourceLocation.Footer, "/static/plugins/ueditor/ueditor.config.js");

    // PekHtml.AppendScriptParts(ResourceLocation.Footer, "~/layui/layui.js");

    var storeList = Model.storeList as IEnumerable<SelectListItem>;

    var merchantMaterial = Model.merchantMaterial as MerchantMaterial;
}
<style asp-location="true">
    .page {
    min-height: 430px;
    }

    .html {
    background-color: #FFF;
    }

    .backgroundColor {
    background: #FAFAFA none repeat scroll 0 0;
    border-width: 1px;
    border: 1px solid #ccc;
    }

    .width300 {
    min-width: 300px;
    }

    .red {
    color: red
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("编辑-物料")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("Edit",new { Id = merchantMaterial!.Id})" class="current"><span>@T("修改")</span></a></li>
            </ul>
        </div>
    </div>

    @using (Html.BeginForm("Edit", "Materials", FormMethod.Post, new { id = "form2", enctype = "multipart/form-data" }))
    {
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120"><span class="red">*</span>@T("物料名称")</td>
                    <td class="vatop rowform"><input type="text" name="name" value="@merchantMaterial.Name" class="width300" /></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("所属店铺")</td>
                    <td class="vatop rowform">
                        <select id="store" name="storeId">
                            <!option value="0">
                                @T("默认")
                            </!option>
                            @foreach (SelectListItem item in storeList??[])
                            {
                                if (item.Value == merchantMaterial.StoreId.ToString())
                                {
                                    <!option value="@item.Value" @("selected")>
                                        @item.Text
                                    </!option>
                                }
                                else
                                {
                                    <!option value="@item.Value">
                                        @item.Text
                                    </!option>
                                }
                            }
                        </select>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("备注")</td>
                    <td class="vatop rowform"><textarea class="layui-textarea" name="remark" value="@merchantMaterial.Remark">@merchantMaterial.Remark</textarea></td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" style="min-width:100px;margin-left:150px;" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>

    }
</div>
<script asp-location="Footer">
        $(function () {
        $('#form2').validate({
            errorPlacement: function (error, element) {
                // console.log();
                error.appendTo(element.parent().parent().find('td:last'));
            },
             rules: {
                 name : {
                    required: true
                 }
            },
            messages: {
                name : {
                    required: '@T("物料名称不能为空")'
                }
            }
        });
    })
</script>
