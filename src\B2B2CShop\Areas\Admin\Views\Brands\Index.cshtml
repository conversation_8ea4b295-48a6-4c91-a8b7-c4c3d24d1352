﻿@using Pek.IO

<style>
    img{
        object-fit: contain;
        max-width: 250px;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("品牌管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
        <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
        <li><a href="javascript:dsLayerOpen('@Url.Action("AddBrand")','@T("添加")')" ><span>@T("添加")</span></a></li>
        <li><a href="@Url.Action("ApplyIndex")" ><span>@T("待审核")</span></a></li>
    </ul>
        </div>
    </div>

    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("品牌名称")</dt>
                <dd><input class="txt" name="brandName" id="search_brand_name" value="" type="text"></dd>
            </dl>
            <dl>
                <dt>@T("所属分类")</dt>
                <dd><input class="txt" name="classificationName" id="search_brand_class" value="" type="text"></dd>
            </dl>
            <div class="btn_group">
                 <a href="javascript:document.formSearch.submit();" class="btn " title="@T("查询")">@T("查询")</a>
                                  <a class="btn btn-default" href="javascript:export_xls('@Url.Action("ExportApprovedBrands")')"><span>@T("导出Excel")</span></a>
            </div>
        </div>
        <input type="hidden" value="" name="export">
    </form>
    
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="@T("提示相关设置操作时应注意的要点")">@T("操作提示")</h4>
            <span id="explanationZoom" title="@T("收起提示")" class="arrow"></span>
        </div>
        <ul>
            <li>@T("商家添加商品时可以选择商品所属品牌，用户可以选择品牌查询该品牌所属的商品")</li>
            <li>@T("被推荐的品牌，将在前台品牌推荐模块处显示")</li>
            <li>@T("品牌将按品牌分类分组，相同分类的品牌为一组")</li>
        </ul>
    </div>
    <table class="ds-default-table">
        <thead>
            <tr>
                <th></th>
                <th>@T("品牌ID")</th>
                <th>@T("排序")</th>
                <th>@T("品牌名称")</th>
                <th>@T("所属分类")</th>
                <th>@T("品牌图片标识")</th>
                <th>@T("展示方式")</th>
                <th>@T("推荐")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (Brand item in Model.list)
            {
                <tr>
                    <th><input type="checkbox" class="checkitem" value="@item.Id"></th>
                    <td>@item.Id</td>
                    <td>@item.Sort</td>
                    <td>@item.Name</td>
                    <td>@item.BClass</td>
                    <td>
                        @if (string.IsNullOrWhiteSpace(item.Pic))
                        {
                            <img src="/uploads/common/default_brand_image.gif" onerror=""/>
                        }
                        else
                        {
                            <img src="@FileUtil.JoinPath(DHSetting.Current.CurDomainUrl, $"{item.Pic}")" onerror=""/>
                        }
                    </td>
                    <td> @(item.ShowType == 1? T("文字") : T("图片") ) </td>
                    <td> @(item.Recommend? T("是") : T("否")  )     </td>
                    <td>
                        <!--  href="javascript:dsLayerOpen('@Url.Action("EditBrand", new { id = item.Id })')" -->
                        <a  href="javascript:dsLayerOpen('@Url.Action("EditBrand", new { id = item.Id })')" class="dsui-btn-edit"><i
                                class="iconfont"></i>@T("编辑")</a>
                        <a href="javascript:;"
                            onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { ids = item.Id })','@T("您确定要删除吗?")')"
                            class="dsui-btn-del">
                            <i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
            @if(Model.list.Count == 0)
                {
                    <tr class="no_data">
                        <td colspan="10">没有符合条件的记录</td>
                    </tr>
                }  
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small"
                        onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>

</div>
<script asp-location="Footer">
    function submit_delete(items) {
        _uri = "@Url.Action("Delete")?ids=" + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
    }
</script>
