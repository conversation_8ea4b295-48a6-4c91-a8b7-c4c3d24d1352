﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("分销设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("DistributionSetting")" class="current"><span>@T("分销设置")</span></a></li>
                <li><a href="@Url.Action("DistributionGoods")"><span>@T("分销商品")</span></a></li>
                <li><a href="@Url.Action("DistributionMember")"><span>@T("分销员管理")</span></a></li>
                <li><a href="@Url.Action("DistributionMemberLevel")"><span>@T("分销员等级")</span></a></li>
                <li><a href="@Url.Action("DistributionOrder")"><span>@T("分销员订单")</span></a></li>
            </ul>
        </div>
    </div>
    <form id="inviter_form" method="post" enctype="multipart/form-data" name="form1" action="">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120">@T("分销开关")</td>
                    <td class="vatop">
                        <div class="onoff">
                            <label for="DistributionEnable1"
                                class="cb-enable @(Model.DistributionEnable == 1 ? "selected" : "")">@T("是")</label>
                            <label for="DistributionEnable0"
                                class="cb-disable @(Model.DistributionEnable == 0 ? "selected" : "")">@T("否")</label>
                            <input id="DistributionEnable1" name="DistributionEnable" value="1" type="radio"
                                @(Model.DistributionEnable == 1 ? "checked=\"checked\"" : "")>
                            <input id="DistributionEnable0" name="DistributionEnable" value="0" type="radio"
                                @(Model.DistributionEnable == 0 ? "checked=\"checked\"" : "")>
                        </div>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("分销级别:")</label></td>
                    <td class="vatop">
                        <label>
                            <input type="radio" value="1" name="DistributionLevel"
                                @(Model.DistributionLevel == 1 ? "checked=\"checked\"" : "")>@T("一级分销") </label>
                        <label>
                            <input type="radio" value="2" name="DistributionLevel"
                                @(Model.DistributionLevel == 2 ? "checked=\"checked\"" : "")>@T("二级分销") </label>
                        <label>
                            <input type="radio" value="3" name="DistributionLevel"
                                @(Model.DistributionLevel == 3 ? "checked=\"checked\"" : "")>@T("三级分销") </label>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("详情页显示分销佣金")</td>
                    <td class="vatop">
                        <div class="onoff">
                            <label for="DistributionShowCommission1"
                                class="cb-enable @(Model.DistributionShowCommission == 1 ? "selected" : "")">@T("是")</label>
                            <label for="DistributionShowCommission0"
                                class="cb-disable @(Model.DistributionShowCommission == 0 ? "selected" : "")">@T("否")</label>
                            <input id="DistributionShowCommission1" name="DistributionShowCommission" value="1"
                                type="radio" @(Model.DistributionShowCommission == 1 ? "checked=\"checked\"" : "")>
                            <input id="DistributionShowCommission0" name="DistributionShowCommission" value="0"
                                type="radio" @(Model.DistributionShowCommission == 0 ? "checked=\"checked\"" : "")>
                        </div>
                    </td>
                    <td class="vatop tips">@T("开启后，分销员可以在商品详情页看到分销佣金")</td>
                </tr>

                <tr class="noborder">
                    <td class="required w120">@T("分销员返佣")</td>
                    <td class="vatop">
                        <div class="onoff">
                            <label for="DistributionReturnCommissionEnable1"
                                class="cb-enable @(Model.DistributionReturnCommissionEnable == 1 ? "selected" : "")">@T("是")</label>
                            <label for="DistributionReturnCommissionEnable0"
                                class="cb-disable @(Model.DistributionReturnCommissionEnable == 0 ? "selected" : "")">@T("否")</label>
                            <input id="DistributionReturnCommissionEnable1" name="DistributionReturnCommissionEnable"
                                value="1" type="radio"
                                @(Model.DistributionReturnCommissionEnable == 1 ? "checked=\"checked\"" : "")>
                            <input id="DistributionReturnCommissionEnable0" name="DistributionReturnCommissionEnable"
                                value="0" type="radio"
                                @(Model.DistributionReturnCommissionEnable == 0 ? "checked=\"checked\"" : "")>
                        </div>
                    </td>
                    <td class="vatop tips">@T("开启后，分销员自己购买将获得一级分销佣金")</td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("分销员审核")</td>
                    <td class="vatop">
                        <div class="onoff">
                            <label for="DistributionAuditEnable1"
                                class="cb-enable @(Model.DistributionAuditEnable == 1 ? "selected" : "")">@T("是")</label>
                            <label for="DistributionAuditEnable0"
                                class="cb-disable @(Model.DistributionAuditEnable == 0 ? "selected" : "")">@T("否")</label>
                            <input id="DistributionAuditEnable1" name="DistributionAuditEnable" value="1" type="radio"
                                @(Model.DistributionAuditEnable == 1 ? "checked=\"checked\"" : "")>
                            <input id="DistributionAuditEnable0" name="DistributionAuditEnable" value="0" type="radio"
                                @(Model.DistributionAuditEnable == 0 ? "checked=\"checked\"" : "")>
                        </div>
                    </td>
                    <td class="vatop tips">@T("开启后，分销员需要审核后才能分销")</td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("分销员条件:")</label></td>
                    <td class="vatop">
                        <label>
                            <input type="radio" value="0" name="DistributionConditions"
                                @(Model.DistributionConditions == 0 ? "checked=\"checked\"" : "")>@T("无") </label>
                        <label>
                            <input type="radio" value="1" name="DistributionConditions" @(Model.DistributionConditions > 0 ? "checked=\"checked\"" : "")>@T("历史消费金额达到")<input type='text'
                                name='DistributionConditionAmount' id='DistributionConditionAmount'
                                value='@Model.DistributionConditions' class='input-txt valid' />
                        </label>

                    </td>
                    <td class="vatop tips">@T("元")</td>
                </tr>

                <tr class="noborder">
                    <td class="required w120">@T("分销背景图片")</td>
                    <td class="vatop">
                        <div style="margin-bottom:8px;" >
                            <img id="buttonImagePreview" src="@Model.DistributionBackgroundImage" style="max-width:200px; background-color:gray" />
                        </div>
                        <span class="type-file-box">
                            <input type='button' name='button' id='button1' value='@T("上传")' class='type-file-button' />
                            <input name="DistributionBackgroundImage" type="file" class="type-file-file"
                                id="DistributionBackgroundImage" size="30" hidefocus="true"
                                ds_type="change_inviter_back" style="display:none;">
                        </span>
                    </td>
                    <td class="vatop tips">
                        <p class="notic">@T("推广背景图片，最佳显示尺寸为360*578像素")</p>
                    </td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("1级佣金比例")</td>
                    <td class="vatop">
                        <input id="DistributionCommissionLevel1" name="DistributionCommissionLevel1"
                            value='@(Model.DistributionCommissionLevel1 ?? "")' class="input-txt" type="text">%
                        <span class="err"></span>
                    </td>
                    <td class="vatop tips">@T("基数为分销金额")</td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("2级佣金比例")</td>
                    <td class="vatop">
                        <input id="DistributionCommissionLevel2" name="DistributionCommissionLevel2"
                            value='@(Model.DistributionCommissionLevel2 ?? "")' class="input-txt" type="text">%
                    </td>
                    <td class="vatop tips">@T("基数为分销金额")</td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("3级佣金比例")</td>
                    <td class="vatop">
                        <input id="DistributionCommissionLevel3" name="DistributionCommissionLevel3"
                            value='@(Model.DistributionCommissionLevel3 ?? "")' class="input-txt" type="text">%
                    </td>
                    <td class="vatop tips">@T("基数为分销金额")</td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td></td>
                    <td colspan="15"><input class="btn" type="submit" value='@T("提交")' /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script type="text/javascript">
    (function () {
        var form = document.getElementById('inviter_form');
        if (!form) return;

        // bind upload button to file input
        var uploadBtn = document.getElementById('button1');
        var fileInput = document.getElementById('DistributionBackgroundImage');
        var topPreview = document.getElementById('buttonImagePreview');
        if (uploadBtn && fileInput) {
            uploadBtn.addEventListener('click', function () { fileInput.click(); });
        }

        // preview selected image immediately (display on button area)
        if (fileInput && topPreview) {
            fileInput.addEventListener('change', function () {
                var file = this.files && this.files[0];
                if (!file) return;
                var reader = new FileReader();
                reader.onload = function (e) {
                    topPreview.src = e.target.result;
                };
                reader.readAsDataURL(file);
            });
        }

        // existing submit handler
        form.addEventListener('submit', function (e) {
            e.preventDefault();
            var btn = form.querySelector('input[type=submit]');
            if (btn) {
                btn.disabled = true;
                btn._origText = btn.value;
                btn.value = '@T("提交中...")';
            }

            var fd = new FormData(form);
            fetch('@Url.Action("DistributionSetting")', { method: 'POST', body: fd, credentials: 'same-origin' })
                .then(function (res) { if (!res.ok) throw new Error('Network'); return res.json().catch(function () { return null; }); })
                .then(function (data) {
                    if (data && data.success) {
                        layer.msg(data.msg || '@T("保存成功")', { icon: 1 });
                        setTimeout(function () { location.reload(); }, 700);
                    } else {
                        layer.msg((data && data.msg) || '@T("保存失败")', { icon: 2 });
                    }
                }).catch(function (err) {
                    console.error(err);
                    layer.msg('@T("网络错误，请重试")', { icon: 2 });
                }).finally(function () {
                    if (btn) {
                        btn.disabled = false;
                        btn.value = btn._origText || '@T("提交")';
                    }
                });
        });
    })();
</script>