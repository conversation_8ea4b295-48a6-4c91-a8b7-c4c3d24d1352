﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@model Goods
@{
    var IsEnable = LocalizationSettings.Current.IsEnable;
    var goodsskus = GoodsSKUDetail.FindAllByGoodsId(Model.Id);

}

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("商品管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("GoodsDetail")" class="current"><span>@T("查看")</span></a></li>
            </ul>
        </div>
    </div>
    <div class="ds-default-form">
        <div class="layui-tab layui-tab-brief  Lan" lay-filter="docDemoTabBrief">
            @if (IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准"):</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="ncap-form-default">
                        <dl>
                            <dt>@T("商品名称")：</dt>
                            <dd>@Model.Name</dd>
                        </dl>
                        <dl>
                            <dt>@T("商品卖点")：</dt>
                            <dd>@Model.AdvWord</dd>
                        </dl>
                        <dl>
                            <dt>@T("SEO标题")：</dt>
                            <dd>@Model.SeoTitle</dd>
                        </dl>
                        <dl>
                            <dt>@T("SEO关键字")：</dt>
                            <dd>@Model.SeoKeys</dd>
                        </dl>
                        <dl>
                            <dt>@T("SEO描述")：</dt>
                            <dd>@Model.SeoDescription</dd>
                        </dl>
                        <dl>
                            <dt>@T("电脑端描述")：</dt>
                            <dd id="dsProductDetails">
                                <div id="panel-1" class="ui-tabs-panel">
                                    <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.config.js"></script>
                                    <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.all.min.js"></script>
                                    <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/lang/zh-cn/zh-cn.js"></script>
                                    <script type="text/javascript">

                                        var ue = UE.getEditor('goods_body');
                                        if ("") {
                                        ue.ready(function () {
                                        this.setContent("");
                                        })
                                        }
                                    </script>
                                    <textarea name="goodsBody" id="goods_body" readonly>@Model.GoodsCommon?.GoodsBody</textarea>
                                </div>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("手机端描述")：</dt>
                            <dd id="dsProductDetails">
                                <div id="panel-2" class="ui-tabs-panel ui-tabs-hide">
                                    <script type="text/javascript">

                                        var ue = UE.getEditor('goods_Mobilebody');
                                        if ("") {
                                        ue.ready(function () {
                                        this.setContent("");
                                        })
                                        }
                                    </script>
                                    <textarea name="goods_Mobilebody" id="goods_Mobilebody" readonly>@Model.GoodsCommon?.MobileBody</textarea>
                                </div>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("商品视频")</dt>
                            <dd>
                                <div class="dssc-goods-default-pic">
                                    <div class="goodspic-uplaod">
                                        <div class="upload-thumb" style="position:relative;">
                                            <div class="plyr__video-embed" id="player" style="height:300px;width:200px">
                                                <iframe id="vimeoPlayer" src="@Model.GoodsVideoName" allowfullscreen allowtransparency allow="autoplay"></iframe>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("SKU信息")：</dt>
                            <dd>
                                @{

                                    <table class="sku-table">
                                        <thead>
                                            <tr>
                                                <th>@T("SKU属性")</th>
                                                <th>@T("物料名称")</th>
                                                <th>@T("市场价")</th>
                                                <th>@T("售价")</th>
                                                <th>@T("阶梯价")</th>
                                                <th>@T("图片")</th>
                                                <th>@T("价格锁定")</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var item in goodsskus)
                                            {
                                                var tiereds = GoodsTieredPrice.FindAllBySkuId(item.Id);
                                                var images = GoodsImages.FindAllBySkuId(item.Id);
                                                <tr>
                                                    <td>@item.SpecValueDetail(0)</td>
                                                    <td>@(MerchantMaterial.FindById(item.MaterialId)?.Name)</td>
                                                    <td>@item.GoodsMarketPrice</td>
                                                    <td>@item.GoodsPrice</td>
                                                    <td>
                                                        @foreach (var tiered in tiereds)
                                                        {
                                                            <p>@tiered.MinQuantity + @tiered.Price</p>
                                                        }
                                                    </td>
                                                    <td>
                                                        @foreach (var image in images)
                                                        {
                                                            var imageUrl = AlbumPic.FindByNameAndSId(image.ImageUrl, Model.StoreId)?.Cover;
                                                            if (imageUrl != null)
                                                            {
                                                                <a href="@imageUrl" data-lightbox="material-images" class="">
                                                                    <img src="@imageUrl" alt="@Model.Name" style="max-width: 100px; max-height: 100px;">
                                                                </a>
                                                            }
                                                        }
                                                    </td>
                                                    <td>
                                                        <span class="op-cell" data-id="@item.Id">
                                                            @if (item.PriceLock == 1)
                                                            {
                                                                <span class="locked-label">@T("已锁定")</span>
                                                            }
                                                            else
                                                            {
                                                                <a href="javascript:;" class="dsui-btn lock-btn" data-id="@item.Id">
                                                                    <i class="iconfont">&#xe768;</i>@T("锁定")
                                                                </a>
                                                            }
                                                        </span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                }
                            </dd>
                        </dl>
                    </div>
                </div>
                @if (IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        var goodslan = GoodsLan.FindByGIdAndLId(Model.Id, item.Id);
                        <div class="layui-tab-item">
                            <div class="ncap-form-default">
                                <dl>
                                    <dt>@T("商品名称")：</dt>
                                    <dd>@goodslan.Name</dd>
                                </dl>
                                <dl>
                                    <dt>@T("商品卖点")：</dt>
                                    <dd>@goodslan.AdvWord</dd>
                                </dl>
                                <dl>
                                    <dt>@T("SEO标题")：</dt>
                                    <dd>@goodslan.SeoTitle</dd>
                                </dl>
                                <dl>
                                    <dt>@T("SEO关键字")：</dt>
                                    <dd>@goodslan.SeoKeys</dd>
                                </dl>
                                <dl>
                                    <dt>@T("SEO描述")：</dt>
                                    <dd>@goodslan.SeoDescription</dd>
                                </dl>
                                <dl>
                                    <dt>@T("电脑端描述")：</dt>
                                    <dd id="dsProductDetails">
                                        <div id="panel-1" class="ui-tabs-panel">
                                            <script type="text/javascript">
                                                var ue@(item.Id) = UE.getEditor('<EMAIL>');
                                                if ("") {
                                                ue@(item.Id).ready(function () {
                                                this.setContent("");
                                                })
                                                }

                                            </script>
                                            <textarea name="<EMAIL>" id="<EMAIL>" readonly>@goodslan?.Content</textarea>
                                        </div>
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>@T("手机端描述")：</dt>
                                    <dd id="dsProductDetails">
                                        <div id="panel-2" class="ui-tabs-panel ui-tabs-hide">
                                            <script type="text/javascript">
                                                var ue@(item.Id) = UE.getEditor('goods_Mobilebody_@(item.Id)');
                                                if ("") {
                                                ue@(item.Id).ready(function () {
                                                this.setContent("");
                                                })
                                                }
                                            </script>
                                            <textarea name="goods_Mobilebody_@(item.Id)" id="goods_Mobilebody_@(item.Id)" readonly>@goodslan?.MobileContent</textarea>
                                        </div>
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>@T("商品视频")</dt>
                                    <dd>
                                        <div class="dssc-goods-default-pic">
                                            <div class="goodspic-uplaod">
                                                <div class="upload-thumb" style="position:relative;" >
                                                    <div class="plyr__video-embed" id="player_@(item.Id)" style="height:300px;width:200px">
                                                        <iframe id="vimeoPlayer_@(item.Id)" src="@goodslan?.GoodsVideoName" allowfullscreen allowtransparency allow="autoplay"></iframe>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>@T("SKU信息")：</dt>
                                    <dd>
                                        @{

                                            <table class="sku-table">
                                                <thead>
                                                    <tr>
                                                        <th>@T("SKU属性")</th>
                                                        <th>@T("物料名称")</th>
                                                        <th>@T("市场价")</th>
                                                        <th>@T("售价")</th>
                                                        <th>@T("阶梯价")</th>
                                                        <th>@T("图片")</th>
                                                        <th>@T("价格锁定")</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var goodssku in goodsskus)
                                                    {
                                                        var tiereds = GoodsTieredPrice.FindAllBySkuId(goodssku.Id);
                                                        var images = GoodsImagesLan.FindAllBySkuIdAndLId(goodssku.Id, item.Id);
                                                        <tr>
                                                            <td>@goodssku.SpecValueDetail(item.Id)</td>
                                                            <td>@(MerchantMaterial.FindById(goodssku.MaterialId)?.Name)</td>
                                                            <td>@goodssku.GoodsMarketPrice</td>
                                                            <td>@goodssku.GoodsPrice</td>
                                                            <td>
                                                                @foreach (var tiered in tiereds)
                                                                {
                                                                    <p>@tiered.MinQuantity + @tiered.Price</p>
                                                                }
                                                            </td>
                                                            <td>
                                                                @foreach (var image in images)
                                                                {
                                                                    var imageUrl = AlbumPic.FindByNameAndSId(image.ImageUrl, Model.StoreId)?.Cover;
                                                                    if (imageUrl != null)
                                                                    {
                                                                        <a href="@imageUrl" data-lightbox="material-images" class="">
                                                                            <img src="@imageUrl" alt="@Model.Name" style="max-width: 100px; max-height: 100px;">
                                                                        </a>
                                                                    }
                                                                }
                                                            </td>
                                                            <td>
                                                                <span class="op-cell" data-id="@goodssku.Id">
                                                                    @if (goodssku.PriceLock == 1)
                                                                    {
                                                                        <span class="locked-label">@T("已锁定")</span>
                                                                    }
                                                                    else
                                                                    {
                                                                        <a href="javascript:;" class="dsui-btn lock-btn" data-id="@goodssku.Id">
                                                                            <i class="iconfont">&#xe768;</i>@T("锁定")
                                                                        </a>
                                                                    }
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        }
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    }
                }
            </div>
            <div class="ncap-form-default">
                <dl>
                    <dt>@T("商品分类属性")</dt>
                    <dd>
                        @{
                            var goodsClassAttributes = ClassAttributes.FindAllByClassIdLan(Model.CId, 0);
                            var goodsAttribute = GoodsExAttributes.FindByGId(Model.Id);

                            <div class="attribute-wrapper">
                                @for (int i = 0; i < goodsClassAttributes.Count; i++)
                                {
                                    ClassAttributes classAttribute = goodsClassAttributes[i];
                                    var attributeStr = goodsAttribute != null ? goodsAttribute[classAttribute.MappingField ?? ""] : "";
                                    <div class="attribute-item">
                                        <label>@classAttribute.Name：</label>
                                        <input name="<EMAIL>" value="@attributeStr" type="text" class="text w100" readonly/>
                                    </div>
                                    @if ((i + 1) % 3 == 0 && i != goodsClassAttributes.Count - 1)
                                    {
                                        <div class="clear"></div>
                                    }
                                }
                            </div>
                        }
                    </dd>
                </dl>
            </div>
        </div>
        @if (!ViewBag.IsView)
        {
            <div class="btn_group">    
                <button type="button" class="btn" onclick="approveGoods()">@T("审核通过")</button>
                <button type="button" class="btn" style="background:red" onclick="rejectGoods()">@T("审核不通过")</button>
            </div>
        }
    </div>
</div>
<link rel="stylesheet" href="~/static/plugins/js/jquery.lightbox/css/lightbox.min.css">
<script src="~/static/plugins/js/jquery.lightbox/js/lightbox.min.js"></script>

<link href="~/public/static/plugins/plyr/plyr.css" rel="stylesheet" />
<script src="~/public/static/plugins/plyr/plyr.js"></script>
<script>
    // 存储所有播放器实例
    let players = {};

    // 初始化播放器函数
    function initPlayer(langId = '') {
        const playerId = langId ? `player_${langId}` : 'player';
        if (players[playerId]) {
            players[playerId].destroy();
        }
        players[playerId] = new Plyr(`#${playerId}`, {
            controls: ['play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
            ratio: '6:9',
            resetOnEnd: false
        });
    }

    // 页面加载时初始化默认播放器
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化默认语言的播放器
        initPlayer();

        // 初始化其他语言的播放器
        document.querySelectorAll('[id^="player_"]').forEach(player => {
            const langId = player.id.split('_')[1];
            initPlayer(langId);
        });
    });

    function loadVideo(langId = '') {
        const urlInput = document.getElementById(langId ? `videoUrl_${langId}` : 'videoUrl').value.trim();
        if (!urlInput) {
            alert('@T("请输入视频链接")');
            return;
        }

        // 提取视频ID和hash
        const urlMatch = urlInput.match(/video\/(\d+)(?:\?h=([a-zA-Z0-9]+))?/);
        if (!urlMatch) {
            alert('@T("请输入有效的Vimeo视频链接")');
            return;
        }

        const videoId = urlMatch[1];
        const hash = urlMatch[2] || '';
        const playerId = langId ? `player_${langId}` : 'player';

        // 先销毁现有播放器
        if (players[playerId]) {
            players[playerId].destroy();
        }

        // 重建播放器容器
        const playerContainer = document.getElementById(playerId);
        playerContainer.innerHTML = `
            <iframe
                id="vimeoPlayer${langId ? '_' + langId : ''}"
                src=""
                allowfullscreen
                allowtransparency
                allow="autoplay"
            ></iframe>
        `;

        // 构建嵌入URL
        let embedUrl = `${urlInput}`;
        embedUrl += (embedUrl.includes('?') ? '&' : '?') + 'background=1&autoplay=1&loop=1&byline=0&title=0&controls=0';

        // 更新iframe的src
        document.getElementById(`vimeoPlayer${langId ? '_' + langId : ''}`).src = embedUrl;

        // 重新初始化播放器
        setTimeout(() => {
            initPlayer(langId);
        }, 100);
    }
     // 审核通过函数
    // 审核通过函数
    function approveGoods() {
        layer.confirm('@T("确定要审核通过此商品吗？")', {
            title: '@T("确认操作")',
            btn: ['@T("确定")','@T("取消")']
        }, function(){
            const loading = layer.msg('@T("处理中...")', {icon: 16, shade: 0.3, time: 0});
            // 调用后端接口
            $.post('@Url.Action("AuditGoods", "Goods")', { 
                gId: '@Model.Id',
                ispass: true,
                reason: ''
            })
            .done(function(res) {
                layer.close(loading);
                if(res.success) {
                    layer.msg('@T("审核通过成功")', {icon: 1});
                    setTimeout(() => location.href = '@Url.Action("Index")', 500);
                } else {
                    layer.msg(res.msg || '@T("操作失败")', {icon: 2});
                }
            })
            .fail(function() {
                layer.close(loading);
                layer.msg('@T("请求失败，请重试")', {icon: 2});
            });
        });
    }
    
    // 审核不通过函数
    function rejectGoods() {
        layer.prompt({
            title: '@T("请输入拒绝原因")',
            formType: 2,
            btn: ['@T("确定")','@T("取消")']
        }, function(reason, index){
            layer.close(index);
            const loading = layer.msg('@T("处理中...")', {icon: 16, shade: 0.3, time: 0});
            
            // 调用后端接口
            $.post('@Url.Action("AuditGoods", "Goods")', { 
                gId: '@Model.Id',
                ispass: false,
                reason: reason
            })
            .done(function(res) {
                layer.close(loading);
                if(res.success) {
                    layer.msg('@T("拒绝成功")', {icon: 1});
                    setTimeout(() => location.href = '@Url.Action("Index")', 500);
                } else {
                    layer.msg(res.msg || '@T("操作失败")', {icon: 2});
                }
            })
            .fail(function() {
                layer.close(loading);
                layer.msg('@T("请求失败，请重试")', {icon: 2});
            });
        });
    }
    $(function () {
        $('body').on('click', '.lock-btn', function () {
            var $this = $(this);
            var id = $this.data('id');
            $.post('@Url.Action("LockPrice")', { skuId: id }, function (res) {
                if (res.success) {
                    $this.closest('.op-cell').html('<span class="locked-label">@T("已锁定")</span>');
                    layer.msg('@T("锁定成功")', { icon: 1,time:500}, function () {
                        // 刷新页面
                        location.reload();
                    });
                } else {
                    layer.msg(res.msg || '@T("操作失败")', { icon: 2 });
                }
            }).fail(function () {
                layer.msg('@T("请求失败，请重试")', { icon: 2 });
            });
        });
    });
</script>
<style>
    .sku-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
        font-size: 14px;
    }

        .sku-table th {
            background-color: #f8f8f8;
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            border: 1px solid #e8e8e8;
        }

        .sku-table td {
            padding: 12px 15px;
            border: 1px solid #e8e8e8;
            vertical-align: middle;
        }

        .sku-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .sku-table tr:hover {
            background-color: #f5f5f5;
        }

        .sku-table img {
            max-width: 80px;
            max-height: 80px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

    .tiered-price {
        margin: 3px 0;
        padding: 3px 5px;
        background: #f0f7ff;
        border-radius: 3px;
        display: inline-block;
    }

    .attribute-wrapper {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .attribute-item {
        padding: 10px;
        box-sizing: border-box;
        display: flex;
    }

    .attribute-item label {
        white-space: wrap;
        width: 150px;
        text-align: right; 
    }

    .attribute-item input {
        flex: 1; 
    }

    .clear {
        width: 100%;
    }
    
    .btn_group {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        padding: 10px;
        box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
        z-index: 1000;
    }

    .dsui-btn {
        display: inline-block;
        padding: 6px 12px;
        font-size: 12px;
        color: #fff;
        background-color: #1890ff;
        border-radius: 3px;
        text-decoration: none;
        transition: background-color .2s;
    }

        .dsui-btn:hover {
            background-color: #40a9ff;
            color: #fff;
        }
        
    .locked-label {
        display: inline-block;
        padding: 6px 12px;
        font-size: 12px;
        color: #999;
        background-color: #f5f5f5;
        border-radius: 3px;
        cursor: not-allowed;
    }
    
</style>