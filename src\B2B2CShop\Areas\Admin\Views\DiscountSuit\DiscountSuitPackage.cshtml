﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("优惠套装")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("活动")</span></a></li>
                <li><a href="@Url.Action("DiscountSuitPackage")" class="current"><span>@T("套餐列表")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("DiscountSuitSetting")','@T("设置")')"><span>@T("设置")</span></a></li>
            </ul>
        </div>
    </div>


    <form method="get" name="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("店铺名称")</dt>
                <dd>
                    <select id="storeId" name="storeId" class="form-control" style="width:200px;">
                        <!option value="">@T("全部")</!option>
                        @foreach (var store in Model.storelist)
                        {
                            <!option value="@store.Id" @(Model.storeId == store.Id ? "selected" : "")>@store.Name</!option>
                        }
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("状态")</dt>
                <dd>
                    <select name="status">
                        <option value="">@T("全部")</option>
                        <!option value="0" @(Model.status == 0 ? "selected" : "")>@T("关闭")</!option>
                        <!option value="1" @(Model.status == 1 ? "selected" : "")>@T("开启")</!option>
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <a href="javascript:document.formSearch.submit();" class="btn " title='@T("查询")'>@T("查询")</a>
            </div>
        </div>
    </form>
    <!-- 帮助 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="@T("提示相关设置操作时应注意的要点")">@T("操作提示")</h4>
            <span id="explanationZoom" title="收起提示" class="arrow"></span>
        </div>
        <ul>
            <li>@T("卖家购买优惠套装活动的列表。")</li>
        </ul>
    </div>

    <!-- 列表 -->
    <form id="list_form" method="post">
        <table class="ds-default-table">
            <thead>
                <tr class="thead">
                    <th>@T("店铺名称")</th>
                    <th class="align-center">@T("购买数量")</th>
                    <th class="align-center">@T("开始时间")</th>
                    <th class="align-center">@T("结束时间")</th>
                    <th class="align-center">@T("状态")</th>
                </tr>
            </thead>
            <tbody id="treet1">
                @foreach (BundlingQuota item in Model.list)
                {
                    <tr class="hover">
                        <td class="align-left"><a href="@Url.Action("ProviderDetail", "Supplier", new { Area = "", sid = item.StoreId })"><span>@item.StoreName</span></a></td>
                        <td class="align-center">@item.Months</td>
                        <td class="align-center"><span>@(UnixTime.ToDateTime(item.StartTime))</span></td>
                        <td class="align-center"><span>@(UnixTime.ToDateTime(item.EndTime))</span></td>
                        <td class="align-center">
                            @(item.State == 0 ? T("关闭") : T("开启"))
                        </td>
                    </tr>
                }
        </table>
        <ul class="pagination">
            @Html.Raw(Model.PageHtml)
        </ul>
    </form>
</div>
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>
<script type="text/javascript">
    $(function() {
        $('#storeId').select2({
            placeholder: '@T("请选择店铺")',
            allowClear: true,
        });
    });
</script>