﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("吸粉红包")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("AddBonus")','@T("添加")')"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>

    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("红包名称")</dt>
                <dd><input type="text" value="@Model.name" name="name" class="txt"></dd>
            </dl>
            <dl>
                <dt>@T("红包类型")</dt>
                <dd>
                    <select name="type">
                        <option value="">@T("请选择...")</option>
                        <!option value="1" @(Model.type==1?"selected":"")>@T("活动红包")</!option>
                        <!option value="2" @(Model.type==2?"selected":"")>@T("注册红包")</!option>
                        <!option value="3" @(Model.type==3?"selected":"")>@T("奖品红包")</!option>
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("红包状态")</dt>
                <dd>
                    <select name="state">
                        <option value="">@T("请选择...")</option>
                        <!option value="1" @(Model.state==1?"selected":"")>@T("正在进行")</!option>
                        <!option value="2" @(Model.state==2?"selected":"")>@T("已过期")</!option>
                        <!option value="3" @(Model.state==3?"selected":"")>@T("已失效")</!option>
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value='@T("搜索")'>
                <a href="/index.php/admin/Bonus/index.html" class="btn btn-default" title='@T("取消")'>@T("取消")</a>
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th>@T("红包名称")</th>
                <th>@T("红包类型")</th>
                <th>@T("红包总面额")</th>
                <th>@T("领取金额")</th>
                <th>@T("领取人数")</th>
                <th>@T("开始时间")</th>
                <th>@T("结束时间")</th>
                <th>@T("红包状态")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (Bonus item in Model.list)
            {
                <tr id="ds_row_8">
                    <td>@item.Name</td>
                    <td>@(item.Type == 1 ? T("活动红包") : item.Type == 2 ? T("注册红包") : item.Type == 3 ? T("奖品红包") : T("未知状态"))</td>
                    <td>@item.TotalPrice</td>
                    <td>@item.ReceivePrice</td>
                    <td>@item.ReceiveCount</td>
                    <td>@(UnixTime.ToDateTime(item.BeginTime))</td>
                    <td>@(UnixTime.ToDateTime(item.EndTime))</td>
                    <td>@(item.State == 1?T("正在进行"):item.State == 2?T("已过期"):item.State == 3?T("已失效"):T("未知状态"))</td>
                    <td>
                        <a href="javascript:dsLayerOpen('@Url.Action("BonusDetail", new { id = item.Id })','@T("详情")')" class="dsui-btn-view"><i class="iconfont"></i>@T("详情")</a>
                        @if (item.State == 1)
                        {
                            <a href="javascript:dsLayerOpen('@Url.Action("EditBonus", new { id = item.Id })','@T("编辑")')" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                            <a href="javascript:dsLayerConfirm('@Url.Action("InvalidBonus", new { id = item.Id })','@T("您确定要删除吗?")')" class="dsui-btn-del"><i class="iconfont"></i>@T("失效")</a>
                        }
                    </td>
                </tr>
            }
        </tbody>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>