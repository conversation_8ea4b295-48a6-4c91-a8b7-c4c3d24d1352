﻿using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using NewLife;
using Pek;
using Pek.DsMallUI;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;
using B2B2CShop.Entity;
using Pek.DsMallUI.Common;
using DH.Core.Domain.Localization;
using DH.Entity;
using System.Collections.Generic;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using XCode;

namespace B2B2CShop.Areas.Admin.Controllers
{
    /// <summary>
    /// 快递物流公司
    /// </summary>
    [DisplayName("快递物流公司")]
    [Description("用于快递物流公司的管理")]
    [AdminArea]
    [DHMenu(80, ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/Express", CurrentMenuName = "ExpressList", CurrentIcon = "&#xe69e;", LastUpdate = "20250206")]
    public class ExpressController : PekCubeAdminControllerX
    {
        /// <summary>
        /// 快递物流公司列表
        /// </summary>
        /// <param name="name"></param>
        /// <param name="code"></param>
        /// <param name="page"></param>
        /// <param name="limit"></param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("快递物流公司列表")]
        public IActionResult Index(string name, string code, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            //var pages = new PageParameter()
            //{
            //    PageIndex = page,
            //    PageSize = limit,
            //    RetrieveTotalCount = true,
            //    Sort = "Id",
            //    Desc = true
            //};

            name = name.SafeString().Trim();
            code = code.SafeString().Trim();

            //var localizationSettings = LocalizationSettings.Current;
            //if (localizationSettings.IsEnable)
            //{
            //    viewModel.list = Express.FindByNameAndCode(name, code, pages);
            //}
            //else
            //{

            //}

            //viewModel.list = Express.FindByNameAndCode(name, code, pages).Select(e => new Express { Id = e.Id, Name = InternationalExpressLan.FindByEIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name, Code = e.Code });

            var exp = new WhereExpression();
            if (!code.IsNullOrWhiteSpace()) exp &= Express._.Code == code;

            var data = Express.FindAll(exp)
                .Select(e => new Express { Id = e.Id, Name = InternationalExpressLan.FindByEIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name, Code = e.Code })
                .WhereIf(!name.IsNullOrWhiteSpace(), e => e.Name.Contains(name))
                .ToList();

            var list = data.Skip((page - 1) * limit).Take(limit).ToList();

            var pageCount = data.Count / limit;
            if (data.Count % limit > 0) pageCount++;

            viewModel.list = list;

            viewModel.page = page;
            viewModel.name = name;
            viewModel.code = code;

            //viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> { { "name", name }, { "code", code } });
            viewModel.PageHtml = PageHelper.CreatePage(page, data.Count, pageCount, Url.Action("Index"), new Dictionary<string, string> { { "name", name }, { "code", code } });
            return View(viewModel);
        }

        /// <summary>
        /// 新增快递物流公司
        /// </summary>
        /// <returns></returns>
        [DisplayName("新增快递物流公司")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult AddExpress()
        {
            ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            return View();
        }

        /// <summary>
        /// 新增快递物流公司
        /// </summary>
        /// <param name="name"></param>
        /// <param name="code"></param>
        /// <param name="status"></param>
        /// <param name="letter"></param>
        /// <param name="oType"></param>
        /// <param name="url"></param>
        /// <param name="tel"></param>
        /// <param name="email"></param>
        /// <param name="twoLetterIsoCode"></param>
        /// <returns></returns>
        [DisplayName("新增快递物流公司")]
        [EntityAuthorize(PermissionFlags.Insert)]
        [HttpPost]
        public IActionResult AddExpress(string name, string code, string status, string letter, short oType, string url, string tel, string email, string twoLetterIsoCode)
        {

            if (name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
            }

            var express = new Express
            {
                Name = name,
                Code = code,
                Status = status == "on",
                Letter = letter,
                OType = oType,
                Url = url,
                //Tel = tel,
                //Email = email,
                //TwoLetterIsoCode = twoLetterIsoCode
            };

            express.Insert();

            if (LocalizationSettings.Current.IsEnable)
            {
                var languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                using (var tran1 = InternationalExpressLan.Meta.CreateTrans())
                {
                    foreach (var item in languagelist)
                    {
                        var ex = new InternationalExpressLan();
                        ex.EId = express.Id;
                        ex.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                        if (ex.Name.IsNullOrWhiteSpace())
                        {
                            continue;
                        }
                        ex.LId = item.Id;
                        ex.Insert();
                    }
                    tran1.Commit();
                }
            }


            return MessageTip(GetResource("创建成功"));
        }

        /// <summary>
        /// 修改快递物流公司
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [DisplayName("修改快递物流公司")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult EditExpress(int id)
        {
            var express = Express.FindById(id);
            ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            return View(express);
        }

        /// <summary>
        /// 修改快递物流公司
        /// </summary>
        /// <param name="id"></param>
        /// <param name="name"></param>
        /// <param name="code"></param>
        /// <param name="status"></param>
        /// <param name="letter"></param>
        /// <param name="oType"></param>
        /// <param name="url"></param>
        /// <param name="tel"></param>
        /// <param name="email"></param>
        /// <param name="twoLetterIsoCode"></param>
        /// <returns></returns>
        [DisplayName("修改快递物流公司")]
        [EntityAuthorize(PermissionFlags.Update)]
        [HttpPost]
        public IActionResult EditExpress(int id, string name, string code, string status, string letter, short oType, string url, string tel, string email, string twoLetterIsoCode)
        {
            var express = Express.FindById(id);
            if (express == null)
            {
                return Prompt(new PromptModel { Message = GetResource("快递物流公司不存在") });
            }
            express.Name = name;
            express.Code = code;
            express.Status = status == "on";
            express.Letter = letter;
            express.OType = oType;
            express.Url = url;
            //express.Tel = tel;
            //express.Email = email;
            //express.TwoLetterIsoCode = twoLetterIsoCode;

            express.Update();

            if (LocalizationSettings.Current.IsEnable)
            {
                var languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var a = Newtonsoft.Json.JsonConvert.SerializeObject(languagelist);

                var List = InternationalExpressLan.FindAllByEId(express.Id);
                using (var tran1 = InternationalExpressLan.Meta.CreateTrans())
                {
                    foreach (var item in languagelist)
                    {
                        var ex = List.Find(x => x.LId == item.Id);
                        if (ex != null)
                        {
                            ex.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                            ex.Update();
                        }
                        else
                        {
                            ex = new InternationalExpressLan();
                            ex.EId = express.Id;
                            ex.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                            if (ex.Name.IsNullOrWhiteSpace())
                            {
                                continue;
                            }
                            ex.LId = item.Id;
                            ex.Insert();

                        }
                    }
                    tran1.Commit();
                }
            }
            return MessageTip(GetResource("修改成功"));
        }

        /// <summary>
        /// 删除快递物流公司
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [DisplayName("删除快递物流公司")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult Delete(string ids)
        {
            int[] idArr = ids.SplitAsInt();
            var res = new DResult();

            Express.DelByIds(ids);
            Express.Meta.Cache.Clear("", true);

            res.success = true;
            res.code = 10000;
            res.msg = GetResource("删除成功");
            return Json(res);
        }
    }
}

