﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@model Bonus;
@{
    var localizationSettings = LocalizationSettings.Current;
    var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
}
<div class="page">
    <form method="post" id="bonus_form">
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准")</li>
                    @foreach (var item in LanguageList)
                    {
                        <li data="@item.Id">@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder"> 
                                <td class="required w120">@T("红包名称")</td>
                                <td class="vatop rowform"><input id="bonus_name" name="bonus_name" value="@Model.Name" class="input-txt" type="text"></td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder"> 
                                <td class="required w120">@T("红包祝福语")</td>
                                <td class="vatop rowform"><input id="bonus_blessing" name="bonus_blessing" value="@Model.Blessing" class="input-txt" type="text"></td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td class="required w120">@T("红包备注")</td>
                                <td class="vatop rowform"><textarea name="bonus_remark">@Model.Remark</textarea></td>
                                <td class="vatop tips"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in LanguageList)
                    {
                        var lanBonus = BonusLan.FindByBIdAndLId(Model.Id, item.Id);
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder"> 
                                        <td class="required w120">@T("红包名称")</td>
                                        <td class="vatop rowform"><input id="<EMAIL>" name="[@item.Id].bonus_name" value="@lanBonus?.Name" class="input-txt" type="text"></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder"> 
                                        <td class="required w120">@T("红包祝福语")</td>
                                        <td class="vatop rowform">
                                            <input id="<EMAIL>" name="[@item.Id].bonus_blessing" value="@lanBonus?.Blessing" class="input-txt" type="text">
                                            </td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="required w120">@T("红包备注")</td>
                                        <td class="vatop rowform"><textarea name="[@item.Id].bonus_remark">@lanBonus?.Remark</textarea></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
            </div>
            <table class="ds-default-table">
                <tbody>
                    <tr class="noborder"> 
                        <td class="required w120">@T("红包类型")</td>
                        <td class="vatop rowform">
                        <input value="@(Model.Type == 1 ? T("活动红包") : Model.Type == 2 ? T("注册红包") : Model.Type == 3 ? T("奖品红包") : T("未知状态"))" disabled class="input-txt" type="text">
                        </td>
                        <td class="vatop tips"></td>
                    </tr>
                    <tr class="noborder"> 
                        <td class="required w120">@T("红包总面额")</td>
                        <td class="vatop rowform"><input id="bonus_totalprice" name="bonus_totalprice" value="@Model.TotalPrice" class="input-txt" type="text" disabled ></td>
                        <td class="vatop tips"></td>
                    </tr>
                    <tr class="noborder"> 
                        <td class="required w120">@T("开始时间")</td>
                        <td class="vatop rowform"><input id="bonus_begintime" name="bonus_begintime" value="@(UnixTime.ToDateTime(Model.BeginTime).ToString("yyyy-MM-dd HH:mm:ss"))" class="input-txt" type="text"></td>
                        <td class="vatop tips"></td>
                    </tr>
                    <tr class="noborder"> 
                        <td class="required w120">@T("结束时间")</td>
                    <td class="vatop rowform"><input id="bonus_endtime" name="bonus_endtime" value="@(UnixTime.ToDateTime(Model.EndTime).ToString("yyyy-MM-dd HH:mm:ss"))" class="input-txt" type="text"></td>
                        <td class="vatop tips"></td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr class="tfoot">
                        <td colspan="15"><input type="submit" value='@T("确认提交")' class="btn" lay-submit lay-filter="bonus_submit" /></td>
                </tfoot>
            </table>
    </form>
</div>
<script>
    layui.use(['form', 'laydate'], function () {
        var form = layui.form;
        var laydate = layui.laydate;
        var startIns, endIns;

        // 设置默认日期为今天和明天（格式 yyyy-MM-dd）如果输入框为空
        function _fmtDate(d) { var y = d.getFullYear(); var m = (d.getMonth() + 1).toString().padStart(2, '0'); var dd = d.getDate().toString().padStart(2, '0'); return y + '-' + m + '-' + dd; }
        var _today = new Date();
        var _tomorrow = new Date(_today);
        _tomorrow.setDate(_tomorrow.getDate() + 1);
        var _startEl = document.getElementById('startdate');
        var _endEl = document.getElementById('enddate');
        try {
            if (_startEl && (!_startEl.value || _startEl.value.trim() === '')) _startEl.value = _fmtDate(_today);
            if (_endEl && (!_endEl.value || _endEl.value.trim() === '')) _endEl.value = _fmtDate(_tomorrow);
        } catch (e) { }

        // 日期选择器初始化
        startIns = laydate.render({
            elem: '#bonus_begintime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            value: (_startEl && _startEl.value) ? _startEl.value : undefined,
            trigger: 'click',
            done: function (value) {
                if (value) {
                    try { endIns.config.min = value; } catch (e) { }
                } else {
                    try { endIns.config.min = ''; } catch (e) { }
                }
            }
        });

        endIns = laydate.render({
            elem: '#bonus_endtime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            value: (_endEl && _endEl.value) ? _endEl.value : undefined,
            trigger: 'click',
            done: function (value) {
                if (value) {
                    try { startIns.config.max = value; } catch (e) { }
                } else {
                    try { startIns.config.max = ''; } catch (e) { }
                }
            }
        });

        // 绑定 layui 提交事件
        form.on('submit(bonus_submit)', function (formData) {
            var $form = $('#bonus_form');
            if (!$form.length) return false;

            function isNumber(v) { return !isNaN(parseFloat(v)) && isFinite(v); }

            var $btn = $form.find('input[type=submit]');
            var name = $.trim($('#bonus_name').val() || '');
            if (!name) { layer.msg('@T("请填写红包名称")', { icon: 0 }); return false; }

            var bt = $.trim($('#bonus_begintime').val() || '');
            var et = $.trim($('#bonus_endtime').val() || '');
            if (!bt || !et) { layer.msg('@T("请填写开始时间和结束时间")', { icon: 0 }); return false; }

            var url = $form.attr('action');
            var fd = new FormData($form[0]);
            $btn.prop('disabled', true);

            $.ajax({
                url: url,
                type: 'POST',
                data: fd,
                processData: false,
                contentType: false,
                success: function (res) {
                    var msg = (res && res.msg) ? res.msg : '@T("操作完成")';
                    if (res && res.success) {
                        layer.msg(msg, { icon: 1, time: 1200 }, function () {
                            try {
                                var data = (res && res.data) ? res.data : null;
                                if (parent && typeof parent.receiveNewBonus === 'function') {
                                    try { parent.receiveNewBonus(data); } catch (e) { }
                                }
                                if (parent && parent.layer) {
                                    var idx = parent.layer.getFrameIndex(window.name);
                                    parent.layer.close(idx);
                                    try { parent.location.reload(); } catch (e) { }
                                } else if (window.opener) {
                                    try { window.opener.location.reload(); } catch (e) { }
                                    try { window.close(); } catch (e) { }
                                } else {
                                    try { window.location.reload(); } catch (e) { }
                                }
                            } catch (e) {
                                try { window.close(); } catch (ee) { }
                            }
                        });
                    } else {
                        layer.msg(msg || '@T("操作失败")', { icon: 2 });
                        $btn.prop('disabled', false);
                    }
                },
                error: function () {
                    layer.msg('@T("请求失败，请重试")');
                    $btn.prop('disabled', false);
                }
            });

            return false;
        });
    });
</script>
