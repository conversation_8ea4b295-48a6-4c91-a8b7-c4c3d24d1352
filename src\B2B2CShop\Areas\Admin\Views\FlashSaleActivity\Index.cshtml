﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("秒杀")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("活动列表")</span></a></li>
                <li><a href="@Url.Action("FlashSalePackage")"><span>@T("套餐管理")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("PackageSet")', '@T("设置")')"><span>@T("设置")</span></a></li>
            </ul>
        </div>
    </div>

    <form method="get" name="formSearch" action="@Url.Action("Index")">
        <div class="ds-search-form">
            <dl>
                <dt>@T("活动名称")</dt>
                <dd><input type="text" value="@Model.name" name="name" id="name" class="txt" style="width:160px;"></dd>
            </dl>
            <dl>
                <dt>@T("店铺名称")</dt>
                <dd>
                    <select name="storeId" id="storeId" class="select" style="min-width:180px;">
                    <!option value="">@T("全部")</!option>
                        @foreach (var store in Model.storelist)
                        {
                            <!option value="@store.Id" @(Model.storeId == store.Id?"selected":"")>@store.Name</!option>
                        }
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("状态")</dt>
                <dd>
                    <select name="status" id="status">
                            <!option value="0" @(Model.status == 0?"selected":"")>@T("全部")</!option>
                            <!option value="1" @(Model.status == 1?"selected":"")>@T("正常")</!option>
                            <!option value="2" @(Model.status == 2?"selected":"")>@T("已结束")</!option>
                            <!option value="3" @(Model.status == 3?"selected":"")>@T("管理员关闭")</!option>
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <button type="submit" class="btn" title="@T("查询")">@T("查询")</button>
            </div>
        </div>
    </form>
    <!-- 帮助 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="@T("提示相关设置操作时应注意的要点")">@T("操作提示")</h4>
            <span id="explanationZoom" title="@T("收起提示")" class="arrow"></span>
        </div>
        <ul>
            <li>@T("卖家发布的秒杀活动列表")</li>
            <li>@T("取消操作后的活动不可恢复，请慎重操作")</li>
            <li>@T("点击详细按钮，查看活动详细信息")</li>
        </ul>
    </div>

    <!-- 列表 -->
    <table class="ds-default-table">
        <thead>
            <tr class="thead">
                <th class="w24"></th>
                <th class="align-left"><span>@T("活动名称")</span></th>
                <th class="align-left" width="200"><span>@T("店铺名称")</span></th>
                <th class="align-center" width="160"><span>@T("开始时间")</span></th>
                <th class="align-center" width="160"><span>@T("结束时间")</span></th>
                <th class="align-center" width="100"><span>@T("购买下限")</span></th>
                <th class="align-center" width="120"><span>@T("状态")</span></th>
                <th class="align-center" width="200"><span>@T("操作")</span></th>
            </tr>
        </thead>
        <tbody>
            @foreach (FlashSaleActivity item in Model.list)
            {
                <tr class="hover">
                    <td><input type="checkbox" class="checkitem" name="ids" value="@item.Id" /></td>
                    <td class="align-left"><span>@item.Name</span></td>
                    <td class="align-left"><span>@item.StoreName</span></td>
                    <td class="align-center"><span>@(UnixTime.ToDateTime(item.StartTime))</span></td>
                    <td class="align-center"><span>@(UnixTime.ToDateTime(item.EndTime))</span></td>
                    <td class="align-center"><span>@(item.LowerLimit == 0 ? T("不限") : item.LowerLimit.SafeString())</span></td>
                    <td class="align-center">
                        <span>
                        @if (DateTime.Now>UnixTime.ToDateTime(item.EndTime))
                            {
                                @T("已结束")
                            }
                            else
                            {
                                  @(item.State switch{
                                    0=> T("管理员关闭"),
                                    1=> T("正常"),
                                    _ => T("未知")
                                    })
                            }
                        </span>
                    </td>
                    <td>
                        <a dstype="btn_review_fail" data-rushbuy-id="@item.Id" href="javascript:;" class="dsui-btn-view"><i class="iconfont"></i>@T("详细")</a>
                        <a href="javascript:dsLayerConfirm('/index.php/admin/Groupbuy/groupbuy_del.html?groupbuy_id=7','@T("确认删除该抢购活动?")',7)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
        </tbody>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>
<script>
$(function() {
    $('#storeId').select2({
        placeholder: '@T("请选择店铺")',
        allowClear: true,
    });
});
</script>
