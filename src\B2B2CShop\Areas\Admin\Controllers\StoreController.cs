﻿using B2B2CShop.Entity;

using DH.Entity;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Exceptions;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Timing;
using System.ComponentModel;
using System.Dynamic;
using System.Xml.Linq;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>店铺管理</summary>
[DisplayName("店铺管理")]
[Description("用于店铺管理")]
[AdminArea]
[DHMenu(100, ParentMenuName = "Stores", ParentMenuDisplayName = "店铺", ParentMenuUrl = "~/{area}/Store", ParentMenuOrder = 80, CurrentMenuUrl = "~/{area}/Store", CurrentMenuName = "StoreList", CurrentIcon = "&#xe6ec;", LastUpdate = "20241202")]
public class StoreController : PekCubeAdminControllerX
{

    /// <summary>
    /// 店铺列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("店铺列表")]
    public IActionResult Index(string shopkeeper, string shopNames, int belongingLevel = -1, int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };

        shopkeeper = shopkeeper.SafeString().Trim();
        shopNames = shopNames.SafeString().Trim();

        var storeGradeList = StoreGrade.FindAllWithCache().OrderBy(e => e.Sort);
        ViewBag.StoreGradeList = storeGradeList.Select(item => new SelectListItem { Value = item.Id.ToString(), Text = item.Name });

        viewModel.list = Store.Search(belongingLevel, shopkeeper, shopNames, pages);
        viewModel.page = page;
        viewModel.belongingLevel = belongingLevel.ToString();
        viewModel.shopkeeper = shopkeeper;
        viewModel.shopNames = shopNames;

        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> { { "belongingLevel", belongingLevel.ToString() }, { "shopkeeper", shopkeeper }, { "shopNames", shopNames } });
        return View(viewModel);
    }

    /// <summary>
    /// 新增外驻店铺
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增外驻店铺")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddStore()
    {
        return View();
    }

    /// <summary>
    /// 新增外驻店铺
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增外驻店铺")]
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    public IActionResult AddStore(string ShopName, string MemberName, string Password)
    {
        try
        {
            using (var tran1 = UserE.Meta.CreateTrans())
            {
                var member = new UserE();
                member.Name = MemberName;
                member.Password = ManageProvider.Provider?.PasswordProvider.Hash(Password.Length == 32 ? Password : Password.MD5());
                member.RegisterTime = DateTime.Now;
                member.Enable = true;
                member.Logins = 0;
                member.RegisterIP = Pek.Helpers.DHWeb.IP;
                member.RoleID = Role.GetOrAdd(DHSetting.Current.DefaultRole).ID;
                member.DisplayName = ShopName + MemberName;
                member.Insert();

                UserLog.AddLog($"添加用户【{MemberName}】:{member.ID}", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");

                var modelDetail = new UserDetail();
                modelDetail.Id = member.ID;
                modelDetail.Insert();

                var modelUserEx = new UserEx
                {
                    Id = member.ID,
                };
                modelUserEx.Insert();

                var store = new Store();
                store.Name = ShopName;
                store.GradeId = 1;
                store.UId = member.ID;
                store.UName = MemberName;
                store.SellerName = MemberName;
                store.State = 1;
                store.Theme = "default";
                store.Credit = 100;
                store.DescCredit = 5;
                store.ServiceCredit = 5;
                store.DeliveryCredit = 5;
                store.AddTime = UnixTime.ToTimestamp(DateTime.Now);
                char pycode = NewLife.Common.PinYin.GetFirst(ShopName).Substring(0, 1).ToUpper().ToDGChar();
                if (pycode>='A'&& pycode<='Z')
                {
                    store.PyAbbr = pycode.ToString();
                }
                else
                {
                    store.PyAbbr = "#";
                }
                store.Insert();
                UserLog.AddLog($"添加外驻店铺【{ShopName}】:{store.Id}", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");

                var seller = new Seller();
                seller.Name = MemberName;
                seller.UId = member.ID;
                seller.StoreId = store.Id;
                seller.IsAdmin = true;
                seller.Insert();
                UserLog.AddLog($"添加卖家【{MemberName}】:{seller.Id}", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");

                var albumCategory = new AlbumCategory();
                albumCategory.Name = "默认相册";
                albumCategory.StoreId = store.Id;
                albumCategory.Content = "";
                albumCategory.Sort = 255;
                albumCategory.Cover = "";
                albumCategory.IsDefault = true;
                albumCategory.Insert();
                UserLog.AddLog($"添加店铺相册【默认相册】:{store.Id}", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");

                var storeJoinIn = new StoreJoinIn();
                storeJoinIn.UId = member.ID;
                storeJoinIn.UName = MemberName;
                storeJoinIn.SellerName = MemberName;
                storeJoinIn.Name = ShopName;
                storeJoinIn.State = 40;
                storeJoinIn.Year = 1;
                storeJoinIn.Insert();
                UserLog.AddLog($"添加店铺入驻【{ShopName}】:{storeJoinIn.Id}", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");
                tran1.Commit();
            }
        }
        catch (DHException ex)
        {
            return Prompt(new PromptModel { Message = ex.Message });
        }

        return MessageTip(GetResource("创建成功"));
    }

    /// <summary>
    /// 店铺数据删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("店铺数据删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        Store.DelByIds(Ids);

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 修改列表字段值
    /// </summary>
    /// <param name="value">修改名称</param>
    /// <param name="Id">分类编号</param>
    /// <param name="column">字段名</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("修改列表字段值")]
    public IActionResult ChangeName(String value, Int32 Id, String column)
    {
        if (value.IsNullOrWhiteSpace()) return Json(false);

        var Models = Store.FindById(Id);

        if (Models == null) return Json(false);

        if (column == "gc_name")
        {
            var Model = Store.FindByName(value);
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }

            Models.Name = value;
        }
        else if (column == "gc_sort")
        {
            Models.Sort = value.ToDGShort();
        }
        else
        {
            return Json(false);
        }

        Models.Update();

        return Json(true);
    }

    /// <summary>
    /// 验证店铺名
    /// </summary>
    /// <param name="ShopName"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("验证店铺名")]
    public IActionResult VerifyStoreName(string ShopName, int Id)
    {
        return Json(Store.FindByStoreName(Id, ShopName.SafeString().Trim()));
    }

    /// <summary>
    /// 验证用户名
    /// </summary>
    /// <param name="MemberName"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("验证用户名")]
    public IActionResult VerifyName(string MemberName, int Id)
    {
        return Json(UserE.FindByUserName(Id, MemberName.SafeString().Trim()));
    }

    //资金调节
    [DisplayName("资金调节")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult CapitalAdjustment(int Id)
    {
        var store = Store.FindById(Id);
        if (store == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在") });
        }
        dynamic viewModel = new ExpandoObject();
        viewModel.store = store;
        return View(viewModel);
    }

    //资金调节
    [DisplayName("资金调节")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult CapitalAdjustment(int id, string name, int type, decimal amount, string remark)
    {
        //1增加2减少3冻结4解冻
        var store = Store.FindById(id);
        if (store == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在") });
        }

        switch (type)
        {
            case 1: // 增加资金
                store.AvaliableMoney += amount;
                UserLog.AddLog($"增加店铺资金【{store.SellerName}】:{amount}元", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");
                break;
            case 2: // 减少资金
                store.AvaliableMoney -= amount;
                UserLog.AddLog($"减少店铺资金【{store.SellerName}】:{amount}元", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");
                break;
            case 3: // 冻结资金
                store.FreezeMoney += amount;
                store.AvaliableMoney -= amount;
                UserLog.AddLog($"冻结店铺资金【{store.SellerName}】:{amount}元", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");
                break;
            case 4: // 解冻资金
                store.FreezeMoney -= amount;
                store.AvaliableMoney += amount;
                UserLog.AddLog($"解冻店铺资金【{store.SellerName}】:{amount}元", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");
                break;
            default:
                return Prompt(new PromptModel { Message = GetResource("无效的操作类型") });
        }

        store.Update();

        return MessageTip(GetResource("操作成功"));

    }

    [DisplayName("编辑店铺")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditStore(long id)
    {
        var store = Store.FindById(id);
        if (store == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在") });
        }
        return View(store);
    }
    
    [DisplayName("编辑店铺")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult EditStore(long store_id, string store_name, int grade_id, DateTime end_time, short store_state,string store_close_info)
    {
        var store = Store.FindById(store_id);
        if (store == null) return Prompt(new PromptModel { Message = GetResource("数据不存在") });
        if (store_name.IsNullOrEmpty()) return Prompt(new PromptModel { Message = GetResource("店铺名称不能为空") });
        store.Name = store_name;
        char pycode = NewLife.Common.PinYin.GetFirst(store_name).Substring(0, 1).ToUpper().ToDGChar();
        if (pycode >= 'A' && pycode <= 'Z')
        {
            store.PyAbbr = pycode.ToString();
        }
        else
        {
            store.PyAbbr = "#";
        }
        store.GradeId = grade_id;
        if (UnixTime.ToTimestamp(end_time) > 0)
        {
            store.EndTime = UnixTime.ToTimestamp(end_time);
        }
        store.State = store_state;
        //if (store.State==0)
        //{
        //    if (store_close_info.IsNullOrEmpty()) return Prompt(new PromptModel { Message = GetResource("店铺关闭原因不能为空") });

        //    store.CloseInfo = store_close_info;
        //}
        //else
        //{
        //    store.CloseInfo = "";
        //}
        store.Update();
        Store.Meta.Cache.Clear("", true);
        return Prompt(new PromptModel { Message = GetResource("修改成功"),IsOk = true,BackUrl=Url.Action("Index") });
    }
    [DisplayName("编辑店铺入住信息")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult EditStoreJoinIn(long store_id, string company_name,string CountryId,int ProvinceId,string contacts_name,string contacts_phone,string contacts_email,string business_licence_number,string business_licence_address,DateTime business_licence_start,DateTime business_licence_end,string business_sphere)
    {
        var store = Store.FindById(store_id);
        if (store == null) return Prompt(new PromptModel { Message = GetResource("数据不存在") });
        var storejoinin = StoreJoinIn.FindByUId(store.UId);
        if (storejoinin == null) return Prompt(new PromptModel { Message = GetResource("数据不存在") });

        storejoinin.CompanyName = company_name;
        storejoinin.CountryId = Country.FindByTwoLetterIsoCode(CountryId)?.Id??0;
        storejoinin.CountryCode = CountryId;
        storejoinin.ProvinceId = ProvinceId;
        storejoinin.ProvinceCode = Regions.FindById(ProvinceId)?.AreaCode;
        storejoinin.ContactsName = contacts_name;
        storejoinin.ContactsPhone = contacts_phone;
        storejoinin.ContactsEmail = contacts_email;
        storejoinin.BusinessLicenceNumber = business_licence_number;
        storejoinin.BusinessLicenceAddress = business_licence_address;
        storejoinin.BusinessLicenceStart = business_licence_start;
        storejoinin.BusinessLicenceEnd = business_licence_end;
        storejoinin.BusinessSphere = business_sphere;
        storejoinin.Update();
        StoreJoinIn.Meta.Cache.Clear("", true);
        store.CompanyName = company_name;
        store.Phone = contacts_phone;
        store.Update();
        Store.Meta.Cache.Clear("", true);
        return Prompt(new PromptModel { Message = GetResource("修改成功"),IsOk = true,BackUrl=Url.Action("Index") });
    }

    [DisplayName("店铺入驻申请列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult StoreApply(string shopkeeper, string shopNames, int belongingLevel = -1, int state = -1, int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };

        shopkeeper = shopkeeper.SafeString().Trim();
        shopNames = shopNames.SafeString().Trim();

        var storeGradeList = StoreGrade.FindAllWithCache().OrderBy(e => e.Sort);
        ViewBag.StoreGradeList = storeGradeList.Select(item => new SelectListItem { Value = item.Id.ToString(), Text = item.Name });

        viewModel.list = StoreJoinIn.Search(belongingLevel,state, shopkeeper, shopNames, pages);
        viewModel.page = page;
        viewModel.belongingLevel = belongingLevel.ToString();
        viewModel.state = state.ToString();
        viewModel.shopkeeper = shopkeeper;
        viewModel.shopNames = shopNames;

        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("StoreApply"), new Dictionary<string, string> { { "belongingLevel", belongingLevel.ToString() }, { "shopkeeper", shopkeeper }, { "shopNames", shopNames } });
        return View(viewModel);
    }

    [DisplayName("店铺入驻申请审核")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult AuditStoreJoinIn(long id)
    {
        if(id <= 0)return Content(GetResource("店铺入驻ID为空"));
        var storeJoinIn = StoreJoinIn.FindById(id);
        if (storeJoinIn == null) return Content(GetResource("店铺入驻记录不存在"));
        return View(storeJoinIn);
    }


    [DisplayName("店铺入驻申请审核")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult AuditStoreJoinIn(long id,decimal paying_amount, string verify_type,string joinin_message, double[] commis_rate)
    {
        if (id <= 0) return Json(new DResult() { success = false, msg = GetResource("店铺入驻ID为空") });
        var storeJoinIn = StoreJoinIn.FindById(id);
        if (storeJoinIn == null) return Json(new DResult() { success = false, msg = GetResource("店铺入驻记录不存在") });
        if (verify_type.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = GetRequest("请选择审核意见") });
        if (verify_type != "pass" && joinin_message.IsNullOrWhiteSpace()) return Json(new DResult() { success = false, msg = GetRequest("请选择输入审核意见") });

        if (verify_type == "fail")//不通过
        {
            storeJoinIn.PayingAmount = paying_amount;
            storeJoinIn.State = (short)30;
            storeJoinIn.Message = joinin_message;
            storeJoinIn.Update();
        }
        else
        {
            using (var tran = StoreJoinIn.Meta.CreateTrans())
            {
                try
                {

                    storeJoinIn.PayingAmount = paying_amount;
                    storeJoinIn.State = (short)20;
                    storeJoinIn.Message = joinin_message;

                    var classes = storeJoinIn.BusinessClasses;
                    for (int i = 0; i < commis_rate.Length; i++)
                    {
                        classes[i].CommisRate = commis_rate[i];
                    }
                    storeJoinIn.BusinessCategoryInfo = classes.ToJson();
                    storeJoinIn.Update();
                    #region 创建店铺数据表
                    char pycode = NewLife.Common.PinYin.GetFirst(storeJoinIn.Name).Substring(0, 1).ToUpper().ToDGChar();
                    if (pycode <= 'A' && pycode >= 'Z')
                    {
                        pycode = '#';
                    }
                    var modelStore = new Store
                    {
                        Name = storeJoinIn.Name,
                        GradeId = storeJoinIn.StoreGradeId.ToInt(),
                        UId = storeJoinIn.UId,
                        UName = storeJoinIn.UName,
                        SellerName = storeJoinIn.SellerName,
                        StoreClassId = storeJoinIn.StoreClassId,
                        CountryId = storeJoinIn.CountryId,
                        CountryCode = storeJoinIn.CountryCode,
                        RegionId = storeJoinIn.ProvinceId,
                        RegionCode = storeJoinIn.ProvinceCode,
                        Address = storeJoinIn.Address,
                        PyAbbr = pycode.ToString(),
                        State = 1,
                        Sort = 255,
                        AddTime = UnixTime.ToTimestamp(DateTime.Now),
                        Recommend = false,
                        Theme = "default",
                        Credit = 100,
                        DescCredit = 5,
                        ServiceCredit = 5,
                        DeliveryCredit = 5,
                        Collect = 0,
                        Sales = 0,
                        FreePrice = 0.00M,
                        DecorationSwitch = false,
                        DecorationOnly = false,
                        DecorationImageCount = 0,
                        PlatformStore = false,
                        BindAllGc = false,
                        BaoZh = false,
                        QTian = false,
                        ZhPing = false,
                        ErXiaoShi = false,
                        TuiHuo = false,
                        ShiYong = false,
                        ShiTi = false,
                        XiaoXie = false,
                        HuoDaoFK = false,
                        Longitude = String.Empty,
                        Latitude = String.Empty,
                        MgDiscountState = false,
                        AvaliableDeposit = paying_amount,
                        FreezeDeposit = 0.00M,
                        PayableDeposit = paying_amount,
                        AvaliableMoney = 0.00M,
                        FreezeMoney = 0.00M,
                        KdnIfOpen = false,
                        TrackIfOpen = false,
                    };
                    modelStore.Insert();
                    UserLog.AddLog($"添加店铺【{storeJoinIn.Name}】:{modelStore.Id}", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");

                    #endregion

                    #region 新建店铺相册
                    var modelSeller = new Seller
                    {
                        Name = storeJoinIn.SellerName,
                        UId = storeJoinIn.UId,
                        SellerGroupId = 0,
                        StoreId = modelStore.Id,
                        IsAdmin = false,
                    };
                    modelSeller.Insert();
                    UserLog.AddLog($"添加卖家【{storeJoinIn.SellerName}】:{modelSeller.Id}", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");


                    var modelAlbumCategory = new AlbumCategory
                    {
                        Name = "默认相册",
                        StoreId = modelStore.Id,
                        Sort = 255,
                        IsDefault = true
                    };
                    modelAlbumCategory.Insert();
                    UserLog.AddLog($"添加店铺相册【默认相册】:{modelStore.Id}", $"{ControllerContext.ActionDescriptor.ControllerName}&{ControllerContext.ActionDescriptor.ActionName}");

                    var modelSnsAlbumClass = new SnsAlbumClass
                    {
                        UId = storeJoinIn.UId,
                        Name = $"{storeJoinIn.SellerName} 买家秀",
                        Des = "买家秀默认相册",
                        Sort = 255,
                        IsDefault = true,
                    };
                    modelSnsAlbumClass.Insert();
                    #endregion
                }
                catch (Exception ex)
                {
                    return Json(new DResult() { success = false, msg = GetRequest("异常：") + ex.Message });
                }

                tran.Commit();
            }
        }
        return Json(new DResult() { success = true, msg = GetResource("审核完成") });
    }


    [DisplayName("店铺入驻申请删除")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult DeleteStoreJoinIn(long Id)
    {
        if (Id <= 0) return Json(new DResult() { success = false, msg = GetResource("店铺入驻ID为空") });
        var storeJoinIn = StoreJoinIn.FindById(Id);
        if (storeJoinIn == null) return Json(new DResult() { success = false, msg = GetResource("店铺入驻记录不存在") });
        if (storeJoinIn.State == 20 || storeJoinIn.State == 40) return Json(new DResult() { success = false, msg = GetResource("店铺已开通无法删除") });
        storeJoinIn.Delete();
        StoreJoinIn.Meta.Cache.Clear("",true);
        return Json(new DResult() { success = true, msg = GetResource("删除成功") });
    }


    [DisplayName("店铺入驻信息")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult StoreJoinInDetail(long Id)
    {
        if (Id <= 0) return Json(new DResult() { success = false, msg = GetResource("店铺入驻ID为空") });
        var storeJoinIn = StoreJoinIn.FindById(Id);
        if (storeJoinIn == null) return Json(new DResult() { success = false, msg = GetResource("店铺入驻记录不存在") });
        return View(storeJoinIn); 
    }
}