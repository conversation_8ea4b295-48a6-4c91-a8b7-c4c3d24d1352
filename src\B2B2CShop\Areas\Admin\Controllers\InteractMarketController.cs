﻿using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{

    [DisplayName("平台互动活动")]
    [Description("平台互动活动管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/InteractMarket", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/InteractMarket", CurrentMenuName = "InteractMarketList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class InteractMarketController : PekCubeAdminControllerX
    {
        // 统一入口（可作为概览页，如需可创建对应视图 ~/Areas/Admin/Views/InteractMarket/Index.cshtml）
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("互动活动列表")]
        public IActionResult Index()
        {
            return View(); // 建议创建概览页视图
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("刮刮卡-活动列表")]
        public IActionResult ScratchCardIndex(string name,int page = 1,int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.name = name;
            viewModel.list = MarketActivityManage.Search(name,1, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("ScratchCardIndex"), new Dictionary<string, string> {
            { "name", name }
            } );
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("幸运大抽奖-活动列表")]
        public IActionResult LotteryDrawIndex(string name, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.name = name;
            viewModel.list = MarketActivityManage.Search(name, 2, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("LotteryDrawIndex"), new Dictionary<string, string> {
            { "name", name }
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("砸金蛋-活动列表")]
        public IActionResult GoldenEggIndex(string name, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.name = name;
            viewModel.list = MarketActivityManage.Search(name, 3, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("GoldenEggIndex"), new Dictionary<string, string> {
            { "name", name }
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("翻翻看-活动列表")]
        public IActionResult FlipCardIndex(string name, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.name = name;
            viewModel.list = MarketActivityManage.Search(name, 4, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("FlipCardIndex"), new Dictionary<string, string> {
            { "name", name }
            });
            return View(viewModel);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type">活动类型</param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增")]
        public IActionResult Add(int type)
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.type = type;
            viewModel.bonusList = Bonus.FindAllRewardBonus();
            viewModel.voucherlist = VoucherTemplate.FindAllValid();
            return View(viewModel);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type">活动类型</param>
        /// <param name="name">活动名称</param>
        /// <param name="detail">活动详情</param>
        /// <param name="failed">未中奖说明</param>
        /// <param name="begintime">活动开始时间</param>
        /// <param name="endtime">活动结束时间</param>
        /// <param name="jointype">参与类型</param>
        /// <param name="joincount">参与次数限制</param>
        /// <param name="pointtype">总中奖次数</param>
        /// <param name="point">消耗积分/param>
        /// <param name="awardtype">奖品类型</param>
        /// <param name="rewardpoint">奖品积分</param>
        /// <param name="bonusid">红包ID</param>
        /// <param name="vouchertemplateid">优惠券模板ID</param>
        /// <param name="rewardcount">奖品数量</param>
        /// <param name="probability">中奖概率</param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增")]
        [HttpPost]
        public IActionResult Add(int type, string name, string detail, string failed, DateTime begintime, DateTime endtime, int jointype, int joincount, int pointtype, int point, List<Awards> Awards)
        {
            try
            {
                // 基础校验
                if (type is < 1 or > 4)
                    return Json(new DResult { success = false, msg = GetResource("活动类型无效") });

                if (string.IsNullOrWhiteSpace(name))
                    return Json(new DResult { success = false, msg = GetResource("活动名称不能为空") });

                if (begintime >= endtime)
                    return Json(new DResult { success = false, msg = GetResource("开始时间必须小于结束时间") });

                if (jointype != 2 && joincount <= 0)
                    return Json(new DResult { success = false, msg = GetResource("请输入有效的参与次数限制") });

                // 奖项参数校验（使用 List<Awards>）
                if (Awards == null || Awards.Count == 0)
                    return Json(new DResult { success = false, msg = GetResource("请至少配置一个奖项") });

                var n = Awards.Count;
                var probSum = 0;

                for (var i = 0; i < n; i++)
                {
                    var aw = Awards[i];
                    var idx = i + 1;

                    // 数量：允许为0表示不参与抽取，禁止负数
                    if (aw.Count < 0)
                        return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项数量必须大于0") });

                    // 概率：0~100
                    if (aw.Probability < 0 || aw.Probability > 100)
                        return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项概率应在0~100之间") });

                    // 类型：1=积分 2=红包 3=优惠券
                    switch (aw.Type)
                    {
                        case 1:
                            if (aw.Point < 0)
                                return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项积分必须大于0") });
                            break;
                        case 2:
                            if (aw.BonusId < 0)
                                return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项未选择红包") });
                            break;
                        case 3:
                            if (aw.VoucherTemplateId < 0)
                                return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项未选择优惠券模板") });
                            break;
                        default:
                            return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项类型无效") });
                    }

                    probSum += (int)aw.Probability;
                }

                if (probSum > 100)
                    return Json(new DResult { success = false, msg = GetResource("所有奖项概率之和不能超过100%") });

                // 保存活动
                var activity = new MarketActivityManage
                {
                    Name = name,
                    Detail = detail,
                    Failed = failed,
                    Type = type,
                    BeginTime = Pek.Timing.UnixTime.ToTimestamp(begintime),
                    EndTime = Pek.Timing.UnixTime.ToTimestamp(endtime),
                    JoinType = jointype,
                    JoinCount = joincount,
                    TotalWin = pointtype, // 总中奖次数
                    Point = point,        // 每次消耗积分
                    TotalCount = 0,
                    AddTime = Pek.Timing.UnixTime.ToTimestamp(DateTime.Now)
                };

                activity.Insert();

                // 添加翻译表
                if (LocalizationSettings.Current.IsEnable)
                {
                    var languageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                    foreach (var lang in languageList)
                    {
                        var lanname = (GetRequest($"[{lang.Id}].name")).SafeString().Trim();
                        var landetail = (GetRequest($"[{lang.Id}].detail")).SafeString().Trim();
                        var lanfailed = (GetRequest($"[{lang.Id}].failed")).SafeString().Trim();

                        var lan = new MarketActivityManageLan
                        {
                            MId = activity.Id,
                            LId = lang.Id,
                            Name = lanname,
                            Detail = landetail,
                            Failed = lanfailed
                        };
                        lan.Insert();
                    }
                }

                // 保存奖项（按序号作为等级）
                for (var i = 0; i < n; i++)
                {
                    var aw = Awards[i];
                    var award = new MarketActivityAward
                    {
                        ActivityId = activity.Id,
                        Level = i + 1,
                        AwardType = aw.Type,
                        Count = (int)Math.Min(int.MaxValue, Math.Max(0, aw.Count)),
                        Probability = (int)aw.Probability,
                        Point = aw.Type == 1 ? aw.Point : 0,
                        BonusId = aw.Type == 2 ? aw.BonusId : 0,
                        VoucherTemplateId = aw.Type == 3 ? aw.VoucherTemplateId : 0
                    };
                    award.Insert();
                }

                return Json(new DResult { success = true, msg = GetResource("新增活动成功") });
            }
            catch (Exception ex)
            {
                return Json(new DResult { success = false, msg = GetResource("新增活动失败") + "：" + ex.Message });
            }
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑")]
        public IActionResult Edit(long id)
        {
            var entity = MarketActivityManage.FindById(id);
            if (entity == null) return Json(new DResult { success = false, msg = GetResource("找不到活动记录") });
            ViewBag.bonusList = Bonus.FindAllRewardBonus();
            ViewBag.voucherlist = VoucherTemplate.FindAllValid();
            return View(entity);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑")]
        [HttpPost]
        public IActionResult Edit(long id, string name, string detail, string failed, DateTime begintime, DateTime endtime, int jointype, int joincount, int pointtype, int point, List<Awards> awards)
        {
            try
            {
                var entity = MarketActivityManage.FindById(id);
                if (entity == null) return Json(new DResult { success = false, msg = GetResource("找不到活动记录") });

                if (string.IsNullOrWhiteSpace(name))
                    return Json(new DResult { success = false, msg = GetResource("活动名称不能为空") });

                if (begintime >= endtime)
                    return Json(new DResult { success = false, msg = GetResource("开始时间必须小于结束时间") });

                if (jointype != 2 && joincount <= 0)
                    return Json(new DResult { success = false, msg = GetResource("请输入有效的参与次数限制") });

                // 奖项参数校验（使用 List<Awards>）
                if (awards == null || awards.Count == 0)
                    return Json(new DResult { success = false, msg = GetResource("请至少配置一个奖项") });

                var n = awards.Count;
                var probSum = 0;

                for (var i = 0; i < n; i++)
                {
                    var aw = awards[i];
                    var idx = i + 1;

                    if (aw.Count < 0) // 0 表示不参与抽取，禁止负数
                        return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项数量必须大于0") });

                    if (aw.Probability < 0 || aw.Probability > 100)
                        return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项概率应在0~100之间") });

                    switch (aw.Type)
                    {
                        case 1: // 积分
                            if (aw.Point < 0)
                                return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项积分必须大于0") });
                            break;
                        case 2: // 红包
                            if (aw.BonusId < 0)
                                return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项未选择红包") });
                            break;
                        case 3: // 优惠券
                            if (aw.VoucherTemplateId < 0)
                                return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项未选择优惠券模板") });
                            break;
                        default:
                            return Json(new DResult { success = false, msg = GetResource("第") + idx + GetResource("个奖项类型无效") });
                    }

                    probSum += (int)aw.Probability;
                }

                if (probSum > 100)
                    return Json(new DResult { success = false, msg = GetResource("所有奖项概率之和不能超过100%") });

                // 更新活动主表（不修改 Type）
                entity.Name = name;
                entity.Detail = detail;
                entity.Failed = failed;
                entity.BeginTime = Pek.Timing.UnixTime.ToTimestamp(begintime);
                entity.EndTime = Pek.Timing.UnixTime.ToTimestamp(endtime);
                entity.JoinType = jointype;
                entity.JoinCount = joincount;
                entity.TotalWin = pointtype;
                entity.Point = point;
                entity.Update();

                // 更新翻译表
                if (LocalizationSettings.Current.IsEnable)
                {
                    var languageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                    foreach (var lang in languageList)
                    {
                        var lanname = (GetRequest($"[{lang.Id}].name")).SafeString().Trim();
                        var landetail = (GetRequest($"[{lang.Id}].detail")).SafeString().Trim();
                        var lanfailed = (GetRequest($"[{lang.Id}].failed")).SafeString().Trim();

                        var lan = MarketActivityManageLan.FindByMIdAndLId(entity.Id, lang.Id) ?? new MarketActivityManageLan
                        {
                            MId = entity.Id,
                            LId = lang.Id
                        };
                        lan.Name = lanname;
                        lan.Detail = landetail;
                        lan.Failed = lanfailed;

                        if (lan.Id > 0) lan.Update();
                        else lan.Insert();
                    }
                }

                for (var i = 0; i < n; i++)
                {
                    var aw = awards[i];
                    var award = MarketActivityAward.FindByActivityIdAndLevel(entity.Id, i + 1)??new MarketActivityAward();
                    award.ActivityId = entity.Id;
                    award.Level = i + 1;
                    award.AwardType = aw.Type;
                    award.Count = (int)Math.Min(int.MaxValue, Math.Max(0, aw.Count));
                    award.Probability = (int)aw.Probability;
                    award.Point = aw.Type == 1 ? aw.Point : 0;
                    award.BonusId = aw.Type == 2 ? aw.BonusId : 0;
                    award.VoucherTemplateId = aw.Type == 3 ? aw.VoucherTemplateId : 0;
                    if (award.Id > 0) award.Update();
                    else award.Insert();
                }

                return Json(new DResult { success = true, msg = GetResource("编辑成功") });
            }
            catch (Exception ex)
            {
                return Json(new DResult { success = false, msg = GetResource("编辑失败") + "：" + ex.Message });
            }
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("详情")]
        public IActionResult Detail(long id,int page = 1,int limit = 10)
        {
            var entity = MarketActivityManage.FindById(id);
            if (entity == null) return Json(new DResult { success = false, msg = GetResource("找不到活动记录") });
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            dynamic viewModel = new ExpandoObject();
            viewModel.entity = entity;
            viewModel.list = MarketActivityLog.Search(entity.Id, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Detail"), new Dictionary<string, string> {
            { "id", id.SafeString() }
            });
            return View(viewModel);

        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("链接地址")]
        public IActionResult Link(int type, long id)
        {
            var entity = MarketActivityManage.FindById(id);
            if (entity == null) return Json(new DResult { success = false, msg = GetResource("找不到活动记录") });
            return View(entity);
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除活动")]
        public IActionResult Delete(long id)
        {
            MarketActivityManage.Delete(MarketActivityManage._.Id == id);
            MarketActivityManage.Meta.Cache.Clear("", true);
            MarketActivityManageLan.DeleteByWId(id);
            MarketActivityAward.DeleteByActivityId(id);
            MarketActivityLog.DeleteByActivityId(id);
            return Json(new DResult { success = true, msg = GetResource("删除成功") });
        }
    }
    // 用于接收前端传递的奖项参数
    public class Awards
    {
        public int Type { get; set; }
        public int Point { get; set; }
        public long BonusId { get; set; }
        public long VoucherTemplateId { get; set; }
        public long Count { get; set; }
        public long Probability { get; set; }
    }
}
