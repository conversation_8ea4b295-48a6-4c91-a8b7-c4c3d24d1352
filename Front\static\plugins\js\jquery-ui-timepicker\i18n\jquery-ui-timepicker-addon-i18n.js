/*! jQuery Timepicker Addon - v1.6.3 - 2016-04-20
* http://trentrichardson.com/examples/timepicker
* Copyright (c) 2016 <PERSON>; Licensed MIT */

(function($){

// source: src/i18n/jquery-ui-timepicker-af.js
/* Afrikaans translation for the jQuery Timepicker Addon */
/* Written by <PERSON><PERSON> */

	$.timepicker.regional['af'] = {
		timeOnlyTitle: 'Kies Tyd',
		timeText: 'Tyd ',
		hourText: 'Ure ',
		minuteText: 'Minute',
		secondText: 'Sekondes',
		millisecText: 'Millisekondes',
		microsecText: 'Mikrosekondes',
		timezoneText: 'Tydsone',
		currentText: 'Huidige Tyd',
		closeText: 'Klaar',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-am.js
/* Armenian translation for the jQuery Timepicker Addon */
/* Written by <PERSON><PERSON><PERSON><PERSON><EMAIL> */

	$.timepicker.regional['am'] = {
		timeOnlyTitle: 'Ընտրեք ժամանակը',
		timeText: 'Ժամանակը',
		hourText: 'Ժամ',
		minuteText: 'Րոպե',
		secondText: 'Վարկյան',
		millisecText: 'Միլիվարկյան',
		microsecText: 'Միկրովարկյան',
		timezoneText: 'Ժամային գոտին',
		currentText: 'Այժմ',
		closeText: 'Փակել',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-bg.js
/* Bulgarian translation for the jQuery Timepicker Addon */
/* Written by Plamen Kovandjiev */

	$.timepicker.regional['bg'] = {
		timeOnlyTitle: 'Изберете време',
		timeText: 'Време',
		hourText: 'Час',
		minuteText: 'Минути',
		secondText: 'Секунди',
		millisecText: 'Милисекунди',
		microsecText: 'Микросекунди',
		timezoneText: 'Часови пояс',
		currentText: 'Сега',
		closeText: 'Затвори',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-ca.js
/* Catalan translation for the jQuery Timepicker Addon */
/* Written by Sergi Faber */

	$.timepicker.regional['ca'] = {
		timeOnlyTitle: 'Escollir una hora',
		timeText: 'Hora',
		hourText: 'Hores',
		minuteText: 'Minuts',
		secondText: 'Segons',
		millisecText: 'Milisegons',
		microsecText: 'Microsegons',
		timezoneText: 'Fus horari',
		currentText: 'Ara',
		closeText: 'Tancar',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-cs.js
/* Czech translation for the jQuery Timepicker Addon */
/* Written by Ondřej Vodáček */

	$.timepicker.regional['cs'] = {
		timeOnlyTitle: 'Vyberte čas',
		timeText: 'Čas',
		hourText: 'Hodiny',
		minuteText: 'Minuty',
		secondText: 'Vteřiny',
		millisecText: 'Milisekundy',
		microsecText: 'Mikrosekundy',
		timezoneText: 'Časové pásmo',
		currentText: 'Nyní',
		closeText: 'Zavřít',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['dop.', 'AM', 'A'],
		pmNames: ['odp.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-da.js
/* Danish translation for the jQuery Timepicker Addon */
/* Written by Lars H. Jensen (http://www.larshj.dk) */

    $.timepicker.regional['da'] = {
        timeOnlyTitle: 'Vælg tid',
        timeText: 'Tid',
        hourText: 'Time',
        minuteText: 'Minut',
        secondText: 'Sekund',
        millisecText: 'Millisekund',
        microsecText: 'Mikrosekund',
        timezoneText: 'Tidszone',
        currentText: 'Nu',
        closeText: 'Luk',
        timeFormat: 'HH:mm',
        timeSuffix: '',
        amNames: ['am', 'AM', 'A'],
        pmNames: ['pm', 'PM', 'P'],
        isRTL: false
    };

// source: src/i18n/jquery-ui-timepicker-de.js
/* German translation for the jQuery Timepicker Addon */
/* Written by Marvin */

	$.timepicker.regional['de'] = {
		timeOnlyTitle: 'Zeit wählen',
		timeText: 'Zeit',
		hourText: 'Stunde',
		minuteText: 'Minute',
		secondText: 'Sekunde',
		millisecText: 'Millisekunde',
		microsecText: 'Mikrosekunde',
		timezoneText: 'Zeitzone',
		currentText: 'Jetzt',
		closeText: 'Fertig',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['vorm.', 'AM', 'A'],
		pmNames: ['nachm.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-el.js
/* Hellenic translation for the jQuery Timepicker Addon */
/* Written by Christos Pontikis */

	$.timepicker.regional['el'] = {
		timeOnlyTitle: 'Επιλογή ώρας',
		timeText: 'Ώρα',
		hourText: 'Ώρες',
		minuteText: 'Λεπτά',
		secondText: 'Δευτερόλεπτα',
		millisecText: 'Χιλιοστοδευτερόλεπτα',
		microsecText: 'Μικροδευτερόλεπτα',
		timezoneText: 'Ζώνη ώρας',
		currentText: 'Τώρα',
		closeText: 'Κλείσιμο',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['π.μ.', 'AM', 'A'],
		pmNames: ['μ.μ.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-es.js
/* Spanish translation for the jQuery Timepicker Addon */
/* Written by Ianaré Sévi */
/* Modified by Carlos Martínez */

	$.timepicker.regional['es'] = {
		timeOnlyTitle: 'Elegir una hora',
		timeText: 'Hora',
		hourText: 'Horas',
		minuteText: 'Minutos',
		secondText: 'Segundos',
		millisecText: 'Milisegundos',
		microsecText: 'Microsegundos',
		timezoneText: 'Uso horario',
		currentText: 'Hoy',
		closeText: 'Cerrar',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['a.m.', 'AM', 'A'],
		pmNames: ['p.m.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-et.js
/* Estonian translation for the jQuery Timepicker Addon */
/* Written by Karl Sutt (<EMAIL>) */

	$.timepicker.regional['et'] = {
		timeOnlyTitle: 'Vali aeg',
		timeText: 'Aeg',
		hourText: 'Tund',
		minuteText: 'Minut',
		secondText: 'Sekund',
		millisecText: 'Millisekundis',
		microsecText: 'Mikrosekundis',
		timezoneText: 'Ajavöönd',
		currentText: 'Praegu',
		closeText: 'Valmis',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-eu.js
/* Basque trannslation for JQuery Timepicker Addon */
/* Translated by Xabi Fer */
/* Fixed by Asier Iturralde Sarasola - iametza interaktiboa */

	$.timepicker.regional['eu'] = {
		timeOnlyTitle: 'Aukeratu ordua',
		timeText: 'Ordua',
		hourText: 'Orduak',
		minuteText: 'Minutuak',
		secondText: 'Segundoak',
		millisecText: 'Milisegundoak',
		microsecText: 'Mikrosegundoak',
		timezoneText: 'Ordu-eremua',
		currentText: 'Orain',
		closeText: 'Itxi',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['a.m.', 'AM', 'A'],
		pmNames: ['p.m.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-fa.js
/* Persian translation for the jQuery Timepicker Addon */
/* Written by Meysam Pour Ganji */

    $.timepicker.regional['fa'] = {
        timeOnlyTitle: 'انتخاب زمان',
        timeText: 'زمان',
        hourText: 'ساعت',
        minuteText: 'دقیقه',
        secondText: 'ثانیه',
        millisecText: 'میلی ثانیه',
        microsecText: 'میکرو ثانیه',
        timezoneText: 'منطقه زمانی',
        currentText: 'الان',
        closeText: 'انتخاب',
        timeFormat: 'HH:mm',
        timeSuffix: '',
        amNames: ['قبل ظهر', 'AM', 'A'],
        pmNames: ['بعد ظهر', 'PM', 'P'],
        isRTL: true
    };

// source: src/i18n/jquery-ui-timepicker-fi.js
/* Finnish translation for the jQuery Timepicker Addon */
/* Written by Juga Paazmaya (http://github.com/paazmaya) */

	$.timepicker.regional['fi'] = {
		timeOnlyTitle: 'Valitse aika',
		timeText: 'Aika',
		hourText: 'Tunti',
		minuteText: 'Minuutti',
		secondText: 'Sekunti',
		millisecText: 'Millisekunnin',
		microsecText: 'Mikrosekuntia',
		timezoneText: 'Aikavyöhyke',
		currentText: 'Nyt',
		closeText: 'Sulje',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['ap.', 'AM', 'A'],
		pmNames: ['ip.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-fr.js
/* French translation for the jQuery Timepicker Addon */
/* Written by Thomas Lété */

	$.timepicker.regional['fr'] = {
		timeOnlyTitle: 'Choisir une heure',
		timeText: 'Heure',
		hourText: 'Heures',
		minuteText: 'Minutes',
		secondText: 'Secondes',
		millisecText: 'Millisecondes',
		microsecText: 'Microsecondes',
		timezoneText: 'Fuseau horaire',
		currentText: 'Maintenant',
		closeText: 'Terminé',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-gl.js
/* Galician translation for the jQuery Timepicker Addon */
/* Written by David Barral */

	$.timepicker.regional['gl'] = {
		timeOnlyTitle: 'Elixir unha hora',
		timeText: 'Hora',
		hourText: 'Horas',
		minuteText: 'Minutos',
		secondText: 'Segundos',
		millisecText: 'Milisegundos',
		microsecText: 'Microssegundos',
		timezoneText: 'Fuso horario',
		currentText: 'Agora',
		closeText: 'Pechar',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['a.m.', 'AM', 'A'],
		pmNames: ['p.m.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-he.js
/* Hebrew translation for the jQuery Timepicker Addon */
/* Written by Lior Lapid */

	$.timepicker.regional["he"] = {
		timeOnlyTitle: "בחירת זמן",
		timeText: "שעה",
		hourText: "שעות",
		minuteText: "דקות",
		secondText: "שניות",
		millisecText: "אלפית השנייה",
		microsecText: "מיקרו",
		timezoneText: "אזור זמן",
		currentText: "עכשיו",
		closeText:"סגור",
		timeFormat: "HH:mm",
		timeSuffix: '',
		amNames: ['לפנה"צ', 'AM', 'A'],
		pmNames: ['אחה"צ', 'PM', 'P'],
		isRTL: true
	};

// source: src/i18n/jquery-ui-timepicker-hr.js
/* Croatian translation for the jQuery Timepicker Addon */
/* Written by Mladen */

	$.timepicker.regional['hr'] = {
		timeOnlyTitle: 'Odaberi vrijeme',
		timeText: 'Vrijeme',
		hourText: 'Sati',
		minuteText: 'Minute',
		secondText: 'Sekunde',
		millisecText: 'Milisekunde',
		microsecText: 'Mikrosekunde',
		timezoneText: 'Vremenska zona',
		currentText: 'Sada',
		closeText: 'Gotovo',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['a.m.', 'AM', 'A'],
		pmNames: ['p.m.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-hu.js
/* Hungarian translation for the jQuery Timepicker Addon */
/* Written by Vas Gábor */

	$.timepicker.regional['hu'] = {
		timeOnlyTitle: 'Válasszon időpontot',
		timeText: 'Idő',
		hourText: 'Óra',
		minuteText: 'Perc',
		secondText: 'Másodperc',
		millisecText: 'Milliszekundumos',
		microsecText: 'Ezredmásodperc',
		timezoneText: 'Időzóna',
		currentText: 'Most',
		closeText: 'Kész',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['de.', 'AM', 'A'],
		pmNames: ['du.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-id.js
/* Indonesian translation for the jQuery Timepicker Addon */
/* Written by Nia */

	$.timepicker.regional['id'] = {
		timeOnlyTitle: 'Pilih Waktu',
		timeText: 'Waktu',
		hourText: 'Pukul',
		minuteText: 'Menit',
		secondText: 'Detik',
		millisecText: 'Milidetik',
		microsecText: 'Mikrodetik',
		timezoneText: 'Zona Waktu',
		currentText: 'Sekarang',
		closeText: 'OK',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-it.js
/* Italian translation for the jQuery Timepicker Addon */
/* Written by Marco "logicoder" Del Tongo */

    $.timepicker.regional['it'] = {
        timeOnlyTitle: 'Scegli orario',
        timeText: 'Orario',
        hourText: 'Ora',
        minuteText: 'Minuti',
        secondText: 'Secondi',
        millisecText: 'Millisecondi',
        microsecText: 'Microsecondi',
        timezoneText: 'Fuso orario',
        currentText: 'Adesso',
        closeText: 'Chiudi',
        timeFormat: 'HH:mm',
        timeSuffix: '',
        amNames: ['m.', 'AM', 'A'],
        pmNames: ['p.', 'PM', 'P'],
        isRTL: false
    };

// source: src/i18n/jquery-ui-timepicker-ja.js
/* Japanese translation for the jQuery Timepicker Addon */
/* Written by Jun Omae */

	$.timepicker.regional['ja'] = {
		timeOnlyTitle: '時間を選択',
		timeText: '時間',
		hourText: '時',
		minuteText: '分',
		secondText: '秒',
		millisecText: 'ミリ秒',
		microsecText: 'マイクロ秒',
		timezoneText: 'タイムゾーン',
		currentText: '現時刻',
		closeText: '閉じる',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['午前', 'AM', 'A'],
		pmNames: ['午後', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-ko.js
/* Korean translation for the jQuery Timepicker Addon */
/* Written by Genie */

	$.timepicker.regional['ko'] = {
		timeOnlyTitle: '시간 선택',
		timeText: '시간',
		hourText: '시',
		minuteText: '분',
		secondText: '초',
		millisecText: '밀리초',
		microsecText: '마이크로',
		timezoneText: '표준 시간대',
		currentText: '현재 시각',
		closeText: '닫기',
		timeFormat: 'tt h:mm',
		timeSuffix: '',
		amNames: ['오전', 'AM', 'A'],
		pmNames: ['오후', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-lt.js
/* Lithuanian translation for the jQuery Timepicker Addon */
/* Written by Irmantas Šiupšinskas */

	$.timepicker.regional['lt'] = {
		timeOnlyTitle: 'Pasirinkite laiką',
		timeText: 'Laikas',
		hourText: 'Valandos',
		minuteText: 'Minutės',
		secondText: 'Sekundės',
		millisecText: 'Milisekundės',
		microsecText: 'Mikrosekundės',
		timezoneText: 'Laiko zona',
		currentText: 'Dabar',
		closeText: 'Uždaryti',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['priešpiet', 'AM', 'A'],
		pmNames: ['popiet', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-lv.js
/* Latvian translation for the jQuery Timepicker Addon */
/* Written by Dmitry Bogatykh */

	$.timepicker.regional['lv'] = {
		timeOnlyTitle: 'Ievadiet laiku',
		timeText: 'Laiks',
		hourText: 'Stundas',
		minuteText: 'Minūtes',
		secondText: 'Sekundes',
		millisecText: 'Milisekundes',
		microsecText: 'Mikrosekundes',
		timezoneText: 'Laika josla',
		currentText: 'Tagad',
		closeText: 'Aizvērt',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'AM', 'A'],
		pmNames: ['PM', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-mk.js
/* Macedonian cyrilic translation for the jQuery Timepicker Addon */
/* Written by Vlatko Ristovski */

	$.timepicker.regional['mk'] = {
		timeOnlyTitle: 'Одберете време',
		timeText: 'Време',
		hourText: 'Час',
		minuteText: 'Минути',
		secondText: 'Секунди',
		millisecText: 'Милисекунди',
		microsecText: 'Микросекунди',
		timezoneText: 'Временска зона',
		currentText: 'Сега',
		closeText: 'Затвори',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-nl.js
/* Dutch translation for the jQuery Timepicker Addon */
/* Written by Martijn van der Lee */

	$.timepicker.regional['nl'] = {
		timeOnlyTitle: 'Tijdstip',
		timeText: 'Tijd',
		hourText: 'Uur',
		minuteText: 'Minuut',
		secondText: 'Seconde',
		millisecText: 'Milliseconde',
		microsecText: 'Microseconde',
		timezoneText: 'Tijdzone',
		currentText: 'Vandaag',
		closeText: 'Sluiten',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-no.js
/* Norwegian translation for the jQuery Timepicker Addon */
/* Written by Morten Hauan (http://hauan.me) */

	$.timepicker.regional['no'] = {
		timeOnlyTitle: 'Velg tid',
		timeText: 'Tid',
		hourText: 'Time',
		minuteText: 'Minutt',
		secondText: 'Sekund',
		millisecText: 'Millisekund',
		microsecText: 'mikrosekund',
		timezoneText: 'Tidssone',
		currentText: 'Nå',
		closeText: 'Lukk',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['am', 'AM', 'A'],
		pmNames: ['pm', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-pl.js
/* Polish translation for the jQuery Timepicker Addon */
/* Written by Michał Pena */

	$.timepicker.regional['pl'] = {
		timeOnlyTitle: 'Wybierz godzinę',
		timeText: 'Czas',
		hourText: 'Godzina',
		minuteText: 'Minuta',
		secondText: 'Sekunda',
		millisecText: 'Milisekunda',
		microsecText: 'Mikrosekunda',
		timezoneText: 'Strefa czasowa',
		currentText: 'Teraz',
		closeText: 'Gotowe',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-pt-BR.js
/* Brazilian Portuguese translation for the jQuery Timepicker Addon */
/* Written by Diogo Damiani (<EMAIL>) */

	$.timepicker.regional['pt-BR'] = {
		timeOnlyTitle: 'Escolha o horário',
		timeText: 'Horário',
		hourText: 'Hora',
		minuteText: 'Minutos',
		secondText: 'Segundos',
		millisecText: 'Milissegundos',
		microsecText: 'Microssegundos',
		timezoneText: 'Fuso horário',
		currentText: 'Agora',
		closeText: 'Fechar',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['a.m.', 'AM', 'A'],
		pmNames: ['p.m.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-pt.js
/* Portuguese translation for the jQuery Timepicker Addon */
/* Written by Luan Almeida */

	$.timepicker.regional['pt'] = {
		timeOnlyTitle: 'Escolha uma hora',
		timeText: 'Hora',
		hourText: 'Horas',
		minuteText: 'Minutos',
		secondText: 'Segundos',
		millisecText: 'Milissegundos',
		microsecText: 'Microssegundos',
		timezoneText: 'Fuso horário',
		currentText: 'Agora',
		closeText: 'Fechar',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['a.m.', 'AM', 'A'],
		pmNames: ['p.m.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-ro.js
/* Romanian translation for the jQuery Timepicker Addon */
/* Written by Romeo Adrian Cioaba */

	$.timepicker.regional['ro'] = {
		timeOnlyTitle: 'Alegeţi o oră',
		timeText: 'Timp',
		hourText: 'Ore',
		minuteText: 'Minute',
		secondText: 'Secunde',
		millisecText: 'Milisecunde',
		microsecText: 'Microsecunde',
		timezoneText: 'Fus orar',
		currentText: 'Acum',
		closeText: 'Închide',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-ru.js
/* Russian translation for the jQuery Timepicker Addon */
/* Written by Trent Richardson */

	$.timepicker.regional['ru'] = {
		timeOnlyTitle: 'Выберите время',
		timeText: 'Время',
		hourText: 'Часы',
		minuteText: 'Минуты',
		secondText: 'Секунды',
		millisecText: 'Миллисекунды',
		microsecText: 'Микросекунды',
		timezoneText: 'Часовой пояс',
		currentText: 'Сейчас',
		closeText: 'Закрыть',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-sk.js
/* Slovak translation for the jQuery Timepicker Addon */
/* Written by David Vallner */

	$.timepicker.regional['sk'] = {
		timeOnlyTitle: 'Zvoľte čas',
		timeText: 'Čas',
		hourText: 'Hodiny',
		minuteText: 'Minúty',
		secondText: 'Sekundy',
		millisecText: 'Milisekundy',
		microsecText: 'Mikrosekundy',
		timezoneText: 'Časové pásmo',
		currentText: 'Teraz',
		closeText: 'Zavrieť',
		timeFormat: 'H:m',
		timeSuffix: '',
		amNames: ['dop.', 'AM', 'A'],
		pmNames: ['pop.', 'PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-sl.js
/* Slovenian translation for the jQuery Timepicker Addon */
/* Written by Hadalin (https://github.com/hadalin) */

    $.timepicker.regional['sl'] = {
        timeOnlyTitle: 'Izberite čas',
        timeText: 'Čas',
        hourText: 'Ura',
        minuteText: 'Minute',
        secondText: 'Sekunde',
        millisecText: 'Milisekunde',
        microsecText: 'Mikrosekunde',
        timezoneText: 'Časovni pas',
        currentText: 'Sedaj',
        closeText: 'Zapri',
        timeFormat: 'HH:mm',
        timeSuffix: '',
        amNames: ['dop.', 'AM', 'A'],
        pmNames: ['pop.', 'PM', 'P'],
        isRTL: false
    };

// source: src/i18n/jquery-ui-timepicker-sq.js
/* Albanian translation for the jQuery Timepicker Addon */
/* Written by Olti Buzi */

    $.timepicker.regional['sq'] = {
        timeOnlyTitle: 'Zgjidh orarin',
        timeText: 'Orari',
        hourText: 'Ora',
        minuteText: 'Minuta',
        secondText: 'Sekonda',
        millisecText: 'Minisekonda',
        microsecText: 'Mikrosekonda',
        timezoneText: 'Zona kohore',
        currentText: 'Tani',
        closeText: 'Mbyll',
        timeFormat: 'HH:mm',
        timeSuffix: '',
        amNames: ['m.', 'AM', 'A'],
        pmNames: ['p.', 'PM', 'P'],
        isRTL: false
    };

// source: src/i18n/jquery-ui-timepicker-sr-RS.js
/* Serbian cyrilic translation for the jQuery Timepicker Addon */
/* Written by Vladimir Jelovac */

	$.timepicker.regional['sr-RS'] = {
		timeOnlyTitle: 'Одаберите време',
		timeText: 'Време',
		hourText: 'Сати',
		minuteText: 'Минути',
		secondText: 'Секунде',
		millisecText: 'Милисекунде',
		microsecText: 'Микросекунде',
		timezoneText: 'Временска зона',
		currentText: 'Сада',
		closeText: 'Затвори',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-sr-YU.js
/* Serbian latin translation for the jQuery Timepicker Addon */
/* Written by Vladimir Jelovac */

	$.timepicker.regional['sr-YU'] = {
		timeOnlyTitle: 'Odaberite vreme',
		timeText: 'Vreme',
		hourText: 'Sati',
		minuteText: 'Minuti',
		secondText: 'Sekunde',
		millisecText: 'Milisekunde',
		microsecText: 'Mikrosekunde',
		timezoneText: 'Vremenska zona',
		currentText: 'Sada',
		closeText: 'Zatvori',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-sv.js
/* Swedish translation for the jQuery Timepicker Addon */
/* Written by Nevon */

	$.timepicker.regional['sv'] = {
		timeOnlyTitle: 'Välj en tid',
		timeText: 'Tid',
		hourText: 'Timme',
		minuteText: 'Minut',
		secondText: 'Sekund',
		millisecText: 'Millisekund',
		microsecText: 'Mikrosekund',
		timezoneText: 'Tidszon',
		currentText: 'Nu',
		closeText: 'Stäng',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-th.js
/* Thai translation for the jQuery Timepicker Addon */
/* Written by Yote Wachirapornpongsa */

	$.timepicker.regional['th'] = {
		timeOnlyTitle: 'เลือกเวลา',
		timeText: 'เวลา ',
		hourText: 'ชั่วโมง ',
		minuteText: 'นาที',
		secondText: 'วินาที',
		millisecText: 'มิลลิวินาที',
		microsecText: 'ไมโคริวินาที',
		timezoneText: 'เขตเวลา',
		currentText: 'เวลาปัจจุบัน',
		closeText: 'ปิด',
		timeFormat: 'hh:mm tt',
		timeSuffix: ''
	};

// source: src/i18n/jquery-ui-timepicker-tr.js
/* Turkish translation for the jQuery Timepicker Addon */
/* Written by Fehmi Can Saglam, Edited by Goktug Ozturk */

	$.timepicker.regional['tr'] = {
		timeOnlyTitle: 'Zaman Seçiniz',
		timeText: 'Zaman',
		hourText: 'Saat',
		minuteText: 'Dakika',
		secondText: 'Saniye',
		millisecText: 'Milisaniye',
		microsecText: 'Mikrosaniye',
		timezoneText: 'Zaman Dilimi',
		currentText: 'Şu an',
		closeText: 'Tamam',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['ÖÖ', 'Ö'],
		pmNames: ['ÖS', 'S'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-uk.js
/* Ukrainian translation for the jQuery Timepicker Addon */
/* Written by Sergey Noskov */

	$.timepicker.regional['uk'] = {
		timeOnlyTitle: 'Виберіть час',
		timeText: 'Час',
		hourText: 'Години',
		minuteText: 'Хвилини',
		secondText: 'Секунди',
		millisecText: 'Мілісекунди',
		microsecText: 'Мікросекунди',
		timezoneText: 'Часовий пояс',
		currentText: 'Зараз',
		closeText: 'Закрити',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-vi.js
/* Vietnamese translation for the jQuery Timepicker Addon */
/* Written by Nguyen Dinh Trung */

	$.timepicker.regional['vi'] = {
		timeOnlyTitle: 'Chọn giờ',
		timeText: 'Thời gian',
		hourText: 'Giờ',
		minuteText: 'Phút',
		secondText: 'Giây',
		millisecText: 'Mili giây',
		microsecText: 'Micrô giây',
		timezoneText: 'Múi giờ',
		currentText: 'Hiện thời',
		closeText: 'Đóng',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['SA', 'S'],
		pmNames: ['CH', 'C'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-zh-CN.js
/* Simplified Chinese translation for the jQuery Timepicker Addon /
/ Written by Will Lu */

	$.timepicker.regional['zh-CN'] = {
		timeOnlyTitle: '选择时间',
		timeText: '时间',
		hourText: '小时',
		minuteText: '分钟',
		secondText: '秒钟',
		millisecText: '毫秒',
		microsecText: '微秒',
		timezoneText: '时区',
		currentText: '现在时间',
		closeText: '关闭',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['AM', 'A'],
		pmNames: ['PM', 'P'],
		isRTL: false
	};

// source: src/i18n/jquery-ui-timepicker-zh-TW.js
/* Chinese translation for the jQuery Timepicker Addon */
/* Written by Alang.lin */

	$.timepicker.regional['zh-TW'] = {
		timeOnlyTitle: '選擇時分秒',
		timeText: '時間',
		hourText: '時',
		minuteText: '分',
		secondText: '秒',
		millisecText: '毫秒',
		microsecText: '微秒',
		timezoneText: '時區',
		currentText: '現在時間',
		closeText: '確定',
		timeFormat: 'HH:mm',
		timeSuffix: '',
		amNames: ['上午', 'AM', 'A'],
		pmNames: ['下午', 'PM', 'P'],
		isRTL: false
	};

})(jQuery);
