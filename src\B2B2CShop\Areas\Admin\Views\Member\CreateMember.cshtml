﻿@{
}
<style asp-location="true">
    .page {
        min-height: 415px
    }
</style>
<div class="page">
    <form id="user_form" enctype="multipart/form-data" method="post">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120"><label class="validation" for="Name">@T("会员名"):</label></td>
                    <td class="vatop rowform"><input type="text" value="" name="Name" id="Name" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="validation" for="PassWord">@T("密码"):</label></td>
                    <td class="vatop rowform"><input type="text" id="PassWord" name="PassWord" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="Mail">@T("电子邮箱"):</label></td>
                    <td class="vatop rowform"><input type="text" value="" id="Mail" name="Mail" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="member_mobile">@T("手机号"):</label></td>
                    <td class="vatop rowform"><input type="text" value="" id="Mobile" name="Mobile" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="TrueName">@T("真实姓名"):</label></td>
                    <td class="vatop rowform"><input type="text" value="" id="TrueName" name="TrueName" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder" style="background: rgb(255, 255, 255);">
                    <td class="required"><label for="DisplayName">@T("昵称"):</label></td>
                    <td class="vatop rowform"><input type="text" value="" id="DisplayName" name="DisplayName" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label> @T("性别"):</label></td>
                    <td class="vatop rowform">
                        <label class="radio-label">
                            <i class="radio-common selected">
                                <input type="radio" value="0" name="Sex" checked="checked">
                            </i>
                            <span>@T("保密")</span>
                        </label>
                        <label class="radio-label">
                            <i class="radio-common">
                                <input type="radio" value="1" name="Sex">
                            </i>
                            <span>@T("男")</span>
                        </label>
                        <label class="radio-label">
                            <i class="radio-common">
                                <input type="radio" value="2" name="Sex">
                            </i>
                            <span>@T("女")</span>
                        </label>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="QQ">QQ:</label></td>
                    <td class="vatop rowform"><input type="text" value="" id="QQ" name="QQ" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="WangWang">@T("阿里旺旺"):</label></td>
                    <td class="vatop rowform"><input type="text" value="" id="WangWang" name="WangWang" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="WeiXin">@T("微信"):</label></td>
                    <td class="vatop rowform"><input type="text" value="" id="WeiXin" name="WeiXin" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="WhatsApp">@T("WhatsApp"):</label></td>
                    <td class="vatop rowform"><input type="text" value="" id="WhatsApp" name="WhatsApp" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="Skype">@T("Skype"):</label></td>
                    <td class="vatop rowform"><input type="text" value="" id="Skype" name="Skype" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="member_ww">@T("员工编号"):</label></td>
                    <td class="vatop rowform"><input type="text" value="" id="Code" name="Code" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="member_ww">@T("备注"):</label></td>
                    <td class="vatop rowform"><textarea name="Remark"></textarea></td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script asp-location="Footer">
    $(function () {
        //  // 获取表单元素
        //  const form = document.getElementById('user_form');

        // // 添加表单提交事件监听器
        // form.addEventListener('submit', function(event) {
        //     // 阻止表单默认提交行为
        //     event.preventDefault();

        //     // 在这里处理您的自定义逻辑
        //     console.log('表单提交事件被拦截',event.serialize());

        //     // 如果需要，您可以手动提交表单
        //     // form.submit();
        // });
        //自定义手机号验证
            jQuery.validator.addMethod("isPhoneNum", function (value, element) {
                var length = value.length;
                var mobile = /^(13[0-9]{9})|(18[0-9]{9})|(14[0-9]{9})|(17[0-9]{9})|(15[0-9]{9})$/;
                return this.optional(element) || (length == 11 && mobile.test(value));
            }, "@T("请正确填写您的手机号码")");

            $('#user_form').validate({
                errorPlacement: function (error, element) {
                        error.appendTo(element.parent().parent().find('td:last'));
                },
                submitHandler:function(form){
                    var pwdInput = $('#PassWord');
                    pwdInput.val(generateHash2(pwdInput.val()))
                    form.submit();
            },   
            rules: {
                Name: {
                    required: true,
                    minlength: 3,
                    maxlength: 32,
                    remote: {
                        url: "@Url.Action("VerifyName")",
                        type: 'get',
                        data: {
                            user_name: function () {
                                return $('#Name').val();
                            },
                            member_id: '0'
                        }
                    }
                },
                PassWord: {
                    required: true,
                    maxlength: 33,
                    minlength: 6
                },
                DisplayName: {
                        minlength: 3,
                        maxlength: 30,
                        remote: {
                            url: '@Url.Action("VerifyDisplayName")',
                            type: 'get',
                            data: {
                                DisplayName: function () {
                                    return $('#DisplayName').val();
                                }
                            }
                        }
                    },
                Mail: {
                    email: true,
                    remote: {
                        url: "@Url.Action("VerifyEmail")",
                        type: 'get',
                        data: {
                            Mail: function () {
                                return $('#Mail').val();
                            }
                        }
                    }
                },
                QQ: {
                    digits: true,
                    minlength: 5,
                    maxlength: 11
                },
                Mobile: {
                    isPhoneNum: true,
                    remote: {
                    url: "@Url.Action("VerifyMobile")",
                    type: 'get',
                        data: {
                             Mobile: function () {
                                 return $('#Mobile').val();
                             }
                        }
                    }
                }
            },
            messages: {
                Name: {
                    required: '@T("会员名不能为空")',
                    maxlength: '@T("用户名必须在3-15字符之间")',
                    minlength: '@T("用户名必须在3-15字符之间")',
                    remote: '@T("会员名有重复，请您换一个")'
                },
                PassWord: {
                    required: '@T("密码不能为空")',
                    maxlength: '@T("密码长度应在6-33个字符之间")',
                    minlength: '@T("密码长度应在6-33个字符之间")'
                },
                Mail: {
                    email: '@T("请您填写有效的电子邮箱")',
                    remote: '@T("邮件地址有重复，请您换一个")'
                },
                Mobile: {
                    isPhoneNum: '@T("请您填写有效的手机号码")',
                    remote: '@T("手机号码有重复，请您换一个")'
                },
                QQ: {
                    digits: '@T("请输入正确的QQ号码")',
                    minlength: '@T("请输入正确的QQ号码")',
                    maxlength: '@T("请输入正确的QQ号码")'
                },
                DisplayName: {
                    maxlength: '@T("昵称必须在3-30字符之间")',
                    minlength: '@T("昵称必须在3-30字符之间")',
                    remote: '@T("昵称有重复，请您换一个")'
                }
            }
        });
    });
</script>