﻿@{ 
}
<style asp-location="true">
    .layui-form-select dl {
        z-index: 9999 !important;
    }

    .layui-input-block {
        margin-left: auto !important;
    }

    .xm-body.absolute {
        z-index: 99999 !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("多语言")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>

    <form id="doc_form" method="post">
        <input type="hidden" name="document_id" value="1" />
        <table class="ds-default-table">
            <tbody>
                <tr>
                    <td class="required"><label class="validation">@T("标题") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="" name="Name" id="Name" class="infoTableInput w300"></td>
                </tr>
                <tr>
                    <td class="required"><label class="validation">@T("显示名称") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="" name="DisplayName" id="DisplayName" class="infoTableInput w300"></td>
                </tr>
                <tr>
                    <td class="required"><label class="validation">@T("英文名") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="" name="EnglishName" id="EnglishName" class="infoTableInput w300"></td>
                </tr>
                <tr>
                    <td class="required"><label class="validation">@T("语言缩写") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="" name="LanguageCulture" id="LanguageCulture" class="infoTableInput w300"></td>
                </tr>
                <tr>
                    <td class="required"><label class="validation">@T("Url缩写") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="" name="UniqueSeoCode" id="UniqueSeoCode" class="infoTableInput w300"></td>
                </tr>
                <tr>
                    <td ><label >@T("旗帜") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="" name="Flag" id="Flag" class="infoTableInput w300"></td>
                </tr>
                <tr>
                    <td ><label >@T("域名") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="" name="Domain" id="Domain" class="infoTableInput w300"></td>
                </tr>
                <tr>
                    <td ><label >LCID </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="" name="Lcid" id="Lcid" class="infoTableInput w300"></td>
                </tr>
                <tr>
                    <td ><label>@T("状态"):</label></td>
                </tr>
                <tr class="noborder">

                    <td class="vatop rowform onoff">

                        <label for="isbuy_1" class="cb-enable selected"><span>@T("有效")</span></label>
                        <label for="isbuy_2" class="cb-disable  "><span>@T("无效")</span></label>
                        <input id="isbuy_1" name="Status" value="1" type="radio" checked="checked">
                        <input id="isbuy_2" name="Status" value="0" type="radio" >

                    </td>
                </tr>
                <tr>
                    <td><label>@T("是否默认"):</label></td>
                </tr>
                <tr class="noborder">

                    <td class="vatop rowform onoff">
                        <label for="isdefault_1" class="cb-enable "><span>@T("是")</span></label>
                        <label for="isdefault_2" class="cb-disable selected"><span>@T("否")</span></label>
                        <input id="isdefault_1" name="IsDefault" value="1" type="radio">
                        <input id="isdefault_2" name="IsDefault" value="0" type="radio" checked="checked">
                    </td>
                </tr>
                <tr>
                    <td ><label>@T("排序") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><input type="text" value="@ViewBag.DisplayOrder" name="DisplayOrder" id="DisplayOrder" class="infoTableInput w300"></td>
                </tr>
                <tr>
                    <td><label>@T("描述") </label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform"><textarea name="Remark" id="Remark" style="min-width: 800px; min-height: 100px;"></textarea></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script asp-location="Footer">
        $('#doc_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().prev().find('td:first'));
            },
            rules: {
                Name: {
                    required: true
                },
                DisplayName: {
                    required: true
                },
                EnglishName: {
                    required: true,
                },
                LanguageCulture: {
                    required: true,
                },
                UniqueSeoCode: {
                    required: true,
                }
            },
            messages: {
                Name: {
                    required: '@T("标题不能为空")'
                },
                DisplayName: {
                    required: '@T("显示名称不为能空")'
                },
                EnglishName: {
                    required: '@T("英文名称不为能空")',
                },
                LanguageCulture: {
                    required: '@T("语言缩写不为能空")',
                },
                UniqueSeoCode: {
                    required: '@T("Url缩写不为能空")',
                }
            }
        });
 
</script>
