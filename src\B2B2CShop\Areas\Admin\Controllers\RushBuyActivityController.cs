﻿using Aop.Api.Domain;
using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.FileProviders;
using NewLife.Data;
using NewLife.Log;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Webs;
using System.ComponentModel;
using System.Dynamic;
using XCode;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("抢购活动")]
    [Description("抢购活动管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/RushBuyActivity", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/RushBuyActivity", CurrentMenuName = "RushBuyActivityList", CurrentIcon = "&#xe734;", LastUpdate = "20241203",CurrentVisible = false)]
    public class RushBuyActivityController : PekCubeAdminControllerX
    {
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("抢购活动管理")]
        public IActionResult Index(string name,long storeId,int status,int page = 1,int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            var list = RushBuyGoods.Search(name, storeId, status, pages);
            viewModel.list = list;
            viewModel.name = name;
            viewModel.storeId = storeId;
            viewModel.status = status;
            viewModel.storelist = Store.FindAll();
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {
            { "name", name },
            { "storeId", storeId.SafeString() },
            { "status", status.SafeString() },
        });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("抢购活动审核")]
        [HttpPost]
        public IActionResult Review(long id, bool flag)
        {
            if (id <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("参数错误") });
            }
            var entity = RushBuyGoods.FindById(id);
            if (entity == null)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到记录") });
            }
            if (entity.State != 10) // 10:审核中
            {
                return Json(new DResult() { success = false, msg = GetResource("当前状态不可审核") });
            }
            entity.State = flag ? 20 : 30; // 20:正常, 30:审核失败
            entity.Update();
            RushBuyGoods.Meta.Cache.Clear("", true);
            return Json(new DResult() { success = true, msg = GetResource(flag ? "审核通过" : "审核拒绝") });
        }


        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("设置推荐")]
        [HttpPost]
        public IActionResult SetRecommend(long id, bool flag)
        {
            if (id <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("参数错误") });
            }
            var entity = RushBuyGoods.FindById(id);
            if (entity == null)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到记录") });
            }
            entity.Recommended = flag;
            entity.Update();
            RushBuyGoods.Meta.Cache.Clear("", true);

            return Json(new DResult() { success = true, msg = GetResource(flag ? "设置为推荐" : "取消推荐") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("删除抢购活动")]
        public IActionResult DeleteActivity(string ids)
        {
            if (string.IsNullOrWhiteSpace(ids))
            {
                return Json(new DResult() { success = false, msg = GetResource("参数错误") });
            }

            var count = RushBuyGoods.DeleteByIds(ids);
            if (count <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("删除失败") });
            }
            return Json(new DResult() { success = true, msg = GetResource("删除成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("取消抢购活动")]
        public IActionResult CancelActivity(long id)
        {
            var entity = RushBuyGoods.FindById(id);
            if (entity==null)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到记录") });
            }
            entity.State = 31;
            entity.Update();
            RushBuyGoods.Meta.Cache.Clear("", true);
            return Json(new DResult() { success = true, msg = GetResource("取消成功") });
        }



        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐管理")]
        public IActionResult RushBuyPackage(long storeId, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.storelist = Store.FindAll();
            viewModel.storeId = storeId;
            viewModel.list = RushBuyQuota.Search(storeId, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("RushBuyPackage"), new Dictionary<string, string> {
                { "storeId", storeId.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("抢购分类")]
        public IActionResult RushBuyClassList()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.list = RushBuyClass.FindAllByParentId(0);
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增抢购分类")]
        public IActionResult AddRushBuyClass()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.list = RushBuyClass.FindAllByParentId(0);
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增抢购分类")]
        [HttpPost]
        public IActionResult AddRushBuyClass(string name,int parentId,int sort)
        {
            if (name.IsNullOrWhiteSpace())
            {
                return Json(new DResult() { success = false, msg = GetResource("抢购分类名称不能为空") });
            }
            var rushBuyClass = new RushBuyClass();
            rushBuyClass.Name = name;
            rushBuyClass.ParentId = parentId;
            rushBuyClass.Sort = sort;
            rushBuyClass.Deep = sort==0?0:1;
            rushBuyClass.Insert();
            if (LocalizationSettings.Current.IsEnable)
            {   
                var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                foreach (var item in LanguageList)
                {
                    var lan = new RushBuyClassLan();
                    lan.RId = rushBuyClass.Id;
                    lan.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                    lan.LId = item.Id;
                    lan.Insert();
                }
            }
            return Json(new DResult() { success = true, msg = GetResource("新增成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增抢购分类下级")]
        public IActionResult AddRushBuyClassSubordinate(int parentId)
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.list = RushBuyClass.FindAllByParentId(0);
            viewModel.parentId = parentId;
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增抢购分类下级")]
        [HttpPost]
        public IActionResult AddRushBuyClassSubordinate(string name, int parentId, int sort)
        {

            if (name.IsNullOrWhiteSpace())
            {
                return Json(new DResult() { success = false, msg = GetResource("抢购分类名称不能为空") });
            }
            var rushBuyClass = new RushBuyClass();
            rushBuyClass.Name = name;
            rushBuyClass.ParentId = parentId;
            rushBuyClass.Sort = sort;
            rushBuyClass.Deep = sort == 0 ? 0 : 1;
            rushBuyClass.Insert();
            if (LocalizationSettings.Current.IsEnable)
            {
                var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                foreach (var item in LanguageList)
                {
                    var lan = new RushBuyClassLan();
                    lan.RId = rushBuyClass.Id;
                    lan.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                    lan.LId = item.Id;
                    lan.Insert();
                }
            }
            return Json(new DResult() { success = true, msg = GetResource("新增成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑抢购分类")]
        public IActionResult EditRushBuyClass(int id)
        {
            var rushBuyClass = RushBuyClass.FindById(id);
            if (rushBuyClass == null)
            {
                return Content(GetResource("未找到记录"));
            }
            
            dynamic viewModel = new ExpandoObject();
            viewModel.list = RushBuyClass.FindAllByParentId(0);
            viewModel.rushBuyClass = rushBuyClass;
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑抢购分类")]
        [HttpPost]
        public IActionResult EditRushBuyClass(int id,string name, int parentId, int sort)
        {
            var rushBuyClass = RushBuyClass.FindById(id);
            if (rushBuyClass == null)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到抢购分类记录") });
            }

            if (name.IsNullOrWhiteSpace())
            {
                return Json(new DResult() { success = false, msg = GetResource("抢购分类名称不能为空") });
            }
            rushBuyClass.Name = name;
            rushBuyClass.ParentId = parentId;
            rushBuyClass.Sort = sort;
            rushBuyClass.Deep = sort == 0 ? 0 : 1;
            rushBuyClass.Update();
            if (LocalizationSettings.Current.IsEnable)
            {
                var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                foreach (var item in LanguageList)
                {
                    var lan = RushBuyClassLan.FindByRIdAndLId(rushBuyClass.Id,item.Id);
                    if (lan==null)
                    {
                        lan = new RushBuyClassLan();
                        lan.RId = rushBuyClass.Id;
                        lan.LId = item.Id;
                    }
                    lan.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                    lan.Save();
                }
            }
            RushBuyClass.Meta.Cache.Clear("", true);

            return Json(new DResult() { success = true, msg = GetResource("编辑成功") });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除抢购分类")]
        public IActionResult DeleteRushBuyClass(string ids)
        {
            if (ids.IsNullOrWhiteSpace())
            {
                return Json(new DResult() { success = false, msg = GetResource("参数错误") });
            }

            var rootIds = ids.SplitAsInt();
            if (rootIds == null || rootIds.Length == 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("参数错误") });
            }

            // 收集所有待删分类（包含子级）
            var idSet = new HashSet<int>(rootIds);
            var frontier = new List<int>(rootIds);
            while (frontier.Count > 0)
            {
                var children = RushBuyClass.FindAll(RushBuyClass._.ParentId.In(frontier));
                if (children == null || children.Count == 0) break;

                var next = new List<int>();
                foreach (var c in children)
                {
                    if (idSet.Add(c.Id)) next.Add(c.Id);
                }
                frontier = next;
            }

            // 若有抢购商品使用这些分类（GClassId），则禁止删除
            var used = RushBuyGoods.FindCount(RushBuyGoods._.GClassId.In(idSet)) > 0;
            if (used)
            {
                return Json(new DResult() { success = false, msg = GetResource("所选分类或其子级已被抢购活动使用，禁止删除") });
            }

            var count = RushBuyClass.DeleteByIds(ids);
            if (count <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("删除失败") });
            }
            return Json(new DResult() { success = true, msg = GetResource("删除成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("抢购价格区间")]
        public IActionResult RushBuyPriceRangeList()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.list = RushBuyPriceRange.FindAll();
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("添加价格区间")]
        public IActionResult AddPriceRange()
        {
            return View();
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("添加价格区间")]
        [HttpPost]
        public IActionResult AddPriceRange(string name, int gprangstart ,int gprangend)
        {
            // 名称必填
            if (name.IsNullOrWhiteSpace())
                return Json(new DResult() { success = false, msg = GetResource("价格区间名称不能为空") });

            // 区间有效性
            if (gprangstart < 0 || gprangend <= 0 || gprangstart >= gprangend)
                return Json(new DResult() { success = false, msg = GetResource("区间范围不合法") });

            // 区间不重叠校验
            if (RushBuyPriceRange.IsRangeOverlap(gprangstart, gprangend))
                return Json(new DResult() { success = false, msg = GetResource("区间与现有区间有重叠") });

            // 保存
            var entity = new RushBuyPriceRange
            {
                Name = name,
                Start = gprangstart,
                End = gprangend
            };
            entity.Insert();

            return Json(new DResult() { success = true, msg = GetResource("新增成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑价格区间")]
        public IActionResult EditPriceRange(int id)
        {
            var entity = RushBuyPriceRange.FindById(id);
            if (entity == null)
            {
                return Content(GetResource("未找到价格区间记录"));
            }
            return View(entity);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑价格区间")]
        [HttpPost]
        public IActionResult EditPriceRange(int id, string name, int gprangstart, int gprangend)
        {
            var entity = RushBuyPriceRange.FindById(id);
            if (entity == null)
                return Json(new DResult() { success = false, msg = GetResource("未找到价格区间记录") });

            if (name.IsNullOrWhiteSpace())
                return Json(new DResult() { success = false, msg = GetResource("价格区间名称不能为空") });

            if (gprangstart < 0 || gprangend <= 0 || gprangstart >= gprangend)
                return Json(new DResult() { success = false, msg = GetResource("区间范围不合法") });

            // 排除自身，校验区间不重叠
            if (RushBuyPriceRange.IsRangeOverlap(gprangstart, gprangend, id))
                return Json(new DResult() { success = false, msg = GetResource("区间与现有区间有重叠") });

            entity.Name = name;
            entity.Start = gprangstart;
            entity.End = gprangend;
            entity.Update();
            RushBuyPriceRange.Meta.Cache.Clear("", true);

            return Json(new DResult() { success = true, msg = GetResource("编辑成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("删除价格区间")]
        public IActionResult DeletePriceRange(string ids)
        {
            if (ids.IsNullOrWhiteSpace())
            {
                return Json(new DResult() { success = false, msg = GetResource("参数错误") });
            }
            var rootIds = ids.SplitAsInt();
            if (rootIds == null || rootIds.Length == 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("参数错误") });
            }
            var count = RushBuyPriceRange.DeleteByIds(ids);
            if (count <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("删除失败") });
            }
            return Json(new DResult() { success = true, msg = GetResource("删除成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        public IActionResult RushBuySetting()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.price = OperationConfig.GetValueByCode("RushBuyPackagePrice");// 获取抢购价格设置
            viewModel.reviewdays = OperationConfig.GetValueByCode("RushBuyPackageReviewDays");// 获取抢购审核天数
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        [HttpPost]
        public IActionResult RushBuySetting(decimal price,int reviewdays)
        {
            var flag = OperationConfig.UpdateValueByCode("RushBuyPackagePrice", price.SafeString(),"抢购套餐价格");// 获取抢购价格设置
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到抢购价格配置") });
            }
            flag = OperationConfig.UpdateValueByCode("RushBuyPackageReviewDays", reviewdays.SafeString(), "抢购套餐审核天数");// 获取抢购审核天数
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到抢购审核天数配置") });
            }
            return Json(new DResult() { success = true, msg = GetResource("设置成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("幻灯片设置")]
        public IActionResult SliderSetting()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.pic1 = OperationConfig.GetValueByCode("RushBuyPic1");
            viewModel.pic2 = OperationConfig.GetValueByCode("RushBuyPic2");
            viewModel.pic3 = OperationConfig.GetValueByCode("RushBuyPic3");
            viewModel.pic4 = OperationConfig.GetValueByCode("RushBuyPic4");

            viewModel.url1 = OperationConfig.GetValueByCode("RushBuyPicUrl1");
            viewModel.url2 = OperationConfig.GetValueByCode("RushBuyPicUrl2");
            viewModel.url3 = OperationConfig.GetValueByCode("RushBuyPicUrl3");
            viewModel.url4 = OperationConfig.GetValueByCode("RushBuyPicUrl4");

            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("幻灯片设置")]
        [HttpPost]
        public IActionResult SliderSetting(IFormFile[] files, string[] urlpaths)
        {
            if (files == null || files.Length == 0)
                return Json(new DResult() { success = false, msg = GetResource("请上传图片") });

            for (int i = 0; i < files.Length; i++)
            {
                var file = files[i];
                if (file == null || file.Length == 0) continue;

                // 生成唯一文件名

                var bytes = file.OpenReadStream().ReadBytes(file.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                var filepath = $"Operation/RushBuySlider/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                file.SaveAs(saveFileName);

                // 保存图片路径到配置表
                var picKey = $"RushBuyPic{i + 1}";
                var flag = OperationConfig.UpdateValueByCode(picKey, filepath, $"抢购套餐幻灯片{i + 1}");
                if (!flag)
                {
                    return Json(new DResult() { success = false, msg = GetResource("未找到抢购幻灯片配置") });
                }
                // 保存图片点击链接到配置表
                var urlKey = $"RushBuyPicUrl{i + 1}";
                var url = (urlpaths != null && urlpaths.Length > i) ? urlpaths[i] : "";
                flag = OperationConfig.UpdateValueByCode(urlKey, url.SafeString(),$"抢购套餐幻灯片跳转地址{i + 1}");
                if (!flag)
                {
                    return Json(new DResult() { success = false, msg = GetResource("未找到抢购幻灯片配置") });
                }
            }

            return Json(new DResult() { success = true, msg = GetResource("设置成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("清空幻灯片")]
        public IActionResult ClearSlider()
        {
            for (int i = 0; i < 4; i++)
            {
                // 1. 获取图片路径
                var picKey = $"RushBuyPic{i + 1}";
                var picPath = OperationConfig.GetValueByCode(picKey);

                // 2. 删除物理文件
                if (!string.IsNullOrWhiteSpace(picPath))
                {
                    var fullPath = picPath;
                    if (!System.IO.Path.IsPathRooted(picPath))
                    {
                        fullPath = DHSetting.Current.UploadPath.GetFullPath().CombinePath(picPath.Replace(DHSetting.Current.UploadPath, "").TrimStart('/', '\\'));
                    }
                    if (System.IO.File.Exists(fullPath))
                    {
                        try { System.IO.File.Delete(fullPath); } catch { /* 忽略异常 */ }
                    }
                }


                // 保存图片路径到配置表
                var flag = OperationConfig.UpdateValueByCode(picKey, "", $"抢购套餐幻灯片{i + 1}");
                if (!flag)
                {
                    return Json(new DResult() { success = false, msg = GetResource("未找到抢购幻灯片配置") });
                }
                // 保存图片点击链接到配置表
                var urlKey = $"RushBuyPicUrl{i + 1}";
                flag = OperationConfig.UpdateValueByCode(urlKey, "", $"抢购套餐幻灯片跳转地址{i + 1}");
                if (!flag)
                {
                    return Json(new DResult() { success = false, msg = GetResource("未找到抢购幻灯片配置") });
                }
            }
            return Json(new DResult() { success = true, msg = GetResource("设置成功") });

        }

    }
}
