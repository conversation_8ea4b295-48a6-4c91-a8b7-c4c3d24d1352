{
  "version": "0.2.0",
  "configurations": [
    {
      // 使用 IntelliSense 找出 C# 调试存在哪些属性
      // 将悬停用于现有属性的说明
      // 有关详细信息，请访问 https://github.com/dotnet/vscode-csharp/blob/main/debugger-launchjson.md。
      "name": ".NET Core Launch (web)",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      // 如果已更改目标框架，请确保更新程序路径。
      "program": "${workspaceFolder}/Bin/B2B2CShop/B2B2CShop.dll",
      "args": [],
      "cwd": "${workspaceFolder}/src/B2B2CShop",
      "stopAtEntry": false,
      // 启用在启动 ASP.NET Core 时启动 Web 浏览器。有关详细信息: https://aka.ms/VSCode-CS-LaunchJson-WebBrowser
      "serverReadyAction": {
        "action": "openExternally",
        "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
      },
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      },
      "sourceFileMap": {
        "/Views": "${workspaceFolder}/Views"
      }
    },
    {
      "name": ".NET Core Attach",
      "type": "coreclr",
      "request": "attach"
    }
  ]
}