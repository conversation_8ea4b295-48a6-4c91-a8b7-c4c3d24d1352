﻿using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Timing;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("代金券")]
    [Description("代金券管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/Voucher", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/Voucher", CurrentMenuName = "VoucherList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class VoucherController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("代金券管理")]
        public IActionResult Index(long storeId, DateTime startTime, DateTime endTime, int status, int recommend=-1, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            if (endTime > DateTime.MinValue)
            {
                endTime = endTime.Date.AddDays(1).AddSeconds(-1); // 设置为 23:59:59
            }
            viewModel.list = VoucherTemplate.Search(storeId,  UnixTime.ToTimestamp(startTime), UnixTime.ToTimestamp(endTime), recommend, status,pages);
            viewModel.storelist = Store.FindAll();
            viewModel.storeId = storeId;
            viewModel.startTime = startTime;
            viewModel.endTime = endTime;
            viewModel.status = status;
            viewModel.recommend = recommend;
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {
                { "storeId", storeId.SafeString() },
                { "startTime", startTime.SafeString() },
                { "endTime", endTime.SafeString() },
                { "status", status.SafeString() },
                { "recommend", recommend.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("代金券编辑")]
        public IActionResult EditVoucher(long id)
        {
            var entity = VoucherTemplate.FindById(id);
            if (entity == null)
            {
                return Content(GetResource("未找到代金券记录"));
            }
            return View(entity);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("代金券编辑")]
        [HttpPost]
        public IActionResult EditVoucher(long id,int points,int status,bool recommended)
        {
            var entity = VoucherTemplate.FindById(id);
            if (entity == null)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到代金券记录") });
            }
            entity.Points  = points;
            entity.State = status;
            entity.Recommend = recommended;
            entity.Update();
            VoucherTemplate.Meta.Cache.Clear("",true);
            return Json(new DResult() { success = true, msg = GetResource("操作成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("设置推荐")]
        [HttpPost]
        public IActionResult SetRecommend(long id, bool flag)
        {
            if (id <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("参数错误") });
            }
            var entity = VoucherTemplate.FindById(id);
            if (entity == null)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到记录") });
            }
            entity.Recommend = flag;
            entity.Update();
            VoucherTemplate.Meta.Cache.Clear("", true);

            return Json(new DResult() { success = true, msg = GetResource(flag ? "设置为推荐" : "取消推荐") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐管理")]
        public IActionResult VoucherPackage(long storeId, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.storelist = Store.FindAll();
            viewModel.storeId = storeId;
            viewModel.list = VoucherQuota.Search(storeId, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("VoucherPackage"), new Dictionary<string, string> {
                { "storeId", storeId.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("面额管理")]
        public IActionResult VoucherDenomination()
        {
            var list = VoucherPrice.FindAll();
            return View(list);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增面额")]
        public IActionResult AddDenomination()
        {
            return View();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="price">面额</param>
        /// <param name="desc">描述</param>
        /// <param name="points">兑换所需积分</param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增面额")]
        [HttpPost]
        public IActionResult AddDenomination(decimal price, string desc, int points)
        {
            if (price <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("面额必须大于0") });
            }
            if (desc.IsNullOrWhiteSpace())
            {
                return Json(new DResult() { success = false, msg = GetResource("请填写描述") });
            }
            if (points < 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("兑换积分必须大于0") });
            }
            var entity = new VoucherPrice
            {
                Price = price,
                Description = desc,
                DefaultPoints = points,
            };
            entity.Insert();
            return Json(new DResult() { success = true, msg = GetResource("新增成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑面额")]
        public IActionResult EditDenomination(int id)
        {
            var entity = VoucherPrice.FindById(id);
            if (entity == null)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到面额记录" )});
            }
            return View(entity);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id">代金券ID</param>
        /// <param name="price">面额</param>
        /// <param name="desc">描述</param>
        /// <param name="points">兑换所需积分</param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑面额")]
        [HttpPost]
        public IActionResult EditDenomination(int id, decimal price, string desc, int points)
        {
            var entity = VoucherPrice.FindById(id);
            if (entity == null)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到面额记录") });
            }
            if (price <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("面额必须大于0") });
            }
            entity.Price = price;
            entity.Description = desc;
            entity.DefaultPoints = points;
            entity.Update();
            return Json(new DResult() { success = true, msg = GetResource("编辑成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("删除面额")]
        public IActionResult DeleteDenomination(string ids)
        {
            if (string.IsNullOrEmpty(ids))
            {
                return Json(new DResult() { success = false, msg = GetResource("参数错误") });
            }
            VoucherPrice.DeleteByIds(ids);
            return Json(new DResult() { success = true, msg = GetResource("删除成功") });
        }


        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        public IActionResult PackageSetting()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.price = OperationConfig.GetValueByCode("VoucherPackagePrice");//代金券套餐价格
            viewModel.num = OperationConfig.GetValueByCode("VoucherActivityNum");//每月活动数量
            viewModel.maxnum = OperationConfig.GetValueByCode("VoucherUserClaimMaxNum");//买家最大领取数量
            return View(viewModel);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="price">购买单价</param>
        /// <param name="num">每月活动数量</param>
        /// <param name="maxnum">买家最大领取数量</param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        [HttpPost]
        public IActionResult PackageSetting(decimal price,int num,int maxnum)
        {
            // 参数校验
            if (price < 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("价格不能为负数") });
            }
            if (num <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("每月活动数量必须大于0") });
            }
            if (maxnum <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("买家最大领取数量必须大于0") });
            }

            // 依次更新配置，任一失败则返回具体错误
            var flag = OperationConfig.UpdateValueByCode("VoucherPackagePrice", price.SafeString(), "代金券套餐价格");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到代金券套餐价格配置") });
            }
            flag = OperationConfig.UpdateValueByCode("VoucherActivityNum", num.SafeString(), "每月活动数量");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到每月活动数量配置") });
            }
            flag = OperationConfig.UpdateValueByCode("VoucherUserClaimMaxNum", maxnum.SafeString(), "买家最大领取数量");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到买家最大领取数量配置") });
            }

            // 清理缓存，确保新配置即时生效
            OperationConfig.Meta.Cache.Clear("", true);

            return Json(new DResult() { success = true, msg = GetResource("设置成功") });
        }


    }
}
