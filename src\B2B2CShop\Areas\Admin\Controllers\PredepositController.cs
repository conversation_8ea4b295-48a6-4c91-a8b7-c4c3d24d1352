﻿using B2B2CShop.Dto;
using B2B2CShop.Entity;

using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using MiniExcelLibs;
using MiniExcelLibs.OpenXml;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;
using System.Dynamic;

using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>预存款</summary>
[DisplayName("预存款")]
[Description("会员预存款管理")]
[AdminArea]
[DHMenu(60, ParentMenuName = "Members", CurrentMenuUrl = "~/{area}/Predeposit", CurrentMenuName = "Predeposit", CurrentIcon = "&#xe6e2;", LastUpdate = "20241210")]
public class PredepositController : PekCubeAdminControllerX {
    /// <summary>
    /// 充值明细列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("充值明细列表")]
    public IActionResult Index(string key, DateTime STime, DateTime ETime, int State, int page = 1)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            OrderBy = "CreateTime desc",
            RetrieveTotalCount = true
        };
        ViewBag.key = key;
        ViewBag.STime = (STime != DateTime.MinValue ? STime.ToString() : "");
        ViewBag.ETime = (ETime != DateTime.MinValue ? ETime.ToString() : "");
        if(ETime != DateTime.MinValue)
        {
            ETime = ETime.AddDays(1);
        }
        var list = PdRecharge.FindByTime(key, STime, ETime, State, pages);
        ViewBag.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "key", key }, { "STime", STime.ToString() }, { "ETime", ETime.ToString() } });
        return View(list);
    }

    /// <summary>
    /// 充值明细导出
    /// </summary>
    /// <param name="key"></param>
    /// <param name="STime"></param>
    /// <param name="ETime"></param>
    /// <param name="State"></param>
    /// <returns></returns>
    [DisplayName("充值明细导出")]
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpGet]
    public IActionResult ExportRechargeDetails(string key, DateTime STime, DateTime ETime, int State)
    {
        var pages = new PageParameter();
        var list = PdRecharge.FindByTime(key, STime, ETime, State, pages).Select(x => new
        {
            x.Id,
            x.UName,
            CreateTime = x.CreateTime.ToFullString(),
            PayTime = x.PayTime == DateTime.MinValue ? "-" : x.PayTime.ToString(),
            x.PCode,
            x.Amount,
            State = x.State ? "已支付" : "未支付"
        });

        var memoryStream = new MemoryStream();
        memoryStream.SaveAs(list, true, "充值明细", ExcelType.XLSX, new OpenXmlConfiguration() { AutoFilter = false });
        memoryStream.Seek(0, SeekOrigin.Begin);

        return File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"充值明细 {DateTime.Now:yyyyMMddhhmm}.xlsx");
    }

    /// <summary>
    /// Ixia
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    public IActionResult RechargeDetail(Int32 Id)
    {
        var model = PdRecharge.FindById(Id);
        if (model == null)
        {
            return View404();
        }
        return View(model);
    }

    /// <summary>
    /// 预存款调节
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("充值明细列表")]
    public IActionResult PdAdd(Int32 Id)
    {
        return View();
    }

    /// <summary>
    /// 线下汇款记录
    /// </summary>
    /// <param name="RName">账户名</param>
    /// <param name="State">状态</param>
    /// <param name="page"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("线下汇款记录")]
    public IActionResult Remittancelog(string RName, string State, Int32 page = 1)
    {
        dynamic viewModel = new ExpandoObject();

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            OrderBy = "CreateTime",
            Desc = true,
            RetrieveTotalCount = true
        };

        viewModel.RName = RName;
        viewModel.State = State;
        viewModel.List = Remittance.SearchByRName(RName, State, pages);
        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Remittancelog"), new Dictionary<String, String> { { "RName", RName }, { "State", State } });
        return View(viewModel);
    }

    /// <summary>
    /// 提现设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("提现设置")]
    public IActionResult PdcashSet()
    {
        // 模拟从数据库获取提现设置参数
        var model = new
        {
            WithdrawalInterval = 7,          // 模拟提现间隔周期为7天
            MinWithdrawalAmount = 100.00m,   // 模拟最低提现金额为100元
            MaxWithdrawalAmount = 10000.00m  // 模拟最大提现金额为10000元
        };

        return View(model);
    }

    /// <summary>
    /// 提现设置
    /// </summary>
    /// <param name="WithdrawalInterval">提现间隔周期（天）</param>
    /// <param name="MinWithdrawalAmount">最低提现金额（元）</param>
    /// <param name="MaxWithdrawalAmount">最大提现金额（元）</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("提现设置")]
    public IActionResult PdcashSet(string WithdrawalInterval, string MinWithdrawalAmount, string MaxWithdrawalAmount)
    {
        if (WithdrawalInterval.IsNullOrWhiteSpace() || MinWithdrawalAmount.IsNullOrWhiteSpace() || MaxWithdrawalAmount.IsNullOrWhiteSpace())
        {
            return MessageTip("所有参数均为必填项");
        }

        if (!WithdrawalInterval.IsInt() || !MinWithdrawalAmount.IsDecimal() || !MaxWithdrawalAmount.IsDecimal())
        {
            return MessageTip("请输入正确的参数格式");
        }

        var interval = WithdrawalInterval.ToInt();
        var minAmount = MinWithdrawalAmount.ToDecimal();
        var maxAmount = MaxWithdrawalAmount.ToDecimal();

        if (interval < 0 || minAmount < 0 || maxAmount < 0)
        {
            return MessageTip("参数不能为负数");
        }

        //var settings = PdcashSettings.FindAll().FirstOrDefault();

        //using (var tran = PdcashSettings.Meta.CreateTrans())
        {
            //if (settings == null)
            {
                // 如果没有现有设置，插入新的
                //settings = new PdcashSettings
                {
                    //WithdrawalInterval = interval,
                    //MinWithdrawalAmount = minAmount,
                    //MaxWithdrawalAmount = maxAmount
                };
                //settings.Insert();
            }
            //else
            {
                // 更新现有设置
                //settings.WithdrawalInterval = interval;
                //settings.MinWithdrawalAmount = minAmount;
                //settings.MaxWithdrawalAmount = maxAmount;
                //settings.Update();
            }
            //tran.Commit();
            // 清除缓存
            //PdcashSettings.Meta.Cache.Clear();
        }

        return MessageTip("提现设置已更新");
    }

    /// <summary>
    /// 线下汇款审核页面
    /// </summary>
    /// <param name="Id">线下汇款记录Id</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("线下汇款审核页面")]
    public IActionResult Audit(int Id)
    {
        var R = Remittance.FindById(Id);
        if (R == null)
        {
            return View404();
        }
        return View(R);
    }

    /// <summary>
    /// 线下汇款审核接口
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="State"></param>
    /// <param name="Remark"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    [DisplayName("线下汇款审核接口")]
    public IActionResult Audit(int Id, string State, string Remark)
    {
        var model = Remittance.FindById(Id);
        if (model == null)
        {
            return MessageTip(GetResource($"审核失败"));
        }
        if (State.IsNullOrWhiteSpace())
        {
            return MessageTip(GetResource($"审核结果不能为空"));
        }
        model.State = State.ToInt();

        var userDetail = UserDetail.FindById(model.CreateUserID);
        if (userDetail == null)
        {
            userDetail = new UserDetail
            {
                Id = model.CreateUserID
            };
            userDetail.Insert();
        }

        using (var tran = Remittance.Meta.CreateTrans())
        {
            if (State.ToInt() == 1)
            {
                userDetail.AvailablePredeposit += model.Amount;
                userDetail.Update();

                var pd = new PdLog
                {
                    UId = model.CreateUserID,
                    UName = UserE.FindByID(model.CreateUserID)?.Name ?? String.Empty,
                    PdType = "recharge",
                    Amount = model.Amount,
                    Balance = userDetail.AvailablePredeposit,
                    Desc = "线下汇款"
                };
                pd.Insert();
            }
            model.Remark = Remark;
            model.AuditDateTime = DateTime.Now;
            model.Update();
            tran.Commit();

            Remittance.Meta.Cache.Clear("");
            PdLog.Meta.Cache.Clear("");
        }
        return MessageTip(GetResource($"审核成功"));
    }

    /// <summary>
    /// 预存款明细
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("预存款明细")]
    public IActionResult PdLod(string mname, string stime, string etime, string aname, Int32 page = 1)
    {
        dynamic viewModel = new ExpandoObject();

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            OrderBy = "CreateTime",
            Desc = true,
            RetrieveTotalCount = true
        };

        viewModel.MName = mname;
        viewModel.STime = stime;
        viewModel.ETime = etime;
        viewModel.AName = aname;

        DateTime? endTime = null;
        if (DateTime.TryParse(etime, out DateTime parsedETime))
        {
            endTime = parsedETime.AddDays(1); // 加24小时，确保范围覆盖结束日期当天
        }

        viewModel.PdLogList = PdLog.Searchs(mname, stime, endTime.ToString()??"", aname, pages);

        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("PdLod"), new Dictionary<String, String> { { "mname", mname }, { "stime", stime }, { "etime", etime }, { "aname", aname } });

        return View(viewModel);
    }

    /// <summary>
    /// 预存款调节
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("预存款调节")]
    public IActionResult EditPred()
    {
        return View();
    }

    /// <summary>
    /// 调节预存款明细
    /// </summary>
    /// <param name="member_id">用户ID</param>
    /// <param name="member_name">用户名</param>
    /// <param name="operatetype">修改类型</param>
    /// <param name="amount">修改金额</param>
    /// <param name="lg_desc">备注</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("预存款调节")]
    [HttpPost]
    public IActionResult EditPred(int member_id, string member_name, int operatetype, string amount, string lg_desc)
    {

        lg_desc = lg_desc.SafeString().Trim();

        var pd = new PdLog();

        var userDetail = UserDetail.FindById(member_id);

        if (userDetail == null)
        {
            return MessageTip(GetResource($"会员信息错误"));
        }

        using (var tran1 = PdLog.Meta.CreateTrans())
        {
            pd.UId = member_id;
            pd.UName = member_name;

            if (!amount.IsFloat())
            {
                return MessageTip(GetResource($"请输入正确的金额"));
            }

            var money = decimal.Parse(amount);

            switch (operatetype)
            {
                case 1:
                    pd.PdType = "sys_add_money";
                    pd.Amount = money;
                    userDetail.AvailablePredeposit = userDetail.AvailablePredeposit + money;
                    break;
                case 2:
                    pd.PdType = "sys_del_money";
                    pd.Amount = money;
                    if (userDetail.AvailablePredeposit < money)
                    {
                        return MessageTip(GetResource($"预存款不足，会员当前预存款{userDetail.AvailablePredeposit}"));
                    }
                    userDetail.AvailablePredeposit = userDetail.AvailablePredeposit - money;
                    break;
                case 3:
                    pd.PdType = "cash_apply";
                    pd.Amount = money;
                    pd.FreezeAmount = money;
                    if (userDetail.AvailablePredeposit < money)
                    {
                        return MessageTip(GetResource($"冻结预存款不足，会员当前预存款{userDetail.AvailablePredeposit}"));
                    }
                    userDetail.AvailablePredeposit = userDetail.AvailablePredeposit - money;
                    userDetail.FreezePredeposit = userDetail.FreezePredeposit + money;
                    break;
                case 4:
                    pd.PdType = "cash_del";
                    pd.Amount = money;
                    pd.FreezeAmount = money;
                    if (userDetail.FreezePredeposit < money)
                    {
                        return MessageTip(GetResource($"可恢复冻结预存款不足，会员当前冻结预存款{userDetail.AvailablePredeposit}"));
                    }
                    userDetail.AvailablePredeposit = userDetail.AvailablePredeposit + money;
                    userDetail.FreezePredeposit = userDetail.FreezePredeposit - money;
                    break;
            }
            pd.Balance = userDetail.AvailablePredeposit;
            pd.Desc = lg_desc;

            pd.Save();
            userDetail.SaveAsync();

            tran1.Commit();
        }

        UserDetail.Meta.Cache.Clear("");
        PdLog.Meta.Cache.Clear("");

        return MessageTip(GetResource("调节成功"));
    }

    /// <summary>
    /// 根据用户名查询余额
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    [DisplayName("根据用户名查询余额")]
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpPost]
    public IActionResult Checkmember(String name)
    {
        var result = new DResult();

        name = name.Trim();

        if (name.IsNullOrEmpty())
        {
            result.msg = GetResource("会员名称不能为空");
            return Json(result);
        }

        var user = UserE.FindByName(name);

        if (user == null)
        {
            result.msg = GetResource("会员信息错误");
            return Json(result);
        }

        var userDetail = UserDetail.FindById(user.ID);

        if (userDetail == null)
        {
            userDetail = new UserDetail();
            userDetail.Id = user.ID;
            userDetail.Save();
            UserDetail.Meta.Cache.Clear("");
        }

        result.data = new { id = user.ID, name = user.Name, available_predeposit = userDetail.AvailablePredeposit, freeze_predeposit = userDetail.FreezePredeposit };

        result.success = true;

        return Json(result);

    }

    /// <summary>
    /// 记录导出
    /// </summary>
    /// <param name="mname"></param>
    /// <param name="stime"></param>
    /// <param name="etime"></param>
    /// <param name="aname"></param>
    /// <returns></returns>
    [DisplayName("预存款明细导出")]
    [EntityAuthorize(PermissionFlags.Detail)]
    [HttpGet]
    public IActionResult ExportPdLod(string mname, string stime, string etime, string aname)
    {
        var pages = new PageParameter();
        var List = PdLog.Searchs(mname, stime, etime, aname, pages).Select(x => new PdListExport { UName = x.UName, CreateTime = x.CreateTime.ToFullString(), Amount = x.Amount, FreezeAmount = x.FreezeAmount, Desc = x.Desc });

        var memoryStream = new MemoryStream();
        memoryStream.SaveAs(List, true, "预存款明细", ExcelType.XLSX, new OpenXmlConfiguration() { AutoFilter = false });
        memoryStream.Seek(0, SeekOrigin.Begin);

        return File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"预存款明细 {DateTime.Now:yyyyMMddhhmm}.xlsx");
    }
}
