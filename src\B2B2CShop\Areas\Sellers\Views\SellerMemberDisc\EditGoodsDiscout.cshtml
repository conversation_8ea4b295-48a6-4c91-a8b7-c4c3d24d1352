@using B2B2CShop.Entity
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "MemberDisc";
    PekHtml.AppendPageCssClassParts("html-sellermemberdisc-page");
    PekHtml.AppendTitleParts(T("设置商品会员等级折扣").Text);
    Goods goods = Model.goods;
}
@await Html.PartialAsync("_Left")

<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li><a href="@Url.Action("MemberDiscout")">@T("设置会员等级折扣")</a></li>
                <li><a href="@Url.Action("GoodsDiscout")">@T("设置商品会员等级折扣")</a></li>
            </ul>
        </div>
        <div class="p20">

            <div class="dssc-form-default">
                <form id="add_form" method="post" action="@Url.Action("SaveGoodsDiscout")">
                    <input type="hidden" name="goodsId" value="@goods.Id"/>
                    <dl>
                        <dt>@T("商品名称")</dt>
                        <dd>
                            @goods.Name
                        </dd>
                    </dl>
                    @foreach (GradeModel item in Model.membergrades)
                    {
                        var discount = goods.MgDiscountDto.FirstOrDefault(p => p.GradeId == item.level)?.Discount ?? 10;
                        <dl>
                            <dt><i class="required">*</i>@item.level_name</dt>
                            <dd>
                                <input class="w60 text" name="[@item.level].discount" type="text" value="@discount" maxlength="30" />
                                <span>@T("折")</span>
                                <p class="hint">@T("数值为0.1至10之间,设置为10表示不享受折扣")</p>
                            </dd>
                        </dl>
                    }
                    <div class="bottom">
                        <input id="submit_button" type="submit" class="submit" value='@T("提交")'>
                    </div>
                </form>
            </div>
            <script>
                $(function () {
                        //页面输入内容验证
                      $("#add_form").validate({
                          errorPlacement: function (error, element) {
                              var error_td = element.parent('dd').children('span');
                              error_td.append(error);
                          },
                          submitHandler: function (form) {
                              var $form = $("#add_form");
                              var $btn = $("#submit_button");
                              $btn.prop('disabled', true);
                              $.ajax({
                                  url: $form.attr('action'),
                                  type: 'POST',
                                  data: $form.serialize(),
                                  success: function (res) {
                                      if (res && res.success) {
                                          var ok = res.msg || '@T("设置成功")';
                                          layer.msg(ok, { icon: 1, time: 800 }, function () {
                                              window.location.href = '@Url.Action("GoodsDiscout")';
                                          });
                                      } else {
                                          var em = (res && res.msg) || '@T("设置失败")';
                                          if (window.layer) { layer.msg(em, { icon: 2 }); } else { alert(em); }
                                      }
                                  },
                                  error: function () {
                                      if (window.layer) { layer.msg('@T("网络错误")', { icon: 2 }); } else { alert('@T("网络错误")'); }
                                  },
                                  complete: function () { $btn.prop('disabled', false); }
                              });
                          }
                      });
                });
            </script>


        </div>
    </div>
</div>