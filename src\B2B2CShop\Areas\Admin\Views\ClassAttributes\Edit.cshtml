﻿@{
    var localizationSettings = LocalizationSettings.Current;
}
<style asp-location="true">
    .page {
        min-height: 415px
    }

    .layui-tab-brief > .layui-tab-title .layui-this {
        color: #419DFD !important;
    }

        .layui-tab-brief > .layui-tab-more li.layui-this:after,
        .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #419DFD !important;
        }

    .layui-tab-content {
        padding: 0px !important;
    }

    .bd {
        border: 2px solid red;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("商品分类属性")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index",new {ClassId=Model.ClassId})"><span>@T("管理")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("编辑")</span></a></li>
            </ul>
        </div>
    </div>

    <form id="goods_class_form" action="@Url.Action("Edit")" enctype="multipart/form-data" method="post">
        <input type="hidden" maxlength="20" value="@Model.ClassId" name="ClassId">
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准"):</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td colspan="2" class="">
                                    <label class="gc_name validation">@T("分类属性名称"):</label>
                                </td>
                                @* <td class="vatop tips"></td> *@
                            </tr>
                            <tr class="noborder">
                                <td colspan="2" class="vatop rowform">
                                    <input type="text" maxlength="45" value="@Model.Name" name="Name" class="txt">
                                </td>
                                @* <td class="vatop tips"></td> *@
                            </tr>
                        </tbody>
                    </table>
                </div>

                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        var ModelLan = ClassAttributesLan.FindByCIdAndLId(Model.Id, item.Id);
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td colspan="2" class="required">
                                            <label class="gc_name validation"
                                                   for="[@item.Id].Name">@T("分类属性名称"):</label>
                                        </td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="vatop rowform">
                                            <input type="text" value="@ModelLan.Name" name="[@item.Id].Name"
                                                   id="[@item.Id].Name" class="txt">
                                        </td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
        </div>
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td colspan="2" class="">
                        <label class="gc_name validation"
                               for="MappingField">@T("分类属性字段"):</label>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <select name="MappingField">
                            @foreach (var item in ViewBag.Filedlist)
                            {
                                <!option value="@item" @(item == Model.MappingField ? "selected" : "")>@item</!option>
                            }
                        </select>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tbody>
        </table>
    </form>
</div>
