﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}


<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("营销")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
        <li><a href="@Url.Action("Index")" ><span>@T("营销中心")</span></a></li>
        <li><a href="@Url.Action("Setting")" class="current"><span>@T("基本设置")</span></a></li>
    </ul>
        </div>
    </div>

    <form method="post" name="settingForm" id="settingForm">
        <table class="ds-default-table">
            <tbody>
            <!-- 促销开启 -->
            <tr class="noborder">
                <td colspan="2" class="required"><label>商品促销:</label></td>
            </tr>
            <tr class="noborder">
                <td class="vatop rowform onoff">
                    <label for="promotion_allow_1" class="cb-enable selected" title="开启"><span>开启</span></label>
                    <label for="promotion_allow_0" class="cb-disable " title="关闭"><span>关闭</span></label>
                    <input type="radio" id="promotion_allow_1" name="promotion_allow" value="1" checked=checked>
                    <input type="radio" id="promotion_allow_0" name="promotion_allow" value="0" >
                </td>
                <td class="vatop tips">启用商品促销功能后，商家可以通过秒杀、满即送、优惠套装和推荐展位活动，对店铺商品进行促销</td>
            </tr>
            <tr>
                <td colspan="2" class="required">抢购:</td>
            </tr>
            <tr class="noborder">
                <td class="vatop rowform onoff">
                    <label for="groupbuy_allow_1" class="cb-enable selected" title="开启"><span>开启</span></label>
                    <label for="groupbuy_allow_0" class="cb-disable " title="关闭"><span>关闭</span></label>
                    <input id="groupbuy_allow_1" name="groupbuy_allow"  checked=checked value="1" type="radio">
                    <input id="groupbuy_allow_0" name="groupbuy_allow"  value="0" type="radio">
                </td>
                <td class="vatop tips">抢购功能启用后，商家通过活动发布抢购商品，进行促销</td>
            </tr>

            <tr class="noborder">
                <td colspan="2" class="required"><label>积分:</label></td>
            </tr>
            <tr class="noborder">
                <td class="vatop rowform onoff">
                    <label for="points_isuse_1" class="cb-enable selected" ><span>开启</span></label>
                    <label for="points_isuse_0" class="cb-disable " ><span>关闭</span></label>
                    <input type="radio" id="points_isuse_1" name="points_isuse" value="1" checked=checked>
                    <input type="radio" id="points_isuse_0" name="points_isuse" value="0" >
                </td>
                <td class="vatop tips">积分系统启用后，可设置会员的注册、登录、购买商品送一定的积分</td>
            </tr>
            <tr>
                <td colspan="2" class="required">积分中心:</td>
            </tr>
            <tr class="noborder">
                <td class="vatop rowform onoff">
                    <label for="pointshop_isuse_1" class="cb-enable selected" title="开启"><span>开启</span></label>
                    <label for="pointshop_isuse_0" class="cb-disable " title="关闭"><span>关闭</span></label>
                    <input id="pointshop_isuse_1" name="pointshop_isuse"  checked=checked  value="1" type="radio">
                    <input id="pointshop_isuse_0" name="pointshop_isuse"  value="0" type="radio">
                </td>
                <td class="vatop tips">积分中心和积分同时启用后，网站将增加积分中心频道</td>
            </tr>
            <tr>
                <td colspan="2" class="required">积分兑换:</td>
            </tr>
            <tr class="noborder">
                <td class="vatop rowform onoff">
                    <label for="pointprod_isuse_1" class="cb-enable selected" title="开启"><span>开启</span></label>
                    <label for="pointprod_isuse_0" class="cb-disable " title="关闭"><span>关闭</span></label>
                    <input id="pointprod_isuse_1" name="pointprod_isuse"  checked=checked  value="1" type="radio">
                    <input id="pointprod_isuse_0" name="pointprod_isuse" } value="0" type="radio">
                </td>
                <td class="vatop tips">积分兑换、积分功能以及积分中心启用后，平台发布礼品，会员的积分在达到要求时可以在积分中心中兑换礼品</td>
            </tr>

            <tr>
                <td colspan="2" class="required">代金券:</td>
            </tr>
            <tr class="noborder">
                <td class="vatop rowform onoff">
                    <label for="voucher_allow_1" class="cb-enable selected" title="开启"><span>开启</span></label>
                    <label for="voucher_allow_0" class="cb-disable " title="关闭"><span>关闭</span></label>
                    <input id="voucher_allow_1" name="voucher_allow"  checked=checked  value="1" type="radio">
                    <input id="voucher_allow_0" name="voucher_allow"  value="0" type="radio">
                </td>
                <td class="vatop tips">代金券功能、积分功能、积分中心启用后，商家可以申请代金券活动；会员积分达到要求时可以在积分中心兑换代金券；<br>拥有代金券的会员可在代金券所属店铺内购买商品时，选择使用而得到优惠</td>
            </tr>
            
            <tr>
                <td colspan="2" class="required">会员等级折扣:</td>
            </tr>
            <tr class="noborder">
                <td class="vatop rowform onoff">
                    <label for="mgdiscount_allow_1" class="cb-enable selected" title="开启"><span>开启</span></label>
                    <label for="mgdiscount_allow_0" class="cb-disable " title="关闭"><span>关闭</span></label>
                    <input id="mgdiscount_allow_1" name="mgdiscount_allow"  checked=checked  value="1" type="radio">
                    <input id="mgdiscount_allow_0" name="mgdiscount_allow"  value="0" type="radio">
                </td>
                <td class="vatop tips">会员等级折扣启用后，商家可以申请会员等级折扣活动；会员等级达到要求时可以在购买商品中享受会员折扣</td>
            </tr>
            </tbody>
        </table>

        
        <table class="ds-default-table">
            <tfoot>
            <tr class="tfoot">
                <td colspan="2"><input class="btn" type="submit" value="提交"/></td>
            </tr>
            </tfoot>
        </table>
    </form>
</div>
<script type="text/javascript">
    
</script>
