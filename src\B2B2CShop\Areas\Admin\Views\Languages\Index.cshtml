﻿@{
    IList<LocaleStringResource> localeStringResources = Model.list;
}
<style asp-location="true">
    .opt_for {
        color: #aaa !important;
    }
    .btnAdd, .btnExport, .btnImport {
        background-color: #FF0000 !important;
        border: none !important;
        padding: 6px 12px !important;
        text-align: center !important;
        line-height: 1.5 !important;
        color: #FFFFFF !important;
    }
</style>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("语言包")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("AddLanguages")"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("翻译键")：</dt>
                <dd><input type="text" value="@Model.lanKey" name="lanKey" class="txt" ></dd>
            </dl>
            <dl>
                <dt>@T("语言")：</dt>
                <dd>
                    <select name="cultureId" id="cultureId">
                        <option value="0">@T("请选择")</option>
                        @foreach (var item in ViewBag.LanguageList)
                        {
                            <option value="@item.Value">@item.Text</option>
                        }
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("翻译值")：</dt>
                <dd><input type="text" value="@Model.lanValue" name="lanValue" class="txt"></dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
                <input type="submit" class="btnAdd" id="btnAdd" value="@T("新增")">
                <input type="submit" class="btnExport" id="btnExport" value="@T("导出")">
                <input type="submit" class="btnImport" id="btnImport" value="@T("导入")">
                <input type="file" id="fileInput" style="display: none;" />
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th class="w24"></th>
                <th>@T("翻译键")</th>
                <th>@T("语言")</th>
                <th>@T("翻译值")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in localeStringResources)
            {
                <tr id="<EMAIL>">
                    <td><input type="checkbox" class="checkitem" name="nav_id[]" value="@item.Id"></td>
                    <td style="width:auto;">@item.LanKey</td>
                    <td style="width:100px;">@item.Language?.DisplayName</td>
                    <td>@item.LanValue</td>
                    <td style="width:120px;">
                        <a href="@Url.Action("EditLanguages", new { Id = item.LanKey })" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { Ids = item.Id })','您确定要删除吗?',@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>

                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<script asp-location="Footer">
    function submit_delete(ids_str) {
        _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }
    // 新增逻辑
    $("#btnAdd").click(function () {
        window.location.href = "@Url.Action("AddLanguages")";
        return false
    });

    // 导出逻辑
    $("#btnExport").click(function () {
        // 获取筛选结果
        var cultureId = $("#cultureId").val();
        
        if (cultureId !== "0") {
            // 创建表单
            var form = $('<form></form>').attr('action', '@Url.Action("Export")').attr('method', 'GET');
            form.append($('<input>').attr('type', 'hidden').attr('name', 'cultureId').attr('value', cultureId));

            // 将表单添加到body并提交
            form.appendTo('body').submit().remove();
        } else {
            alert("@T("请选择语言后，再导出！ps：无需进行搜索")")
        }
        return false
    });
    
    // 导入逻辑
    $("#btnImport").click(function () {
        $("#fileInput").click();
        return false
    });
    $("#fileInput").change(function () {
        var file = this.files[0];
        if (!file) {
            return;
        }
        var formData = new FormData();
        formData.append("file", file);
        $.ajax({
            url: "@Url.Action("Import")",
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                if (response.success) {
                    alert("@T("导入成功")")
                    window.location.reload();
                } else {
                    alert(response.msg)
                }
            }
        });
    });
</script>
