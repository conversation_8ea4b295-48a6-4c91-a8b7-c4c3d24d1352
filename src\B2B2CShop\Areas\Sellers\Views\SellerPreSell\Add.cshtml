@using B2B2CShop.Entity
@using Pek.Timing
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "PreSell";
    PekHtml.AppendPageCssClassParts("html-sellerpresell-page");
    PekHtml.AppendTitleParts(T("新增预售").Text);
    PresellQuota quota = Model.quota;
}
@await Html.PartialAsync("_Left")

<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li><a href="@Url.Action("Index")">@T("预售活动列表")</a></li>
                <li class="current"><a href="@Url.Action("Add")">@T("新增预售")</a></li>
            </ul>
        </div>
        <div class="p20">

            <div class="dssc-form-default">
                <form id="add_form" method="post" action="@Url.Action("Add")">
                    <dl>
                        <dt><i class="required">*</i>@T("预售类型：")</dt>
                        <dd>
                            <ul class="dssc-form-radio-list">
                                <li><label onclick="$('*[dstype=type_2]').hide();$('*[dstype=type_1]').show();"><input
                                            type="radio" name="preselltype" checked="checked" value="1" />@T("全款预售")</label>
                                </li>
                                <li><label onclick="$('*[dstype=type_2]').show();$('*[dstype=type_1]').hide()"><input
                                            type="radio" name="preselltype" value="2" />@T("定金预售")</label></li>
                            </ul>
                            <p class="red">@T("预售类型不可修改")</p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("预售开始时间：") </dt>
                        <dd>
                            <input id="starttime" name="starttime" type="text" class="text w130" /><em
                                class="add-on"><i class="iconfont">&#xe8d6;</i></em>
                            <span></span>
                            <p class="hint">
                                @T("开始时间不能为空且不能早于当前时间") <br /><span class="red">@T("预售开始时间不可修改")</span>
                            </p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("预售结束时间：")</dt>
                        <dd>
                            <input id="endtime" name="endtime" type="text" class="text w130" /><em class="add-on"><i
                                    class="iconfont">&#xe8d6;</i></em><span></span>
                            <p class="hint">
                                @T("结束时间不能为空且不能晚于") @UnixTime.ToDateTime(quota.EndTime) <br /><span class="red">@T("预售结束时间不可修改")</span>

                            </p>
                        </dd>
                    </dl>

                    <dl>
                        <dt><i class="required">*</i>@T("预售商品：")</dt>
                        <dd>
                            <div dstype="presell_goods_info" class="selected-group-goods " style="display:none;">
                                <div class="goods-thumb"><img id="presell_goods_image" src="" /></div>
                                <div class="goods-name">
                                    <a dstype="presell_goods_href" id="presell_goods_name" href="" target="_blank"></a>
                                </div>
                                <div class="goods-price">@T("商城价：")<span dstype="presell_goods_price"></span></div>
                            </div>
                            <a href="javascript:void(0);" id="btn_show_search_goods"
                                class="dssc-btn dssc-btn-acidblue">@T("选择商品")</a>
                            <input id="goodsId" name="goodsId" type="hidden" value="" />
                            <input id="skuId" name="skuId" type="hidden" value="" />
                            <span></span>
                            <div id="div_search_goods" class="div-goods-select mt10" style="display: none;">
                                <table class="search-form">
                                    <tr>
                                        <th class="w150">
                                            <strong>@T("第一步：搜索店内商品")</strong>
                                        </th>
                                        <td class="w160">
                                            <input id="search_goods_name" type="text w150" class="text"
                                                name="goods_name" value="" />
                                        </td>
                                        <td class="w70 tc">
                                            <a href="javascript:void(0);" id="btn_search_goods" class="dssc-btn"><i
                                                class="iconfont">&#xe718;</i>@T("搜索")</a>
                                        </td>
                                        <td class="w10"></td>
                                        <td>
                                            <p class="hint">@T("不输入名称直接搜索将显示店内所有普通商品，特殊商品不能参加。")</p>
                                        </td>
                                    </tr>
                                </table>
                                <div id="div_goods_search_result" class="search-result" style="width:739px;"></div>
                                <a id="btn_hide_search_goods" class="close" href="javascript:void(0);">X</a>
                            </div>
                            <p class="hint">@T("选择参加预售对应的商品")</p>
                        </dd>
                    </dl>
                    <dl>
                        <dt><i class="required">*</i>@T("预售价：")</dt>
                        <dd>
                            <input id="presellprice" name="presellprice" type="text" class="text w130"
                                value="" />@T("元")<span></span>
                            <p class="hint"></p>
                        </dd>
                    </dl>

                    <dl dstype="type_2" style="display:none">
                        <dt><i class="required">*</i>@T("定金：")</dt>
                        <dd>
                            <input id="depositamount" name="depositamount" type="text"
                                class="text w130" value="" />@T("元")<span></span>
                            <p class="hint">@T("定金不可超过预售价的20%")</p>
                        </dd>
                    </dl>

                    <dl dstype="type_1">
                        <dt><i class="required">*</i>@T("发货时间：")</dt>
                        <dd>
                            <input id="shippingtime" name="shippingtime" type="text" class="text w130"
                                value="" /><em class="add-on"><i class="iconfont">&#xe8d6;</i></em><span></span>
                            <p class="hint"></p>
                        </dd>
                    </dl>

                    <div class="bottom">
                        <input id="submit_button" type="submit" class="submit" value='@T("提交")'>
                    </div>
                </form>
            </div>
            <link rel="stylesheet" href="/static/plugins/js/jquery-ui-timepicker/jquery-ui-timepicker-addon.min.css">
            <script src="/static/plugins/js/jquery-ui-timepicker/jquery-ui-timepicker-addon.min.js"></script>
            <script src="/static/plugins/js/jquery-ui-timepicker/i18n/jquery-ui-timepicker-zh-CN.js"></script>


        </div>
    </div>
</div>

<script type="text/javascript">
    $(function () {
        // 默认隐藏搜索框
        $("#div_search_goods").hide();

        // 展示搜索框
        $("#btn_show_search_goods").on('click', function () {
            $("#div_search_goods").show();
        })

        // 隐藏搜索框
        $("#btn_hide_search_goods").on('click', function () {
            $("#div_search_goods").hide();
        })

        // 点击搜索按钮进行搜索并展示商品内容
        $("#btn_search_goods").on('click', function (e) {
            e.preventDefault();
            var goodsName = $("#search_goods_name").val();
            console.log('goodsName', goodsName)

            // 通过AJAX加载SearchGoodsList视图
            $.ajax({
                url: '@Url.Action("SearchGoodsList")',
                type: 'POST',
                data: { keyword: goodsName },
                success: function (result) {
                    // 直接将返回的HTML内容显示在搜索结果区域
                    $("#div_goods_search_result").html(result);

                    // 重新绑定商品选择按钮的点击事件
                    $(document).on('click', 'a[dstype="btn_add_xianshi_goods"]', function () {
                        var goodsId = $(this).data('goods-id');
                        var skuId = $(this).data('goods-skuid');
                        var goodsName = $(this).closest('li').find('.goods-info dt').text();
                        var goodsPrice = $(this).closest('li').find('.goods-info dd').text().replace(/[^0-9.]/g, '');
                        var goodsImage = $(this).closest('li').find('.goods-thumb img').attr('src');

                        // 填充商品信息到表单
                        $('#goodsId').val(goodsId);
                        $('#skuId').val(skuId);
                        $('#presell_goods_name').text(goodsName);
                        $('[dstype="presell_goods_price"]').text(goodsPrice);
                        $('#presell_goods_image').attr('src', goodsImage);

                        // 显示已选择商品区域，隐藏搜索区域
                        $('[dstype="presell_goods_info"]').show();
                        $('#div_search_goods').hide();
                    });
                },
                error: function () {
                        $("#div_goods_search_result").html('<div class="error-result">' + '@T("搜索出错，请重试")' + '</div>');
                }
            });
        })
        var now = '@(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))';
        var enddata =  '@(quota != null ? UnixTime.ToDateTime(quota.EndTime).ToString("yyyy-MM-dd HH:mm:ss") : "")';
        // 预售开始时间
        $('#starttime').datetimepicker({
            dateFormat: 'yy-mm-dd',
            timeFormat: 'HH:mm:ss',
            showSecond: true,
            minDate: now,
            onSelect: function () {
                var start = $('#starttime').datetimepicker('getDate');
                if (start) {
                    // endtime 最小必须 >= start
                    $('#endtime').datetimepicker('option', 'minDate', start);
                    // shippingtime 也至少在 start 之后（可选）
                    $('#shippingtime').datetimepicker('option', 'minDate', start);
                }
            }
        });
         // 预售结束时间
        $('#endtime').datetimepicker({
            dateFormat: 'yy-mm-dd',
            minDate: now,            // 初始至少今天
            maxDate: enddata,   // 套餐到期不能超出
            onSelect: function () {
                var end = $('#endtime').datetimepicker('getDate');
                if (end) {
                    // 限制 starttime 最大不能超过 end
                    $('#starttime').datetimepicker('option', 'maxDate', end);
                    // 发货时间如果要求必须在结束之后，可将 shippingtime 最小值改为 end
                    $('#shippingtime').datetimepicker('option', 'minDate', end);
                }
            }
        });

        // 发货时间（若需至少在结束时间之后，可把 minDate 设置为 endtime 选择后的值，见上面 onSelect）
        $('#shippingtime').datetimepicker({
            dateFormat: 'yy-mm-dd',
            minDate: now
        });
        jQuery.validator.methods.greaterThanDate = function (value, element, param) {
            var date1 = new Date(Date.parse(param.replace(/-/g, "/")));
            var date2 = new Date(Date.parse(value.replace(/-/g, "/")));
            return date1 < date2;
        };

        jQuery.validator.methods.lessThanDate = function (value, element, param) {
            var date1 = new Date(Date.parse(param.replace(/-/g, "/")));
            var date2 = new Date(Date.parse(value.replace(/-/g, "/")));
            return date1 > date2;
        };

        jQuery.validator.methods.greaterThanStartDate = function (value, element) {
            var start_date = $("#starttime").val();
            var date1 = new Date(Date.parse(start_date.replace(/-/g, "/")));
            var date2 = new Date(Date.parse(value.replace(/-/g, "/")));
            return date1 < date2;
        };

        //页面输入内容验证
        $("#add_form").validate({
            errorPlacement: function (error, element) {
                var error_td = element.parent('dd').children('span');
                error_td.append(error);
            },
            onfocusout: false,
             submitHandler: function (form) {
                var $btn = $('#submit_button');
                $btn.prop('disabled', true);
                var postUrl = $('#add_form').attr('action');
                var payload = $(form).serialize();

                $.ajax({
                    url: postUrl,
                    type: 'POST',
                    data: payload,
                    dataType: 'json',
                    success: function (res) {
                        // 兼容不同项目返回格式
                        if (res.success) {
                            layer.msg(res.msg || '@T("添加成功")', { icon: 1, time: 800 }, function () {
                                window.location.href = '@Url.Action("Index")';
                            });
                        } else {
                            layer.msg(res.msg || '@T("添加失败")', { icon: 2, time: 1000 });
                        }
                    },
                    error: function (xhr) {
                        var err = '@T("请求出错，请稍后重试")';
                        layer.msg(err, { icon: 2, time: 1000 });
                    },
                    complete: function () { $btn.prop('disabled', false); }
                });
                return false;
             },
            rules: {
                starttime: {
                    required: true,
                    greaterThanDate: now
                },
                endtime: {
                    required: true,
                    lessThanDate: enddata,
                    greaterThanStartDate: true
                },
                presellprice:{
                    required: true,
                    number: true,
                    min: 0.01
                }
            },
            messages: {
                starttime: {
                    required: '<i class="iconfont">&#xe64c;</i>' + "@T("开始时间不能为空且不能早于当前时间")",
                    greaterThanDate: '<i class="iconfont">&#xe64c;</i>' + "@T("开始时间不能为空且不能早于当前时间")"
                },
                endtime: {
                    required: '<i class="iconfont">&#xe64c;</i>' + "@T("结束时间不能为空且不能晚于")"+enddata,
                    lessThanDate: '<i class="iconfont">&#xe64c;</i>' +  "@T("结束时间不能为空且不能晚于")"+enddata,
                    greaterThanStartDate: '<i class="iconfont">&#xe64c;</i>@T("结束时间必须大于开始时间")'
                },
                presellprice:{
                    required: '<i class="iconfont">&#xe64c;</i>' + "@T("预售价不能为空且必须为数字")",
                    number: '<i class="iconfont">&#xe64c;</i>' + "@T("预售价不能为空且必须为数字")",
                    min: '<i class="iconfont">&#xe64c;</i>' + "@T("预售价必须大于0.01元")"
                }

            }
        });
    })
</script>