﻿@model User
@{
    PekHtml.AppendScriptParts(ResourceLocation.Footer, "/static/plugins/mlselection.js");
    var userDetail = UserDetail.FindById(Model.ID);
    if (userDetail == null)
    {
        userDetail = new UserDetail();
        userDetail.Id = Model.ID;
        userDetail.CountryId = 1;
        userDetail.Save();
    }
    else
    {
        if (userDetail.CountryId == 0)
        {
            userDetail.CountryId = 1;
            userDetail.Save();
        }
    }

    var localizationSettings = LocalizationSettings.Current;
    IEnumerable<Role> roles = ViewBag.RoleList;
}
<style asp-location="true">
    .page {
        min-height: 415px
    }
</style>
<script asp-location="Head">
   var Cid = @userDetail.CountryId
</script>
<div class="page">
    <form id="user_form" enctype="multipart/form-data" method="post">
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120"><label>@T("会员名"):</label></td>
                    <td class="vatop rowform">@Model.Name</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="member_password">@T("密码"):</label></td>
                    <td class="vatop rowform"><input type="text" id="PassWord" name="PassWord" class="txt"></td>
                    <td class="vatop tips">@T("留空表示不修改密码")</td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="member_paypwd">@T("支付密码"):</label></td>
                    <td class="vatop rowform"><input type="text" id="PayPwd" name="PayPwd" class="txt"></td>
                    <td class="vatop tips">@T("留空表示不修改密码")</td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="member_email">@T("电子邮箱"):</label></td>
                    <td class="vatop rowform"><input type="text" value="@Model.Mail" id="Mail" name="Mail" class="txt"></td>
                    <td class="vatop tips">电子邮箱</td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="member_mobile">@T("手机号"):</label></td>
                    <td class="vatop rowform"><input type="text" value="@Model.Mobile" id="Mobile" name="Mobile" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="member_truename">@T("真实姓名"):</label></td>
                    <td class="vatop rowform"><input type="text" value="" id="TrueName" name="TrueName" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder" style="background: rgb(255, 255, 255);">
                    <td class="required"><label class="member_ww">@T("昵称"):</label></td>
                    <td class="vatop rowform"><input type="text" value="@Model.DisplayName" id="DisplayName" name="DisplayName" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("性别"):</label></td>
                    <td class="vatop rowform">
                        <label class="radio-label">
                            <i class="radio-common @(Model.Sex == SexKinds.未知 ? "selected" : "")">
                                <input type="radio" value="0" name="Sex" @(Model.Sex == SexKinds.未知 ? "checked=checked" : "")>
                            </i>
                            <span>@T("保密")</span>
                        </label>
                        <label class="radio-label">
                            <i class="radio-common ">
                                <input type="radio" value="1" name="Sex" @(Model.Sex == SexKinds.男 ? "checked=checked" : "")>
                            </i>
                            <span>@T("男")</span>
                        </label>
                        <label class="radio-label">
                            <i class="radio-common ">
                                <input type="radio" value="2" name="Sex" @(Model.Sex == SexKinds.女 ? "checked=checked" : "")>
                            </i>
                            <span>@T("女")</span>
                        </label>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="member_areainfo">@T("管理角色"):</label></td>
                    <td class="vatop rowform" colspan="2">
                        <span class="w400">
                            <select id="RoleID" name="RoleID">
                                @foreach (var item in roles)
                                {
                                    <!option value="@item.ID" @(Model.RoleID == item.ID ? "selected" : "")>@item.Name</!option>
                                }
                            </select>
                        </span>
                    </td>
                </tr>
                @if (localizationSettings.IsEnable)
                {
                    var CountryList = ViewBag.CountryList as IEnumerable<SelectListItem>;

                    <tr class="noborder">
                        <td class="required"><label class="member_areainfo">@T("所在国家"):</label></td>
                        <td class="vatop rowform" colspan="2">
                            <span class="w400">
                                <select id="Country" name="CountryId">
                                    @foreach (var item in CountryList!)
                                    {
                                        <!option value="@item.Value" @(userDetail.CountryId.ToString() == item.Value ? "selected" : "")>@item.Text</!option>
                                    }
                                </select>
                            </span>
                        </td>
                    </tr>
                }
                <tr class="noborder">
                    <td class="required"><label class="member_areainfo">@T("所在地区"):</label></td>
                    <td class="vatop rowform" colspan="2">
                        <span class="w400">
                            <input type="hidden" value="@userDetail.ProvinceId" name="ProvinceId" id="_area_1">
                            <input type="hidden" value="@userDetail.CityId" name="CityId" id="_area_2">
                            <input type="hidden" value="@userDetail.AreaId" name="AreaId" id="_area_3">
                            <input type="hidden" name="region" id="region" value="@userDetail.AreaInfo" />
                        </span>
                    </td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="member_qq">QQ:</label></td>
                    <td class="vatop rowform"><input type="text" value="@userDetail.QQ" id="QQ" name="QQ" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="member_ww">@T("阿里旺旺"):</label></td>
                    <td class="vatop rowform"><input type="text" value="@userDetail.WangWang" id="WangWang" name="WangWang" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="WeiXin">@T("微信"):</label></td>
                    <td class="vatop rowform"><input type="text" value="@userDetail.WeiXin" id="WeiXin" name="WeiXin" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="WhatsApp">@T("WhatsApp"):</label></td>
                    <td class="vatop rowform"><input type="text" value="@userDetail.WhatsApp" id="WhatsApp" name="WhatsApp" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label for="Skype">@T("Skype"):</label></td>
                    <td class="vatop rowform"><input type="text" value="@userDetail.Skype" id="Skype" name="Skype" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("实名认证"):</label></td>
                    <td class="vatop rowform">
                        <label class="radio-label">
                            <i class="radio-common selected">
                                <input type="radio" value="0" name="AuthState" @(userDetail.AuthState == 0 ? "checked=\"checked\"" : "")>
                            </i>
                            <span>@T("未认证")</span>
                        </label>
                        <label class="radio-label">
                            <i class="radio-common ">
                                <input type="radio" value="3" name="AuthState" @(userDetail.AuthState == 3 ? "checked=\"checked\"" : "")>
                            </i>
                            <span>@T("已认证")</span>
                        </label>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("举报商品"):</label></td>
                    <td class="vatop rowform onoff">
                        <label for="InformAllow1" class="cb-enable @(!userDetail.InformAllow ? "selected" : "")"><span>@T("允许")</span></label>
                        <label for="InformAllow2" class="cb-disable @(userDetail.InformAllow ? "selected" : "")"><span>@T("禁止")</span></label>
                        <input id="InformAllow1" name="InformAllow" @(!userDetail.InformAllow ? "checked=\"checked\"" : "") value="1" type="radio">
                        <input id="InformAllow2" name="InformAllow" @(userDetail.InformAllow ? "checked=\"checked\"" : "") value="2" type="radio">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("允许购买商品"):</label></td>
                    <td class="vatop rowform onoff">
                        <label for="IsBuy_1" class="cb-enable @(!userDetail.IsBuy ? "selected" : "")"><span>@T("允许")</span></label>
                        <label for="IsBuy_2" class="cb-disable @(userDetail.IsBuy ? "selected" : "")"><span>@T("禁止")</span></label>
                        <input id="IsBuy_1" name="IsBuy" @(!userDetail.IsBuy ? "checked=\"checked\"" : "") value="1" type="radio">
                        <input id="IsBuy_2" name="IsBuy" @(userDetail.IsBuy ? "checked=\"checked\"" : "") value="0" type="radio">
                    </td>
                    <td class="vatop tips">@T("如果禁止该项则会员不能在前台进行下单操作")</td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("允许发表言论"):</label></td>
                    <td class="vatop rowform onoff">
                        <label for="IsAllowTalk_1" class="cb-enable @(!userDetail.IsAllowTalk ? "selected" : "")"><span>@T("允许")</span></label>
                        <label for="IsAllowTalk_2" class="cb-disable @(userDetail.IsAllowTalk ? "selected" : "")"><span>@T("禁止")</span></label>
                        <input id="IsAllowTalk_1" name="IsAllowTalk" @(!userDetail.IsAllowTalk ? "checked=\"checked\"" : "") value="1" type="radio">
                        <input id="IsAllowTalk_2" name="IsAllowTalk" @(userDetail.IsAllowTalk ? "checked=\"checked\"" : "") value="0" type="radio">
                    </td>
                    <td class="vatop tips">@T("如果禁止该项则会员不能发表咨询和发送站内信")</td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("允许登录"):</label></td>
                    <td class="vatop rowform onoff">
                        <label for="memberstate_1" class="cb-enable @(Model.Enable ? "selected" : "")"><span>@T("允许")</span></label>
                        <label for="memberstate_2" class="cb-disable @(!Model.Enable ? "selected" : "")"><span>@T("禁止")</span></label>
                        <input id="memberstate_1" name="Enable" value="1" type="radio" @(Model.Enable ? "checked=\"checked\"" : "")>
                        <input id="memberstate_2" name="Enable" @(!Model.Enable ? "checked=\"checked\"" : "") value="0" type="radio">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("手机号码验证"):</label></td>
                    <td class="vatop rowform onoff">
                        <label for="MobileBind_1" class="cb-enable @(userDetail.MobileBind ? "selected" : "")"><span>@T("已验证")</span></label>
                        <label for="MobileBind_2" class="cb-disable @(!userDetail.MobileBind ? "selected" : "")"><span>@T("未验证")</span></label>
                        <input id="MobileBind_1" name="MobileBind" value="1" type="radio" @(userDetail.MobileBind ? "checked=\"checked\"" : "")>
                        <input id="MobileBind_2" name="MobileBind" @(!userDetail.MobileBind ? "checked=\"checked\"" : "") value="0" type="radio">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("邮箱验证"):</label></td>
                    <td class="vatop rowform onoff">
                        <label for="EmailBind_1" class="cb-enable @(userDetail.EmailBind ? "selected" : "")"><span>@T("已验证")</span></label>
                        <label for="EmailBind_2" class="cb-disable @(!userDetail.EmailBind ? "selected" : "")"><span>@T("未验证")</span></label>
                        <input id="EmailBind_1" name="EmailBind" value="1" type="radio" @(userDetail.EmailBind ? "checked=\"checked\"" : "")>
                        <input id="EmailBind_2" name="EmailBind" @(!userDetail.EmailBind ? "checked=\"checked\"" : "") value="0" type="radio">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("积分"):</label></td>
                    <td class="vatop rowform">@T("积分")&nbsp;<strong class="red">@userDetail.Points</strong>&nbsp;积分</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("经验值"):</label></td>
                    <td class="vatop rowform">@T("经验值")&nbsp;<strong class="red">@userDetail.ExpPoints</strong>&nbsp;积分</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("可用预存款"):</label></td>
                    <td class="vatop rowform">@T("可用")&nbsp;<strong class="red">@userDetail.AvailablePredeposit.ToString("#0.00")</strong>&nbsp;元</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label>@T("冻结预存款"):</label></td>
                    <td class="vatop rowform">@T("冻结")&nbsp;<strong class="red">@userDetail.FreezePredeposit.ToString("#0.00")</strong>&nbsp;元</td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="member_ww">@T("员工编号"):</label></td>
                    <td class="vatop rowform"><input type="text" value="@Model.Code" id="Code" name="Code" class="txt"></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="member_ww">@T("备注"):</label></td>
                    <td class="vatop rowform"><textarea name="Remark" id="Remark">@Model.Remark</textarea></td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
    <script type="text/javascript" asp-location="Footer">
        $(function () {
            //自定义手机号验证
            jQuery.validator.addMethod("isPhoneNum", function (value, element) {
                var length = value.length;
                var mobile = /^(13[0-9]{9})|(18[0-9]{9})|(14[0-9]{9})|(17[0-9]{9})|(15[0-9]{9})$/;
                return this.optional(element) || (length == 11 && mobile.test(value));
            }, "@T("请正确填写您的手机号码")");

            $("#region").ds_region({ Country: 'Country' });
            $('#user_form').validate({
                errorPlacement: function (error, element) {
                    error.appendTo(element.parent().parent().find('td:last'));
                },
                submitHandler:function(form){
                    // var pwdInput = $('#PassWord');
                    // pwdInput.val(generateHash2(pwdInput.val()))
                    form.submit();
                },
                rules: {
                    PassWord: {
                        maxlength: 33,
                        minlength: 6
                    },
                    DisplayName: {
                        minlength: 3,
                        maxlength: 30,
                        remote: {
                            url: '@Url.Action("VerifyDisplayName")',
                            type: 'get',
                            data: {
                                DisplayName: function () {
                                    return $('#DisplayName').val();
                                },
                                Id: '@Model.ID'
                            }
                        }
                    },
                    Mail: {
                        email: true,
                        remote: {
                            url: '@Url.Action("VerifyEmail")',
                            type: 'get',
                            data: {
                                Mail: function () {
                                    return $('#Mail').val();
                                },
                                Id: '@Model.ID'
                            }
                        }
                    },
                    QQ: {
                        digits: true,
                        minlength: 5,
                        maxlength: 11
                    },
                    Mobile: {
                         isPhoneNum: true,
                         remote: {
                            url: "@Url.Action("VerifyMobile")",
                            type: 'get',
                            data: {
                                Mobile: function () {
                                    return $('#Mobile').val();
                                },
                                Id: '@Model.ID'
                            }
                        }
                    }
                },
                messages: {
                    PassWord: {
                        maxlength: '@T("密码长度应在6-33个字符之间")',
                        minlength: '@T("密码长度应在6-33个字符之间")'
                    },
                    Mail: {
                        email: '@T("请您填写有效的电子邮箱")',
                        remote: '@T("邮件地址有重复，请您换一个")'
                    },
                    Mobile: {
                        isPhoneNum: '@T("请您填写有效的手机号码")',
                        remote: '@T("手机号码有重复，请您换一个")'
                    },
                    QQ: {
                        digits: '@T("请输入正确的QQ号码")',
                        minlength: '@T("请输入正确的QQ号码")',
                        maxlength: '@T("请输入正确的QQ号码")'
                    },
                    DisplayName: {
                        maxlength: '@T("昵称必须在3-30字符之间")',
                        minlength: '@T("昵称必须在3-30字符之间")',
                        remote: '@T("昵称有重复，请您换一个")'
                    }
                }
            });
        });
    </script>
</div>