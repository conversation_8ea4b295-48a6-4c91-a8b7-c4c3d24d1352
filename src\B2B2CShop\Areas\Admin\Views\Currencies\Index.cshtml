﻿<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("货币管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="/biz/Currencies" class="current"><span>@T("管理")</span></a></li>
                @* <li><a href="/biz/Currencies/AddCurrencies"><span>@T("添加")</span></a></li> *@
                <li><a href="javascript:dsLayerOpen('@Url.Action("AddCurrencies")','@T("新增")')"><span>@T("新增")</span></a></li>
            </ul>
        </div>
    </div>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th></th>
                <th>@T("货币ID")</th>
                <th>@T("货币名称")</th>
                <th>@T("货币编码")</th>
                <th>@T("左符号")</th>
                <th>@T("右符号")</th>
                <th>@T("小数位数")</th>
                <th>@T("汇率值")</th>
                <th>@T("默认货币")</th>
                <th>@T("状态")</th>
                <th>@T("图片标识")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (Currencies item in Model.list)
            {
                <tr>
                    <th><input type="checkbox" class="checkitem" value="@item.Id"></th>
                    <td>@item.Id</td>
                    <td>@item.Name</td>
                    <td>@item.Code</td>
                    <td>@item.SymbolLeft</td>
                    <td>@item.SymbolRight</td>
                    <td>@item.DecimalPlace</td>
                    <td>@item.ExchangeRate</td>
                    <td>
                        <div class="onoff" data-id="@item.Id">
                            <label for="DecimalValue1" onclick="DecimalValue1('@item.Id')" class="cb-enable @(item.DecimalValue?"selected":"")">是</label>
                            <label for="DecimalValue0" onclick="DecimalValue0('@item.Id')" class="cb-disable @(!item.DecimalValue?"selected":"")">否</label>
                            <input id="DecimalValue1" name="State1" value="true" data-id="@item.Id" type="radio" @(item.DecimalValue ? "checked" : "")>
                            <input id="DecimalValue0" name="State0" value="false" data-id="@item.Id" type="radio" @(!item.DecimalValue ? "checked" : "")>
                        </div>
                    </td>
                    <td>
                        <div class="onoff" data-id="@item.Id">
                            <label for="State1" onclick="State1('@item.Id')" class="cb-enable @(item.Status?"selected":"")">是</label>
                            <label for="State0" onclick="State0('@item.Id')" class="cb-disable @(!item.Status?"selected":"")">否</label>
                            <input id="State1" name="State1" value="true" data-id="@item.Id" type="radio" @(item.Status ? "checked" : "")>
                            <input id="State0" name="State0" value="false" data-id="@item.Id" type="radio" @(!item.Status ? "checked" : "")>
                        </div>
                    </td>
                    <td>
                        @if (item.ImgPath != null || item.ImgPath != "")
                        {
                            <a data-lightbox="lightbox-image" data-title="" href="@item.ImgPath" )">
                                <img src="@item.ImgPath" )" onload="javascript:ResizeImage(this,31,31);">
                            </a>
                        }
                    </td>
                    <td>
@*                         <a href="@Url.Action("Edit", new { Id= item.Id })" class="dsui-btn-edit">
                            <i class="iconfont"></i>@T("编辑")
                        </a> *@
                        <a href="javascript:dsLayerOpen('@Url.Action("Edit",new { Id=item.Id})','@item.Name')" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete")?ids=' + @item.Id + '','@T("您确定要删除吗?")')" class="dsui-btn-del">
                            <i class="iconfont"></i>@T("删除")
                        </a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small"
                                   onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>

<script asp-location="Footer">
    function submit_delete(items) {
        _uri = "@Url.Action("Delete")?ids=" + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
    }
    function State1(id) {
        changeCheckboxStatus(id,true)
    }
    function State0(id) {
        changeCheckboxStatus(id,false)
    }
                // 改变选择状态
    const changeCheckboxStatus = (Id,status)=>{
        $.post("@Url.Action("ModifyState")",{Id,status},function (res) {
            if (res.success) {
                if (status) {
                        layui.layer.msg('启用成功')
                    }else{
                        layui.layer.msg('已取消')
                    }
                    return;
                }
                layui.layer.msg(res.msg)
                debugger;
            }).fail(function(err) {
                console.log('选择失败！',err);
                // 在这里处理错误请求或者代码报错
               layui.layer.msg('选择失败！')
            });
        }

            function DecimalValue1(id) {
        changeCheckboxDecimalValue(id,true)
    }
    function DecimalValue0(id) {
           location.reload();
        //changeCheckboxDecimalValue(id,false)
    }

            const changeCheckboxDecimalValue = (Id,decimalValue)=>{
        $.post("@Url.Action("ModifyDecimalValue")",{Id,decimalValue},function (res) {
            if (res.success) {
                if (decimalValue) {
                        //layui.layer.msg('启用成功');
                        location.reload();
                    }else{
                        layui.layer.msg('已取消')
                    }
                    return;
                }
                layui.layer.msg(res.msg)
                debugger;
            }).fail(function(err) {
                console.log('选择失败！',err);
                // 在这里处理错误请求或者代码报错
               layui.layer.msg('选择失败！')
            });
        }
</script>

