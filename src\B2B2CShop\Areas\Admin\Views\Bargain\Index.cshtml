﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("砍价")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("活动列表")</span></a></li>
                <li><a href="@Url.Action("BargainPackage")" ><span>@T("套餐管理")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("BargainSetting")','@T("砍价设置")')"><span>@T("砍价设置")</span></a></li>
            </ul>
        </div>
    </div>
    
    <form method="get" name="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("砍价名称")</dt>
                <dd><input type="text" value="@Model.name" name="name" id="name" class="txt" style="width:100px;"></dd>
            </dl>
            <dl>
                <dt>@T("店铺名称")</dt>
                <dd>
                    <select id="storeId" name="storeId" class="form-control" style="width:200px;">
                        <!option value="">@T("全部")</!option>
                        @foreach (var store in Model.storelist)
                        {
                            <!option value="@store.Id" @(Model.storeId == store.Id ? "selected" : "")>@store.Name</!option>
                        }
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("状态")</dt>
                <dd>
                    <select name="status">
                        <option value="">@T("请选择")</option>
                        <!option value="0" @(Model.status == 0 ? "selected" : "")>@T("已取消")</!option>
                        <!option value="1" @(Model.status == 1 ? "selected" : "")>@T("待开始")</!option>
                        <!option value="2" @(Model.status == 2 ? "selected" : "")>@T("进行中")</!option>
                        <!option value="3" @(Model.status == 3 ? "selected" : "")>@T("已结束")</!option>
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <a href="javascript:document.formSearch.submit();" class="btn" title="@T("查询")">@T("查询")</a>
            </div>
        </div>
    </form>
    
    
    <!-- 帮助 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="@T("提示相关设置操作时应注意的要点")">@T("操作提示")</h4>
            <span id="explanationZoom" title="@T("收起提示")" class="arrow"></span>
        </div>
        <ul>
            <li>@T("砍价活动列表")</li>
            <li>@T("取消操作不可恢复，请慎重操作")</li>
            <li>@T("点击详细链接查看活动详细信息")</li>
        </ul>
    </div>
    
    <!-- 列表 -->
    <form id="list_form" method="post">
        <input type="hidden" id="object_id" name="object_id"/>
        <table class="ds-default-table">
            <thead>
            <tr class="thead">
                <th class="align-left"><span>@T("砍价名称")</span></th>
                <th class="align-left"><span>@T("商品名称")</span></th>
                <th class="align-left" width="240"><span>@T("店铺名称")</span></th>
                <th class="align-center" width="120"><span>@T("开始时间")</span></th>
                <th class="align-center" width="120"><span>@T("结束时间")</span></th>
                <th class="align-center" width="120"><span>@T("商品底价")</span></th>
                <th class="align-center" width="120"><span>@T("限购数量")</span></th>
                <th class="align-center" width="80"><span>@T("状态")</span></th>
                <th class="w120 align-center"><span>@T("操作")</span></th>
            </tr>
            </thead>
            <tbody id="treet1">
                @foreach (Bargain item in Model.list)
                {
                    <tr class="hover">
                        <td><span>@item.Name</span></td>
                        <td><a href="" target="_blank">@item.BargainGoodsName</a></td>
                        <td>
                            <a href="/index.php/home/<USER>/index.html?store_id=1"><span>@item.StoreName</span></a>
                        </td>
                        <td><span>@(UnixTime.ToDateTime(item.StartTime))</span></td>
                        <td><span>@(UnixTime.ToDateTime(item.EndTime))</span></td>
                        <td><span>@item.FloorPrice</span></td>
                        <td><span>@item.LimitQuantity</span></td>
                        <td><span>
                                @if (UnixTime.ToTimestamp() > item.EndTime)
                                {
                                    @T("已结束")
                                }
                                else
                                {
                                    @(item.State switch{
                                        0 => T("已取消"),
                                        1 => T("待开始"),
                                        2 => T("进行中"),
                                        3 => T("已结束"),
                                        _ => T("未知"),
                                    })
                                }
                            </span>
                        </td>
                        <td class="nowrap align-center">
                            <a href="javascript:dsLayerOpen('@Url.Action("BargainDetail",new {bargainId = item.Id})','@(T("砍价详情")+"-"+item.Name)')" class="dsui-btn-view"><i class="iconfont"></i>@T("详细")</a>
                            @if (UnixTime.ToTimestamp() > item.EndTime || item.State == 0 || item.State == 3)
                            {
                                <a href="javascript:dsLayerConfirm('@Url.Action("DelBargain",new {id = item.Id})','@T("您确定要删除吗")?')" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                            }
                            else
                            {
                                <a href="javascript:dsLayerConfirm('@Url.Action("CancelBargain",new {id = item.Id})','@T("您确定要取消吗")?')" class="dsui-btn-del"><i class="iconfont"></i>@T("取消")</a>
                            }
                        </td>
                    </tr>
                }
            </tbody>
        </table>
        <ul class="pagination">
            @Html.Raw(Model.PageHtml)
        </ul>
            </form>

</div>
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>
<script type="text/javascript">
    $(function() {
        $('#storeId').select2({
            placeholder: '@T("请选择店铺")',
            allowClear: true,
        });
    });
</script>