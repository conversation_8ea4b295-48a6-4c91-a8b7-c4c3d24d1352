﻿using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using iTextSharp.text.pdf.parser.clipper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.Recommendations;
using MimeKit.Tnef;
using NewLife.Data;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Timing;
using System.ComponentModel;
using System.Dynamic;
using System.Threading;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("平台代金券")]
    [Description("平台代金券管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/PlatformVoucher", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/PlatformVoucher", CurrentMenuName = "PlatformVoucherList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class PlatformVoucherController : PekCubeAdminControllerX
    {
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("平台代金券管理")]   
        public IActionResult Index(int page,int limit)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };    
            viewModel.list = MallVoucherTemplate.Search("",pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string>());

            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("添加平台代金券")]
        public IActionResult AddVoucher()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.goodsclasslist = GoodsClass.FindAllByParentId(0).Select(x => new GoodsClass
            {
                Id = x.Id,
                Name = GoodsClassLan.FindByGIdAndLId(x.Id, WorkingLanguage.Id)?.RealName ?? x.Name,
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("添加平台代金券")]
        [HttpPost]
        public IActionResult AddVoucher(string title,string desc,long gcId,decimal price,DateTime startTime, DateTime endTime,decimal limit,int points,int quantity, int eachLimit)
        {
            if (title.IsNullOrWhiteSpace())
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券标题") });
            if (desc.IsNullOrWhiteSpace())
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券描述内容") });
            if (gcId < 0)
                return Json(new DResult() { success = false, msg = GetResource("请选择商品分类") });
            if (price <= 0)
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券面额") });
            if (startTime <= DateTime.MinValue)
                return Json(new DResult() { success = false, msg = GetResource("请选择代金券开始时间") });
            if (endTime <= DateTime.MinValue)
                return Json(new DResult() { success = false, msg = GetResource("请选择代金券结束时间") });
            if (limit < 0)
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券消费金额") });
            if (points < 0)
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券领取所需积分") });
            if (quantity <= 0)
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券发放数量") });
            if (eachLimit < 0)
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券限领数量") });
            var gc = GoodsClass.FindById(gcId);
            var entity = new MallVoucherTemplate();
            entity.Title = title;
            entity.Description = desc;
            entity.Price = price;
            entity.GcId = gcId;
            entity.GcIdArr = (gc?.ParentIdList).IsNullOrEmpty()?"": $",{gc.ParentIdList},";
            entity.StartDate = UnixTime.ToTimestamp(startTime);
            entity.EndDate = UnixTime.ToTimestamp(endTime);
            entity.Limit = limit;
            entity.Points = points;
            entity.Quantity = quantity;
            entity.EachLimit= eachLimit;
            entity.Insert();
            //多语言
            if (LocalizationSettings.Current.IsEnable)
            {
                var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                foreach (var item in LanguageList)
                {
                    var lan = new MallVoucherTemplateLan();
                    lan.VId = entity.Id;
                    lan.LId = item.Id;
                    lan.Title = (GetRequest($"[{item.Id}].title")).SafeString().Trim();
                    lan.Description = (GetRequest($"[{item.Id}].desc")).SafeString().Trim();
                    lan.Insert();
                }
            }
            return Json(new DResult() { success = true, msg = GetResource("新增成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑平台代金券")]
        public IActionResult EditVoucher(long id)
        {
            var entity = MallVoucherTemplate.FindById(id);
            if (entity == null)
                return Content(GetResource("代金券不存在"));
            dynamic viewModel = new ExpandoObject();
            viewModel.goodsclasslist = GoodsClass.FindAllByParentId(0).Select(x => new GoodsClass
            {
                Id = x.Id,
                Name = GoodsClassLan.FindByGIdAndLId(x.Id, WorkingLanguage.Id)?.RealName ?? x.Name,
            });
            viewModel.entity = entity;
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑平台代金券")]
        [HttpPost]
        public IActionResult EditVoucher(long id, string title,string desc,long gcId,decimal price, DateTime startTime, DateTime endTime,decimal limit,int points,int quantity, int eachLimit)
        {
            var entity = MallVoucherTemplate.FindById(id);
            if (entity == null)
                return Json(new DResult() { success = false, msg = GetResource("代金券不存在") });
            if (title.IsNullOrWhiteSpace())
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券标题") });
            if (desc.IsNullOrWhiteSpace())
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券描述内容") });
            if (gcId < 0)
                return Json(new DResult() { success = false, msg = GetResource("请选择商品分类") });
            if (price <= 0)
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券面额") });
            if (startTime <=  DateTime.MinValue)
                return Json(new DResult() { success = false, msg = GetResource("请选择代金券开始时间") });
            if (endTime <= DateTime.MinValue)
                return Json(new DResult() { success = false, msg = GetResource("请选择代金券结束时间") });
            if (limit < 0)
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券消费金额") });
            if (points < 0)
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券领取所需积分") });
            if (quantity <= 0)
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券发放数量") });
            if (eachLimit < 0)
                return Json(new DResult() { success = false, msg = GetResource("请输入代金券限领数量") });
            var gc = GoodsClass.FindById(gcId);
            entity.Title = title;
            entity.Description = desc;
            entity.Price = price;
            entity.GcId = gcId;
            entity.GcIdArr = (gc?.ParentIdList).IsNullOrEmpty()?"": $",{gc.ParentIdList},";
            entity.StartDate = UnixTime.ToTimestamp(startTime);
            entity.EndDate = UnixTime.ToTimestamp(endTime);
            entity.Limit = limit;
            entity.Points = points;
            entity.Quantity = quantity;
            entity.EachLimit= eachLimit;
            entity.Update();
            //多语言
            if (LocalizationSettings.Current.IsEnable)
            {
                var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                foreach (var item in LanguageList)
                {
                    var lan = MallVoucherTemplateLan.FindByVIdAndLId(entity.Id,item.Id);
                    if (lan==null)
                    {
                        lan = new MallVoucherTemplateLan();
                        lan.VId = entity.Id;
                        lan.LId = item.Id;
                    }
                    lan.Title = (GetRequest($"[{item.Id}].title")).SafeString().Trim();
                    lan.Description = (GetRequest($"[{item.Id}].desc")).SafeString().Trim();
                    lan.Save();
                }
            }
            return Json(new DResult() { success = true, msg = GetResource("编辑成功") });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除平台代金券")]
        public IActionResult DeleteVoucher(long id)
        {
            if (id <= 0)
                return Json(new DResult() { success = false, msg = GetResource("请选择要删除的代金券") });
            //验证是否领取过
            if (MallVoucherUser.GetCountByTemplateId(id)>0)
                return Json(new DResult() { success = false, msg = GetResource("该代金券已被领取，不能删除") });

            MallVoucherTemplate.Delete(MallVoucherTemplate._.Id == id);
            //删除多语言
            MallVoucherTemplateLan.Delete(MallVoucherTemplateLan._.VId == id);
            MallVoucherTemplate.Meta.Cache.Clear("", true);
            return Json(new DResult() { success = true, msg = GetResource("删除成功") });
        }


        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("平台代金券详情")]
        public IActionResult VoucherDetail(long id)
        {
            var entity = MallVoucherTemplate.FindById(id);
            if (entity == null)
                return Content(GetResource("代金券不存在"));
            return View(entity);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("代金券领取记录")]
        public IActionResult VoucherReceiveRecord(long id)
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.list = MallVoucherUser.FindAllByTemplateId(id);
            return View(viewModel);
        }


        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("检索商品分类")]
        public IActionResult SearchGoodsClass(Int64 pId)
        {
            var res = new DResult();
            if (pId == 0)
            {
                res.success = false;
                res.msg = "未检索到信息";
            }
            res.data = GoodsClass.FindAllByParentId(pId).Select(x => new
            {
                Id = x.Id.SafeString(),
                Name = GoodsClassLan.FindByGIdAndLId(x.Id, WorkingLanguage.Id)?.RealName ?? x.Name,
            });
            res.success = true;
            res.msg = "成功";
            return Json(res);
        }
    }
}
