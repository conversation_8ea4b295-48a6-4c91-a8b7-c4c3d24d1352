﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/integral.css");
}
<!-- 积分中心代金券 -->
<div class="integral">
    <div class="vinfo">
        <div class="avatarBox">
            <img class="avatar" src="/public/images/icons/morentouxiang.png" alt="">
        </div>
        <span>137*****5694</span>
        <div class="textBox">
            <div class="text">
                <p>可用积分</p>
                <p class="num">34</p>
            </div>
            <div class="text">
                <p>余额</p>
                <p class="num">0.00</p>
            </div>
        </div>
    </div>
</div>
<div class="voucherBox">
    @* 平台代金卷 *@
    <div class="voucher">
        <div class="blueBox">
            <image src="/public/images/pics/<EMAIL>" />
            <span>平台代金卷</span>
        </div>
        <div class="more">更多 >> </div>
    </div>
    <div class="threeBox">
        <div class="voucherBg">
            <div class="voucherBg-left">
                <p class="p1">￥ <span class="s1">3</span> </p>
                <p class="p2">满30元可用</p>
                <p>需0积分</p>
            </div>
            <div class="voucherBg-right">
                <p>立</p>
                <p>即</p>
                <p>兑</p>
                <p>换</p>
            </div>
        </div>
        <div class="voucherBg">
        <div class="voucherBg-left">
            <p class="p1">￥ <span class="s1">3</span> </p>
            <p class="p2">满30元可用</p>
            <p>需0积分</p>
        </div>
        <div class="voucherBg-right">
            <p>立</p>
            <p>即</p>
            <p>兑</p>
            <p>换</p>
        </div>
    </div>
    <div class="voucherBg">
        <div class="voucherBg-left">
            <p class="p1">￥ <span class="s1">3</span> </p>
            <p class="p2">满30元可用</p>
            <p>需0积分</p>
        </div>
        <div class="voucherBg-right">
            <p>立</p>
            <p>即</p>
            <p>兑</p>
            <p>换</p>
        </div>
    </div>
    <div class="voucherBg">
        <div class="voucherBg-left">
            <p class="p1">￥ <span class="s1">3</span> </p>
            <p class="p2">满30元可用</p>
            <p>需0积分</p>
        </div>
        <div class="voucherBg-right">
            <p>立</p>
            <p>即</p>
            <p>兑</p>
            <p>换</p>
        </div>
    </div>
    </div>

    @* 热门店铺代金券 *@
    <div class="voucher">
        <div class="blueBox">
            <image src="/public/images/pics/<EMAIL>" />
            <span>热门店铺代金券</span>
        </div>
        <div class="more">更多 >> </div>
    </div>
    <div class="threeBox">
        <div class="voucherBg">
            <div class="voucherBg-left hot">
                <img class="shop-avatar" src="/public/images/icons/morentouxiang.png" alt="">
                <div>
                    <p class="p1">￥ <span class="s1">3</span> </p>
                    <p class="p2">满30元可用</p>
                    <p>需0积分</p>
                </div>
            </div>
            <div class="voucherBg-right">
                <p>立</p>
                <p>即</p>
                <p>兑</p>
                <p>换</p>
            </div>
        </div>
        <div class="voucherBg">
            <div class="voucherBg-left hot">
                <img class="shop-avatar" src="/public/images/icons/morentouxiang.png" alt="">
                <div>
                    <p class="p1">￥ <span class="s1">3</span> </p>
                    <p class="p2">满30元可用</p>
                    <p>需0积分</p>
                </div>
            </div>
            <div class="voucherBg-right">
                <p>立</p>
                <p>即</p>
                <p>兑</p>
                <p>换</p>
            </div>
        </div>
        <div class="voucherBg">
            <div class="voucherBg-left hot">
                <img class="shop-avatar" src="/public/images/icons/morentouxiang.png" alt="">
                <div>
                    <p class="p1">￥ <span class="s1">3</span> </p>
                    <p class="p2">满30元可用</p>
                    <p>需0积分</p>
                </div>
            </div>
            <div class="voucherBg-right">
                <p>立</p>
                <p>即</p>
                <p>兑</p>
                <p>换</p>
            </div>
        </div>
        <div class="voucherBg">
            <div class="voucherBg-left hot">
                <img class="shop-avatar" src="/public/images/icons/morentouxiang.png" alt="">
                <div>
                    <p class="p1">￥ <span class="s1">3</span> </p>
                    <p class="p2">满30元可用</p>
                    <p>需0积分</p>
                </div>
            </div>
            <div class="voucherBg-right">
                <p>立</p>
                <p>即</p>
                <p>兑</p>
                <p>换</p>
            </div>
        </div>
    </div>

</div>
<div class="bug"></div>
