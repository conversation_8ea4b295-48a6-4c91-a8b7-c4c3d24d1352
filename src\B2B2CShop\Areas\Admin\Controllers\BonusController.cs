﻿using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.CodeAnalysis;
using MimeKit.Tnef;
using NewLife.Data;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Timing;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{

    [DisplayName("吸粉红包")]
    [Description("吸粉红包管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/Bonus", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/Bonus", CurrentMenuName = "BonusList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class BonusController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("红包列表")]
        public IActionResult Index(string name,int type ,int state,int page = 1,int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.list = Bonus.Search(name, type, state, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {
                { "name", name },
                { "type", type.SafeString() },
                { "state", state.SafeString() },
            });
            viewModel.name = name;
            viewModel.type = type;
            viewModel.state = state;
            return View(viewModel);
        }


        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增红包")]
        public IActionResult AddBonus()
        {
            return View();
        }

        /// <summary>
        /// 新增红包
        /// </summary>
        /// <param name="bonus_name">红包名称</param>
        /// <param name="bonus_blessing">祝福语</param>
        /// <param name="bonus_remark">备注</param>
        /// <param name="bonus_type">红包类型</param>
        /// <param name="bonus_totalprice">总金额</param>
        /// <param name="bonus_pricetype">价格类型</param>
        /// <param name="bonus_fixedprice">固定金额</param>
        /// <param name="bonus_randomprice_start">随机金额开始</param>
        /// <param name="bonus_randomprice_end">随机金额结束</param>
        /// <param name="bonus_begintime">开始时间</param>
        /// <param name="bonus_endtime">结束时间</param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("新增红包")]
        [HttpPost]
        public IActionResult AddBonus(string bonus_name, string bonus_blessing,string bonus_remark,int bonus_type,decimal bonus_totalprice,int bonus_pricetype ,decimal bonus_fixedprice,decimal bonus_randomprice_start,decimal bonus_randomprice_end,DateTime bonus_begintime,DateTime bonus_endtime)
        {
            if (string.IsNullOrWhiteSpace(bonus_name))
            {
                return Json(new DResult() { success = false, msg = GetResource("红包名称不能为空") });
            }
            if (bonus_type == 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("请选择红包类型") });
            }
            if (bonus_totalprice <= 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("总金额必须大于0") });
            }
            if (bonus_pricetype == 1)
            {
                if (bonus_fixedprice <= 0)
                {
                    return Json(new DResult() { success = false, msg = GetResource("固定金额必须大于0") });
                }
                if (bonus_totalprice % bonus_fixedprice!=0)
                {
                    return Json(new DResult() { success = false, msg = GetResource("固定金额或红包总面额设置错误") });
                }
            }
            if (bonus_pricetype == 2 && (bonus_randomprice_start <= 0 || bonus_randomprice_end <= 0 || bonus_randomprice_start >= bonus_randomprice_end))
            {
                return Json(new DResult() { success = false, msg = GetResource("随机金额范围不正确") });
            }
            if (bonus_begintime >= bonus_endtime)
            {
                return Json(new DResult() { success = false, msg = GetResource("开始时间必须小于结束时间") });
            }
            var bonus = new Bonus
            {
                Name = bonus_name,
                Blessing = bonus_blessing,
                Remark = bonus_remark,
                Type = bonus_type,
                TotalPrice = bonus_totalprice,
                PriceType = bonus_pricetype,
                FixedPrice = bonus_fixedprice,
                RandomPriceStart = bonus_randomprice_start,
                RandomPriceEnd = bonus_randomprice_end,
                BeginTime = UnixTime.ToTimestamp(bonus_begintime),
                EndTime = UnixTime.ToTimestamp(bonus_endtime),
                State = 1,
            };
            bonus.Insert();
            //添加翻译表
            if (LocalizationSettings.Current.IsEnable)
            {
                var languageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                foreach (var lang in languageList)
                {
                    // 支持多语言表单，未填则回落到主名称
                    var name = (GetRequest($"[{lang.Id}].bonus_name")).SafeString().Trim();
                    var blessing = (GetRequest($"[{lang.Id}].bonus_blessing")).SafeString().Trim();
                    var remark = (GetRequest($"[{lang.Id}].bonus_remark")).SafeString().Trim();

                    var lan = new BonusLan
                    {
                        BId = bonus.Id,
                        LId = lang.Id,
                        Name = name,
                        Blessing = blessing,
                        Remark = remark
                    };
                    lan.Insert();
                }
            }
            return Json(new DResult() { success = true, msg = GetResource("新增红包成功") });

        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑红包")]
        public IActionResult EditBonus(long id)
        {
            var entity = Bonus.FindById(id);    
            if (entity == null)return Prompt(new PromptModel() { Message = GetResource("找不到红包记录") });
            return View(entity);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("编辑红包")]
        [HttpPost]
        public IActionResult EditBonus(long id, string bonus_name, string bonus_blessing, string bonus_remark, DateTime bonus_begintime, DateTime bonus_endtime)
        {
            var entity = Bonus.FindById(id);
            if (entity == null)
                return Json(new DResult() { success = false, msg = GetResource("找不到红包记录") });

            if (string.IsNullOrWhiteSpace(bonus_name))
                return Json(new DResult() { success = false, msg = GetResource("红包名称不能为空") });
            if (bonus_begintime >= bonus_endtime)
                return Json(new DResult() { success = false, msg = GetResource("开始时间必须小于结束时间") });

            entity.Name = bonus_name;
            entity.Blessing = bonus_blessing;
            entity.Remark = bonus_remark;
            entity.BeginTime = UnixTime.ToTimestamp(bonus_begintime);
            entity.EndTime = UnixTime.ToTimestamp(bonus_endtime);

            entity.Update();

            // 更新翻译表
            if (LocalizationSettings.Current.IsEnable)
            {
                var languageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                foreach (var lang in languageList)
                {
                    var name = (GetRequest($"[{lang.Id}].bonus_name")).SafeString().Trim();
                    var blessing = (GetRequest($"[{lang.Id}].bonus_blessing")).SafeString().Trim();
                    var remark = (GetRequest($"[{lang.Id}].bonus_remark")).SafeString().Trim();

                    var lan = BonusLan.FindByBIdAndLId(entity.Id, lang.Id) ?? new BonusLan
                    {
                        BId = entity.Id,
                        LId = lang.Id
                    };
                    lan.Name = name;
                    lan.Blessing = blessing;
                    lan.Remark = remark;

                    if (lan.Id > 0) lan.Update();
                    else lan.Insert();
                }
            }

            return Json(new DResult() { success = true, msg = GetResource("编辑成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("红包详情")]
        public IActionResult BonusDetail(long id,int page = 1 ,int limit = 10)
        {
            var entity = Bonus.FindById(id);
            if (entity == null) return Prompt(new PromptModel() { Message = GetResource("找不到红包记录") });
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            dynamic viewModel= new ExpandoObject();
            viewModel.list = BonusReceive.FindAllByBonusId(id);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("BonusDetail"), new Dictionary<string, string> {
            });
            viewModel.entity = entity;
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("提前失效")]
        public IActionResult InvalidBonus(long id)
        {
            var entity = Bonus.FindById(id);
            if (entity == null) return Json(new DResult() { success = false, msg = GetResource("找不到红包记录") });
            entity.State = 3;
            entity.Update();    
            Bonus.Meta.Cache.Clear("", true);
            return Json(new DResult() { success = true, msg = GetResource("操作成功") });
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("红包链接地址")]
        public IActionResult BonusLink()
        {
            return View();
        }
    }
}
