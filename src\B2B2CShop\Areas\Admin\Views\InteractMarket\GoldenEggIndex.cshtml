@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("砸金蛋")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("GoldenEggIndex")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("Add", new { type = 3 })','@T("添加")')"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>

    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("活动名称")</dt>
                <dd><input type="text" value="@Model.name" name="name" class="txt"></dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value='@T("搜索")'>
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th>@T("活动名称")</th>
                <th>@T("参与类型")</th>
                <th>@T("活动时间")</th>
                <th>@T("共参与次数")</th>
                <th>@T("共中奖次数")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (MarketActivityManage item in Model.list)
            {
                <tr id="<EMAIL>">
                    <td>@item.Name</td>
                    <td>@(item.JoinType == 0 ? (T("共") + item.JoinCount.SafeString() + T("次")) : item.JoinType == 1 ? (T("每天") + item.JoinCount.SafeString() + T("次")) : T("无限制"))</td>
                    <td>@(UnixTime.ToDateTime(item.BeginTime) + "~" + UnixTime.ToDateTime(item.EndTime))</td>
                    <td>@item.TotalCount</td>
                    <td>@item.TotalWin</td>
                    <td>
                        <a href="javascript:dsLayerOpen('@Url.Action("Detail", new { id = item.Id })','@T("详细")')" class="dsui-btn-view"><i class="iconfont"></i>@T("详细")</a>
                        <a href="javascript:dsLayerOpen('@Url.Action("Edit", new { id = item.Id })','@T("编辑")')" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                        <a href="javascript:dsLayerConfirm('@Url.Action("Delete", new { id = item.Id })','@T("您确定要删除吗?")')" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
        </tbody>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>