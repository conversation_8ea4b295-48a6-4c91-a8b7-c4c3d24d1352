﻿using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("秒杀活动")]
    [Description("秒杀活动管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/FlashSaleActivity", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/FlashSaleActivity", CurrentMenuName = "FlashSaleActivityList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class FlashSaleActivityController : PekCubeAdminControllerX
    {
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("秒杀活动管理")]
        public IActionResult Index(string name, long storeId, int status, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            var list = FlashSaleActivity.Search(name,storeId,status,pages);
            viewModel.list = list;
            viewModel.name = name;
            viewModel.storeId = storeId;
            viewModel.status = status;
            viewModel.page = page;
            viewModel.limit = limit;
            viewModel.storelist = Store.FindAll();
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {
                { "name", name },
                { "storeId", storeId.SafeString() },
                { "status", status.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("活动详情")]
        public IActionResult ActivityDetail(long id)
        {
            var activity = FlashSaleActivity.FindById(id);
            if (activity==null)
            {
                return Content(GetResource("未找到秒杀活动"));
            }
            return View(activity);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐管理")]
        public IActionResult FlashSalePackage(long storeId)
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.list = FlashSaleQuota.FindAll();
            viewModel.storelist = Store.FindAll();
            viewModel.storeId = storeId;
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        public IActionResult PackageSet()
        {
            var price = OperationConfig.GetValueByCode("FlashSalePackagePrice").ToDecimal(0);
            return View(price);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        [HttpPost]
        public IActionResult PackageSet(decimal price)
        {
            if (price <= 0)
            {
                return Json(new DResult() { success = false, msg = "套餐价格必须大于0" });
            }

            // 保存套餐价格到配置表
            var flag = OperationConfig.UpdateValueByCode("FlashSalePackagePrice", price.SafeString(), "秒杀套餐价格");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = "保存失败" });
            }

            return Json(new DResult() { success = true, msg = "设置成功" });
        }
    }
}
