﻿@{
}
<div class="page">
    @using (Html.BeginForm("UpdateExpSetting", "ExpPoints", FormMethod.Post, new Dictionary<String, Object> { { "id", "settingForm" }, { "name", "settingForm" } }))
    {
        <table class="ds-default-table">
            <tbody>
                <tr>
                    <td class="" colspan="2">
                        <table class="ds-default-table">
                            <thead>
                                <tr class="space">
                                    <th colspan="16">经验值获取规则:</th>
                                </tr>
                                <tr class="thead">
                                    <th>项目</th>
                                    <th>经验值</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="hover">
                                    <td class="w200">会员每天第一次登录</td>
                                    <td><input id="exp_login" name="exp_login" value="@DHSetting.Current.ExpLogin" class="txt" type="text" style="width:60px;"></td>
                                </tr>
                                <tr class="hover">
                                    <td class="w200">订单商品评论</td>
                                    <td><input id="exp_comments" name="exp_comments" value="@DHSetting.Current.ExpComments" class="txt" type="text" style="width:60px;"></td>
                                </tr>
                            </tbody>
                        </table>
                        <table class="ds-default-table">
                            <thead>
                                <tr class="thead">
                                    <th colspan="2">购物</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="hover">
                                    <td class="w200">消费额与赠送经验值比例</td>
                                    <td>
                                        <input id="exp_orderrate" name="exp_orderrate" value="@DHSetting.Current.ExpOrderRate" class="txt" type="text" style="width:60px;">
                                        该值为大于0的数， 例:设置为10，表明消费10单位货币赠送1经验值
                                    </td>
                                </tr>
                                <tr class="hover">
                                    <td>每订单最多赠送经验值</td>
                                    <td>
                                        <input id="exp_ordermax" name="exp_ordermax" value="@DHSetting.Current.ExpOrderMax" class="txt" type="text" style="width:60px;">
                                        该值为大于等于0的数，填写为0表明不限制最多经验值，例:设置为100，表明每订单赠送经验值最多为100经验值
                                    </td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr class="tfoot">
                                    <td colspan="2">
                                        <input class="btn" type="submit" value="提交" />
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    }
</div>