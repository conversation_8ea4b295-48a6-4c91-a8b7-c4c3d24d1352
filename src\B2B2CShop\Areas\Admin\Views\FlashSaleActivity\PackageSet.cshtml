﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <form id="add_form" method="post" action="@Url.Action("PackageSet")">
        <table class="ds-default-table">
            <tbody>
            <tr class="noborder">
                <td class="required w120"><label class="validation">@T("秒杀价格:")</label></td>
                <td class="vatop rowform">
                    <input type="text" id="price" name="price" value="@Model" class="txt" inputmode="decimal" autocomplete="off">
                </td>
                <td class="vatop tips">@T("购买单位为月(30天)，购买后卖家可以在所购买周期内发布秒杀活动")</td>
            </tr>
            </tbody>
            <tfoot>
            <tr class="tfoot">
                <td colspan="15"><input class="btn" type="submit" value="@T("提交")"/></td>
            </tr>
            </tfoot>
        </table>
    </form>

</div>
<script type="text/javascript">
    $(function () {
        var isSubmitting = false;
        var $form = $('#add_form');
        var $submitBtn = $('input[type=submit],button[type=submit]');

        $form.on('submit', function (e) {
            e.preventDefault();
            if (isSubmitting) {
                if (typeof layer !== 'undefined') layer.msg('@T("正在提交，请勿重复点击")', { icon: 0 });
                return false;
            }

            var priceStr = $.trim($('#price').val());
            var test = parseFloat((priceStr || '').replace(',', '.'));
            if (!priceStr || isNaN(test) || test <= 0) {
                if (typeof layer !== 'undefined') layer.msg('@T("套餐价格必须大于0")', { icon: 2 });
                else alert('@T("套餐价格必须大于0")');
                return false;
            }

            isSubmitting = true;
            $submitBtn.prop('disabled', true).addClass('is-loading');

            $.ajax({
                url: $form.attr('action'),
                type: 'POST',
                data: { price: priceStr },
                success: function (res) {
                    var ok = res && (res.success === true || res.Success === true);
                    var msg = (res && (res.msg || res.Message)) || (ok ? '@T("设置成功")' : '@T("操作失败")');
                    if (typeof layer !== 'undefined') {
                        layer.msg(msg, { icon: ok ? 1 : 2, time: 800 }, function () {
                            if (ok) {
                                try {
                                    if (window.parent && window.parent.layer) {
                                        var index = window.parent.layer.getFrameIndex(window.name);
                                        window.parent.layer.close(index);
                                    } else if (window.close) {
                                        window.close();
                                    }
                                } catch (e) { }
                            } else {
                                isSubmitting = false;
                                $submitBtn.prop('disabled', false).removeClass('is-loading');
                            }
                        });
                    } else {
                        alert(msg);
                        if (!ok) {
                            isSubmitting = false;
                            $submitBtn.prop('disabled', false).removeClass('is-loading');
                        } else {
                            try { if (window.close) window.close(); } catch (e) { }
                        }
                    }
                },
                error: function () {
                    if (typeof layer !== 'undefined') layer.msg('@T("网络错误，请稍后重试")', { icon: 2 });
                    else alert('@T("网络错误，请稍后重试")');
                    isSubmitting = false;
                    $submitBtn.prop('disabled', false).removeClass('is-loading');
                }
            });
        });
    });
</script>