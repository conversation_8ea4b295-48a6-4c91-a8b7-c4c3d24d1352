﻿using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>规格管理</summary>
[DisplayName("规格管理")]
[Description("用于商品规格的管理")]
[AdminArea]
[DHMenu(75, ParentMenuName = "Products", CurrentMenuUrl = "~/{area}/GoodsSpec", CurrentMenuName = "GoodsSpecList", CurrentIcon = "&#xe71d;", LastUpdate = "20241218")]
public class GoodsSpecController : PekCubeAdminControllerX {
    /// <summary>
    /// 规格列表
    /// </summary>
    /// <param name="specificationName"></param>
    /// <param name="classificationName"></param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("规格列表")]
    public IActionResult Index(string specificationName, string classificationName, int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };

        specificationName = specificationName.SafeString().Trim();
        classificationName = classificationName.SafeString().Trim();

        viewModel.list = GoodSpec.FindBySpecNameAndClassName(specificationName, classificationName, pages);
        viewModel.page = page;
        viewModel.specificationName = specificationName;
        viewModel.classificationName = classificationName;

        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> { { "specificationName", specificationName }, { "classificationName", classificationName } });
        return View(viewModel);
    }

    /// <summary>
    /// 新增商品规格
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增商品规格")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddSpec()
    {
        return View();
    }

    /// <summary>
    /// 新增商品规格
    /// </summary>
    /// <param name="classificationId"></param>
    /// <param name="subClassificationId"></param>
    /// <param name="specificationName"></param>
    /// <param name="sort"></param>
    /// <returns></returns>
    [DisplayName("新增商品规格")]
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    public IActionResult AddSpec(string subClassificationId, string specificationName, int sort)
    {
        var goodSpec = new GoodSpec();
        long classificationId;
        if (subClassificationId.IsNotNullAndWhiteSpace())
        {
            classificationId = subClassificationId.ToLong();
        }
        else
        {
            goodSpec.GoodsClassId = 0;
            goodSpec.GoodsClassName = "";
            goodSpec.Name = specificationName;
            goodSpec.Sort = sort;

            goodSpec.Insert();

            return MessageTip(GetResource("创建成功"));
        }
        goodSpec.GoodsClassId = classificationId;
        var goodsClass = GoodsClass.FindById(classificationId);
        goodSpec.GoodsClassName = goodsClass?.Name;
        goodSpec.Name = specificationName;
        goodSpec.Sort = sort;

        goodSpec.Insert();

        return MessageTip(GetResource("创建成功"));
    }

    /// <summary>
    /// 修改商品规格
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("修改商品规格")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditSpec(int id)
    {
        var goodSpec = GoodSpec.FindById(id);
        return View(goodSpec);
    }

    /// <summary>
    /// 修改商品规格
    /// </summary>
    /// <param name="id"></param>
    /// <param name="classificationId"></param>
    /// <param name="subClassificationId"></param>
    /// <param name="specificationName"></param>
    /// <param name="sort"></param>
    /// <returns></returns>
    [DisplayName("修改商品规格")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult EditSpec(int id, string subClassificationId, string specificationName, int sort)
    {
        var goodSpec = GoodSpec.FindById(id);
        if (goodSpec==null)
        {
            return Prompt(new PromptModel { Message = GetResource("商品规格不存在") });
        }
        long classificationId;
        if (subClassificationId.IsNotNullAndWhiteSpace())
        {
            classificationId = subClassificationId.ToLong();
        }
        else
        {
            goodSpec.GoodsClassId = 0;
            goodSpec.GoodsClassName = "";
            goodSpec.Name = specificationName;
            goodSpec.Sort = sort;
            goodSpec.Update();
            return MessageTip(GetResource("修改成功"));
        }
        var goodsClass = GoodsClass.FindById(classificationId);
        if (goodsClass == null)
        {
            return Prompt(new PromptModel { Message = GetResource("商品分类不存在") });
        }
        goodSpec.GoodsClassId = classificationId;
        goodSpec.GoodsClassName = goodsClass.Name;
        goodSpec.Name = specificationName;
        goodSpec.Sort = sort;
        goodSpec.Update();
        return MessageTip(GetResource("修改成功"));
    }

    /// <summary>
    /// 查询商品分类
    /// </summary>
    /// <param name="pid"></param>
    /// <returns></returns>
    [DisplayName("查询商品分类")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetGoodsClass(string pid)
    {
        var goodsClass = GoodsClass.FindAllByParentId(pid.ToLong());

        return Json(goodsClass.Select(gc => new { Id = gc.Id.ToString(), gc.Name }));
    }

    /// <summary>
    /// 删除商品规格
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [DisplayName("删除商品规格")]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult Delete(string ids)
    {
        int[] idArr = ids.SplitAsInt();
        if(idArr.Contains(1))
        {
            return Json(new DResult { success = false, code = 10001, msg = GetResource("不能删除默认规格") });
        }
        var res = new DResult();
        GoodSpec.DelByIds(ids);
        GoodSpec.Meta.Cache.Clear("", true);

        GoodTypeSpec.DelBySpecIds(ids);
        GoodTypeSpec.Meta.Cache.Clear("", true);

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
}