@using B2B2CShop.Entity
@inject IWorkContext workContext
@{
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/productDetails.css");

    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;

}
<body>

    <!-- 头部结束 -->
    <div class="_main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div class="textSelect">
                PM01
            </div>
        </div>

        <!-- 正片开始 -->
        <div class="productBox">
            <div class="left flex">
                <div>
                    @foreach(var item in ViewBag.Images)
                    {
                        <div><img src="@item" alt="产品图片"></div>
                    }
                    @*                     <div><img src="../../images/icons/device3.png" alt="产品图片"></div>
                    <div><img src="../../images/icons/device3.png" alt="产品图片"></div>
                    <div><img src="../../images/icons/device3.png" alt="产品图片"></div>
                    <div><img src="../../images/icons/device3.png" alt="产品图片"></div>
                    <div><img src="../../images/icons/device3.png" alt="产品图片"></div> *@
                </div>
                <div>
                    <div>
                        <img src="@Model.GoodsImage" alt="">
                    </div>
                </div>
            </div>
            <div class="right">
                <div class="productTitle">@Model.Name</div>
                <div class="flex"> <div>@T("原厂编号"):    </div>     <div class="textOver" style="margin-left: auto; width: 60%;">727-S40FC008C3B1V000  </div>      </div>
                <div class="flex">
                    <div>@T("制造商"):      </div>
                    <div class="textSelect pointer" style="margin-left: auto; width: 60%;" onclick="toRouter(this)"
                    data-link="../../pages/provider/providerDetail.html">
                        Hi-Link
                    </div>
                </div>
                <div class="flex"> <div>@T("制造商编号"):  </div>     <div class="textOver" style="margin-left: auto; width: 60%;">727-S40FC008C3B1V000  </div>      </div>
                <div class="flex"> <div>@T("型号"):       </div>      <div class="textOver" style="margin-left: auto; width: 60%;">HLK-PM01             </div>      </div>
                <div class="flex"> <div>@T("封装"):       </div>      <div class="textOver" style="margin-left: auto; width: 60%;">插件                 </div>       </div>
                <div class="flex"> <div>@T("包装方式"):   </div>      <div class="textOver" style="margin-left: auto; width: 60%;">盒装                  </div>      </div>

                <div class="starBox">
                    <div id="ID-rate-demo"></div> @Model.EvaluationCount @T("个评价") | @Model.GoodsSalenum@T("人购买")
                </div>
                <script>
                    layui.use(function(){
                    var rate = layui.rate;
                    // 渲染
                    rate.render({
                    elem: '#ID-rate-demo'
                    });
                    });
                </script>
                <div class="configBox">
                    <div style="width: 100%;">@T("型号"):HLK-PM01</div>
                    <div onclick="selectDiv(this)" data-select="true">HLK-PM01</div>
                    <div onclick="selectDiv(this)" data-select="false">HLK-3M05</div>
                    <div onclick="selectDiv(this)" data-select="false">HLK-3M09</div>
                    <div onclick="selectDiv(this)" data-select="false">HLK-3M12</div>
                    <div onclick="selectDiv(this)" data-select="false">HLK-3M15</div>
                    <div onclick="selectDiv(this)" data-select="false">HLK-3M24</div>
                </div>
                <div class="booksBtnBox">
                    <button class="button">  <i class="iconfont icon-pdf"></i>  @T("规格书")</button>
                    <button class="button">  <i class="iconfont icon-tubiao_daochuCAD"></i>  @T("CAD工具")</button>
                </div>
            </div>
        </div>
        <!-- 产品推荐 -->
        <div class="productComment">
            <div class="title">
                产品推荐
            </div>
            <div class="mainBox2_container">
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">
                        34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">
                        34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">
                        34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">
                        34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">
                        34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">
                        34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
            </div>
        </div>

    </div>
    <!-- 商品参数 / 用户评价 -->


    <div class="optionsBox2">
        <div class="titleBox1 flex">
            <div data-select="false" onclick="titleClick(this,1)" class="titleSelect" style="margin-left: 1vw;">@T("商品参数")</div>
            <div data-select="false" onclick="titleClick(this,2)" style="margin-left: 20px;">@T("用户评价")</div>
        </div>
    </div>
    <script>
        function titleClick(dom,index) {
        $(dom).siblings().removeClass('titleSelect');
        $(dom).addClass('titleSelect');
        if ( index == 1) {
        $('.optionsBox2').nextUntil('.goodsDataTitle1').hide();
        if ($('.goodsDataTitle1')[0].style.display === 'none') {
        $('.commentBox').nextUntil('.bug').show();
        }
        }else{
        $('.commentBox').nextUntil('.bug').hide();
        if ($('.commentHeader')[0].style.display === 'none') {
        $('.optionsBox2').nextUntil('.goodsDataTitle1').show();
        }
        }

        }
    </script>
    <!-- 评论头 -->
    <div class="commentHeader" style="display: none;">
        <div class="titleBox">
            <div>@T("用户评价")</div>
        </div>
        <div class="optionsBox flex">
            <div data-select="false" onclick="selectDiv(this,'titleSelect')" class="titleSelect" style="margin-left: 0px;">全部</div>
            <div data-select="false" onclick="selectDiv(this,'titleSelect')">有图</div>
            <div data-select="false" onclick="selectDiv(this,'titleSelect')">追评</div>
            <div data-select="false" onclick="selectDiv(this,'titleSelect')">好评</div>
            <div data-select="false" onclick="selectDiv(this,'titleSelect')">差评</div>
            <div style="margin-left: auto;">默认排序 <i class="iconfont icon-xiangxia"></i> </div>
        </div>
    </div>
    <!-- 评论区 -->
    <div class="commentBox" style="display: none;">
        <div class="commentContainer">
            <div class="commentContent">
                <!-- 每一条评论 -->
                <div class="comment">
                    <div class="commentUserInfo flex">
                        <div class="avatar" style="width: 5%;">
                            <img src="../../images/icons/zhanghao.png" alt="">
                        </div>
                        <div class="userInfo">
                            <div class="userName">张三</div>
                            <div>1天前 · HLK-5M05【220V转5V1A-5W】</div>
                        </div>
                        <div class="iconfont icon-icon" style="margin-left: auto;">有用(6)</div>
                    </div>
                    <div class="commentText">
                        很好，下次还会回购
                        <div class="imageBox">
                            <img src="../../images/icons/device.png" alt="">
                            <img src="../../images/icons/device.png" alt="">
                        </div>
                    </div>
                </div>
                <div class="comment">
                    <div class="commentUserInfo flex">
                        <div class="avatar" style="width: 5%;">
                            <img src="../../images/icons/zhanghao.png" alt="">
                        </div>
                        <div class="userInfo">
                            <div class="userName">张三</div>
                            <div>1天前 · HLK-5M05【220V转5V1A-5W】</div>
                        </div>
                        <div class="iconfont icon-icon" style="margin-left: auto;">有用(6)</div>
                    </div>
                    <div class="commentText">
                        很好，下次还会回购
                        <div class="imageBox">
                            <img src="../../images/icons/device.png" alt="">
                            <img src="../../images/icons/device.png" alt="">
                        </div>
                    </div>
                </div>
                <!--  -->
                <div class="allComments">
                    <span>  查看全部 <i class="iconfont icon-a-zoomline"></i> </span>
                </div>
            </div>
            <!--  -->
        </div>
    </div>
    <!-- & 商品参数 -->

    <div class="goodsDataTitle0 goodsDataTitle1 flex">
        @T("商品参数")
        <button class="button button_blue" style="margin-left: auto;">@T("查看类似产品")</button>
    </div>
    <div class="goodsDataTable">
        <table style="width: 100%;text-align: center;border: 1px solid #BABABA;overflow: hidden;">
            <colgroup>
                <col width="100">
            </colgroup>
            <thead>
                <tr style="background-color: var(--text-color4);">
                    <th style="padding: 1vw;">
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked>
                        </span>
                    </th>
                    <th>梯度</th>
                    <th>原价</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked>
                        </span>
                    </td>
                    <td>1+</td>
                    <td>￥3.88</td>
                </tr>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked>
                        </span>
                    </td>
                    <td>10+</td>
                    <td>￥3.88</td>
                </tr>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked>
                        </span>
                    </td>
                    <td>20+</td>
                    <td>￥3.88</td>
                </tr>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked>
                        </span>
                    </td>
                    <td>50+</td>
                    <td>￥3.88</td>
                </tr>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked>
                        </span>
                    </td>
                    <td>100+</td>
                    <td>￥3.88</td>
                </tr>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked>
                        </span>
                    </td>
                    <td>200+</td>
                    <td>￥3.88</td>
                </tr>
            </tbody>
        </table>
        <div class="likes flex"><div style="margin-left: auto;">2774个相似产品</div></div>
    </div>
    <div class="goodsDataTitle1 flex">
        @T("产品手册PDF")
        <button class="button button_blue" style="margin-left: auto;">@T("下载PDF")</button>
    </div>
    <div class="profilePDF">
        <iframe src="../..//images/files/pdf.pdf"></iframe>
    </div>
    <div class="goodsDataTitle2 goodsDataTitle1" style="padding: 1vw;margin-top: 2vw;">@T("购物指南")</div>

    <div class="shoppingGuide goodsDataTitle3">
        <img src="../../images/pics/02.png" alt="">
    </div>
    <div class="bug"></div>

    <aside class="orderAside" style="display: block;">
        <div class="asideTitle">
            <i class="iconfont icon-zhekou-shi"><span style="margin-left: .5vw;">5件95折</span></i>
            <i class="iconfont icon-chevron-right" style="margin-left: auto;"></i>
        </div>

        <div class="price">
            <div><b style="font-size: 1.7vw;">@<EMAIL></b> <span class="gray">¥5.99</span> <span class="discount">- 63%</span></div>
            <div><div class="gray" style="font-size: 15px;">税前价格</div></div>
        </div>

        <div class="asideTextTitle">阶梯价格</div>
        <div class="tableBox">
            <table style="height: 8vw;width: 100%;text-align: center;">
                <thead>
                    <tr style="background-color: var(--text-color4);">
                        <th style="padding: 0.2vw;">梯度</th>
                        <th style="padding: 0.2vw;">原价</th>
                        <th style="padding: 0.2vw;">售价</th>
                        <th style="padding: 0.2vw;">折合1盒</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1+</td>
                        <td>￥3.88</td>
                        <td>￥3.48</td>
                        <td>￥228</td>
                    </tr>
                    <tr>
                        <td>10+</td>
                        <td>￥3.88</td>
                        <td>￥3.48</td>
                        <td>￥228</td>
                    </tr>
                    <tr>
                        <td>20+</td>
                        <td>￥3.88</td>
                        <td>￥3.48</td>
                        <td>￥228</td>
                    </tr>
                    <tr>
                        <td>50+</td>
                        <td>￥3.88</td>
                        <td>￥3.48</td>
                        <td>￥228</td>
                    </tr>
                    <tr>
                        <td>100+</td>
                        <td>￥3.88</td>
                        <td>￥3.48</td>
                        <td>￥228</td>
                    </tr>
                    <tr>
                        <td>200+</td>
                        <td>￥3.88</td>
                        <td>￥3.48</td>
                        <td>￥228</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="asideTextTitle">@T("库存总量(单位: 个)")</div>
        <div class="tableBox">
            <table style="height: 2.5vw;width: 100%;text-align: center;">
                <thead>
                    <tr style="background-color: var(--text-color4);">
                        <th style="padding: 0.2vw;">内陆仓</th>
                        <th style="padding: 0.2vw;">海外仓</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>8,960</td>
                        <td>7,160</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div></div>
        <div class="asideTextTitle" style="padding-top: 0vw;">@T("购买数量(100个 / 盒)")</div>
        <!-- 计数器 -->
        <div class="computer">
            <div class="iconfont icon-jianhao" onclick="compute(0,10,0)"></div>
            <div style="flex: 1;" id="count"> 10 </div>
            <div class="iconfont icon-tianjia1" onclick="compute(1,10,0)"></div>
        </div>
        <div class="gray" style="padding-top: 2px;">@T("起订量"):10 @T("增量"):10</div>
        @{
            var num = Model.GoodsPrice * 10;
            <div class="asideTextTitle totalPrice" id="totalPrice">@T("总价")：@symbolLeft@num</div>
        }
        
        <div>
            <button class="button asideBtn" style="color: var(--blue-deep);" onclick="addCart()">@T("加入购物车")</button>
        </div>
        <div style="padding: 0px;">
            <button class="button asideBtn button_blue" onclick="Buynow()">@T("立即购买")</button>
            <form id="buyNowForm" action="@Url.Action("CheckOrder", "Shopping")" method="post">
                <input type="hidden" name="goodsIds" value="@Model.Id" />
                <input type="hidden" name="nums" id="nums" value="1" />
            </form>
        </div>

        <div class="flex aside_btnBox2" style="justify-content: space-around;flex-wrap: wrap;padding-top: 1.2vw;">
            @if (ViewBag.IsWish)
            {
                <div onclick="delWishlist()"><i class="iconfont icon-aixin pointer" style="color: var(--blue-deep);"></i>@T("取消心愿单")</div>
            }
            else
            {
                <div onclick="addWishlist()"><i class="iconfont icon-aixin2 pointer" style="color: var(--blue-deep);"></i>@T("加入心愿单")</div>
            }
            <div><i class="iconfont icon-share pointer" style="color: var(--blue-deep);padding-right:0.5vw;"></i>@T("分享链接")</div>
        </div>
        <!-- 右侧边栏 -->
        <script>
            // function handleScroll() {
            //       //到顶 或者到底 就隐藏
            //       if (isAtTop() || isAtBottom()) {
            //           $('.orderAside').fadeOut(300);
            //       }else{
            //           $('.orderAside').fadeIn();
            //       }
            //       //处理滚动事件的逻辑
            //   }

            //   // 节流处理滚动事件
            //   const throttledScroll = throttle(handleScroll, 200);
            //   window.addEventListener('scroll', throttledScroll);
            // // document.addEventListener('mousewheel', function (e) {

            // // })
        </script>
    </aside>
    <!-- <img src="../../images/pics/01.png" class="img" alt=""> -->
    <!-- 服务申明 -->
    <div class="serverInfo">
        <div class="serverTitle" style="font-size: 1vw;">@T("服务申明")</div>
        <div class="serverTitle flex">
            <div class="iconfont icon-sign" style="font-size: 1.5vw;margin-left: .2vw;margin-right: .2vw;"></div>
            <div>@T("快递")</div>
        </div>
        <div class="serverItem flex">
            <div class="iconfont icon-ai210"></div><div>@T("支持七天无理由退货")</div>
        </div>
        <div class="serverItem flex">
            <div class="iconfont icon-ai210"></div><div>@T("如果快递丢失，支持退货")</div>
        </div>
        <div class="serverItem flex">
            <div class="iconfont icon-ai210"></div><div>@T("如果快递损坏，支持退货")</div>
        </div>
        <div class="serverItem flex">
            <div class="iconfont icon-ai210"></div><div>@T("支持90天内免费换货")</div>
        </div>

        <div class="serverTitle"> <i class="iconfont icon-secured" style="font-size: 1.5vw;"></i> @T("安全与隐私")</div>
        <div class="serverItem">
            <div>@T("安全付款:未经您的同意，我们不会与任何第三方分享您的个人信息。")</div>
            <div>@T("安全的个人资料:我们保护您的隐私，确保您的个人资料安全可靠。")</div>
        </div>

        <div class="serverTitle">
            <i class="iconfont icon-money-circle" style="font-size: 1.5vw;"></i>
            @T("支付安全")
        </div>
        <div class="serverItem">
            <div class="paymentMethods flex" style="justify-content: left;margin-bottom: .5vw;">
                <!-- <img src="../../images/icons/zhifubao.png" alt=""> -->
                <img src="../../images/icons/paypal.png" alt="">
                <img src="../../images/icons/weixin.png" alt="">
            </div>
            <div>@T("与受欢迎的支付合作伙伴合作，您的个人信息是安全的。")</div>
        </div>
    </div>
    </div>

</body>
<script asp-location="Footer">

    function Buynow()
    {
        var GoodsNum = $("#count").text();
        $("#nums").val(GoodsNum);
         var form = document.getElementById('buyNowForm');
         form.submit();
    }

    //加入购物车
    function addCart()
    {
        var GoodsId = "@Model.Id";
        var GoodsNum = parseInt($("#count").text());
        $.post("@Url.Action("AddCart")",{GoodsId,GoodsNum},function(res)
        {
            layui.layer.msg(res.msg);
        })
    }

        // 假设每个商品的单价是3.68元
    var unitPrice = "@Model.GoodsPrice";

    function updateTotalPrice(change) {
        var countElement = document.getElementById('count');
        var priceElement = document.getElementById('totalPrice');
        var currentCount = parseInt(countElement.textContent);
        var newCount = currentCount + change;

        // 更新数量显示
        countElement.textContent = newCount;

        // 计算总价
        var totalPrice = newCount * unitPrice;
        // 显示总价，保留两位小数
        priceElement.textContent = '@T("总价")：'+'@symbolLeft' + totalPrice.toFixed(2);
    }

    // 页面加载完毕后，初始化事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelector('.icon-jianhao').addEventListener('click', function() { updateTotalPrice(0); });
        document.querySelector('.icon-tianjia1').addEventListener('click', function() { updateTotalPrice(0); });
    });




    @* 加入心愿清单 *@
    function addWishlist()
    {
        var id = "@Model.Id";
        $.post('@Url.Action("AddWishlist")',{goodsId:id,sId:'@SId'},function(res)
        {
            layui.layer.msg(res.msg);
            if(res.success)
            {
                location.reload();
            }
        })
    }
    @* 取消心愿清单 *@
    function delWishlist()
    {
        var id = "@Model.Id";
        $.post('@Url.Action("DelWishlist")',{goodsId:id},function(res)
        {
            layui.layer.msg(res.msg);
            if(res.success)
            {
                location.reload();
            }
        })
    }
</script>