﻿@using Pek.IO

<style>
    img {
        object-fit: contain;
        max-width: 250px;
    }
</style>
@*
@foreach (Brand item in Model.list)
{
<div>品牌ID</div>
<div>@item.Id</div>
<div>品牌名称</div>
<div>@item.Name</div>
<div>所属分类</div>
<div>@item.BClass</div>
<div>品牌图片标识</div>
<div>
    @if (string.IsNullOrEmpty(item.Pic))
    {
    <img src="@DHSetting.Current.CurDomainUrl/@FileUtil.JoinPath(DHSetting.Current.UploadPath, $"
        common/default_brand_image.gif")" onerror="" width="50" height="50" />
    }
    else
    {
    <img src="@DHSetting.Current.CurDomainUrl/@FileUtil.JoinPath(DHSetting.Current.UploadPath, $" Brand/{item.Pic}")"
        onerror="" width="50" height="50" />
    }
</div>
}*@
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>欢迎界面</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>管理</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("AddBrand")','@T("添加")')" ><span>@T("添加")</span></a></li>
                <li><a href="@Url.Action("ApplyIndex")" class="current"><span>待审核</span></a></li>
            </ul>
        </div>
    </div>

    <div class="fixed-empty"></div>
    <form method="get" name="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>品牌名称</dt>
                <dd><input class="txt" name="brandName" id="search_brand_name" value="" type="text"></dd>
            </dl>
            <dl>
                <dt>所属分类</dt>
                <dd><input class="txt" name="classificationName" id="search_brand_class" value="" type="text"></dd>
            </dl>
            <div class="btn_group">
                <a href="javascript:document.formSearch.submit();" class="btn " title="查询">查询</a>
            </div>
        </div>
    </form>

    <form method='post' id="form_brand">
        <input type="hidden" name="type" id="type" value="" />
        <table class="ds-default-table">
            <thead>
                <tr class="space">
                    <th colspan="15">列表</th>
                </tr>
                <tr class="thead">
                    <th>&nbsp;</th>
                    <th>品牌名称</th>
                    <th>所属分类</th>
                    <th>品牌图片标识</th>
                    <th class="align-center">操作</th>
                </tr>
            </thead>
            <tbody>
                @foreach (Brand item in Model.list)
                {
                    <tr>
                        <td class="w24">
                            <input type="checkbox" class="checkitem" name="del_id[]" value="@item.Id">
                        </td>
                        <td>@item.Name</td>
                        <td>@item.BClass</td>
                        <td>
                            @if (string.IsNullOrWhiteSpace(item.Pic))
                            {
                            <img src="/uploads/common/default_brand_image.gif" onerror="" width="50" height="50" />
                            }
                            else
                            {
                                <img src="@FileUtil.JoinPath(DHSetting.Current.CurDomainUrl, $"{item.Pic}")" onerror=""/>
                            }
                        </td>
                        <td class="w96 align-center">
                            <a href="@Url.Action("ApplyPass", new { id = item.Id })" class="dsui-btn-add"><i class="iconfont"></i>通过</a>
                            @* <a href="javascript:;"
                            onclick="javascript:dsLayerConfirm('@Url.Action("ApplyPass", new { id = item.Id })','@T("确认要通过吗?")')"
                                class="dsui-btn-add"><i class="iconfont"></i>通过</a> *@
                            <a href="javascript:;"
                            onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { ids = item.Id })','@T("您确定要删除吗?")')"
                                class="dsui-btn-del"><i class="iconfont"></i>删除</a>
                        </td>
                    </tr>
                }
                @if(Model.list.Count == 0)
                {
                    <tr class="no_data">
                        <td colspan="10">没有符合条件的记录</td>
                    </tr>
                }               
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                    <td colspan="16">
                        <label for="checkallBottom">@T("全选")</label>
                        &nbsp;&nbsp;
                        <a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_form('pass');" name="id">
                            <span>通过</span>
                        </a>
                        &nbsp;&nbsp;
                        <a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch();" name="id">
                            <span>删除</span>
                        </a>
                    </td>
                </tr>
            </tfoot>
        </table>
        <ul class="pagination">
            @Html.Raw(Model.PageHtml)
        </ul>
    </form>
</div>
<script>
    function submit_form(type) {
        layer.confirm('您确定进行这操作吗？', {
            btn: ['确定', '取消'],
            title: false,
        }, function () {
            $('#type').val(type);
            $('#form_brand').submit();
        });
    }
</script>
<script asp-location="Footer">
    function submit_delete(items) {
        _uri = "@Url.Action("Delete")?ids=" + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
    }
</script>