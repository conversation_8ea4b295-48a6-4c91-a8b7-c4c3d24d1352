﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@model Goods
@{
    var IsEnable = LocalizationSettings.Current.IsEnable;
    var goodsskus = GoodsSKUDetail.FindAllByGoodsId(Model.Id);

}

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("商品管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("GoodsPriceLock")" class="current"><span>@T("价格锁定")</span></a></li>
            </ul>
        </div>
    </div>
    <div class="ds-default-form">
        <div class="layui-tab layui-tab-brief  Lan" lay-filter="docDemoTabBrief">

            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="ncap-form-default">
                        <dl>
                            <dt>@T("SKU价格信息")：</dt>
                            <dd>
                                @{
                                    <table class="sku-table">
                                        <thead>
                                            <tr>
                                                <th>@T("SKU属性")</th>
                                                <th>@T("物料名称")</th>
                                                <th>@T("成本价")</th>
                                                <th>@T("售价")</th>
                                                <th>@T("阶梯价")</th>
                                                <th>@T("操作")</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var item in goodsskus)
                                            {
                                                var tiereds = GoodsTieredPrice.FindAllBySkuId(item.Id);
                                                var images = GoodsImages.FindAllBySkuId(item.Id);
                                                var material = MerchantMaterial.FindById(item.MaterialId);
                                                <tr>
                                                    <td>@item.SpecValueDetail(0)</td>
                                                    <td>@material?.Name</td>
                                                    <td>@material?.CostPrice</td>
                                                    <td>@item.GoodsPrice</td>
                                                    <td>
                                                        @foreach (var tiered in tiereds)
                                                        {
                                                            <p>@tiered.MinQuantity + @tiered.Price</p>
                                                        }
                                                    </td>
                                                    <td>
                                                        <span class="op-cell" data-id="@item.Id">
                                                        @if (item.PriceLock == 1)
                                                        {
                                                            <span class="locked-label">已锁定</span>
                                                        }
                                                        else
                                                        {
                                                            <a href="javascript:;" class="dsui-btn lock-btn" data-id="@item.Id">
                                                                <i class="iconfont">&#xe768;</i>锁定
                                                            </a>
                                                        }
                                                        </span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                }
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<link rel="stylesheet" href="~/static/plugins/js/jquery.lightbox/css/lightbox.min.css">
<script src="~/static/plugins/js/jquery.lightbox/js/lightbox.min.js"></script>
<script>
    $(function () {
        $('body').on('click', '.lock-btn', function () {
            var $this = $(this);
            var id = $this.data('id');
            $.post('@Url.Action("LockPrice")', { skuId: id }, function (res) {
                if (res.success) {
                    $this.closest('.op-cell').html('<span class="locked-label">已锁定</span>');
                    layer.msg('锁定成功');
                } else {
                    layer.msg(res.msg || '操作失败');
                }
            });
        });
    });
</script>

<style>
    .sku-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
        font-size: 14px;
    }

    .sku-table th {
        background-color: #f8f8f8;
        padding: 12px 15px;
        text-align: left;
        font-weight: 600;
        border: 1px solid #e8e8e8;
    }

    .sku-table td {
        padding: 12px 15px;
        border: 1px solid #e8e8e8;
        vertical-align: middle;
    }

    .sku-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .sku-table tr:hover {
        background-color: #f5f5f5;
    }

    .sku-table img {
        max-width: 80px;
        max-height: 80px;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .tiered-price {
        margin: 3px 0;
        padding: 3px 5px;
        background: #f0f7ff;
        border-radius: 3px;
        display: inline-block;
    }

    .attribute-wrapper {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .attribute-item {
        padding: 10px;
        box-sizing: border-box;
        display: flex;
    }

    .attribute-item label {
        white-space: wrap;
        width: 150px;
        text-align: right;
    }

    .attribute-item input {
        flex: 1;
    }

    .clear {
        width: 100%;
    }

    .btn_group {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        padding: 10px;
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }

    .dsui-btn {
        display: inline-block;
        padding: 6px 12px;
        font-size: 12px;
        color: #fff;
        background-color: #1890ff;
        border-radius: 3px;
        text-decoration: none;
        transition: background-color .2s;
    }

    .dsui-btn:hover {
        background-color: #40a9ff;
        color: #fff;
    }

    .locked-label {
        display: inline-block;
        padding: 6px 12px;
        font-size: 12px;
        color: #999;
        background-color: #f5f5f5;
        border-radius: 3px;
        cursor: not-allowed;
    }
</style>