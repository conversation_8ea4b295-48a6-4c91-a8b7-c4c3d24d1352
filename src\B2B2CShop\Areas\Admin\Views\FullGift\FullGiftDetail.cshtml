﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    FullGift entity = Model.entity;
}
<style>
    /* 固定活动规则商品缩略图大小 */
    .mansong-rule-list .goods-thumb img {
        width: 80px;
        height: 80px;
        object-fit: cover; /* 裁剪填充不变形 */
        display: block;
    }
</style>
<div class="page">

    <table class="ds-default-table">
        <thead>
            <tr class="thead">
                <th class="w24"></th>
                <th class="align-left">@T("活动名称")</th>
                <th class="align-center">@T("开始时间")</th>
                <th class="align-center">@T("结束时间")</th>
                <th class="align-center">@T("活动列表")</th>
                <th class="align-center">@T("状态")</th>
            </tr>
        </thead>
        <tbody>
            <tr class="bd-line">

                <td></td>
                <td class="align-left">@entity.Name</td>
                <td class="align-center">@(UnixTime.ToDateTime(entity.StartTime))</td>
                <td class="align-center">@(UnixTime.ToDateTime(entity.EndTime))</td>
                <td>
                    <ul class="mansong-rule-list">
                        @foreach (FullGiftRule item in Model.rules)
                        {
                            <li>
                                @T("单笔订单满")<strong>@item.Price</strong>@T("元")，&nbsp;
                                @if (item.Discount > 0)
                                {
                                    @T("立减现金")
                                    <strong>@item.Discount</strong>
                                    @T("元")
                                }
                                <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })" title="@item.GoodsName @item.GoodsSku?.SpecValueDetail()" target="_blank"
                                   class="goods-thumb">
                                    <img src="@(AlbumPic.FindByName(item.GoodsSku?.GetDefaultGoodsImage() ?? "")?.Cover ?? "")" />
                                </a>
                            </li>
                        }
                    </ul>
                </td>
                <td class="align-center">
                    @if (UnixTime.ToTimestamp() > entity.EndTime)
                    {
                        @T("已结束")
                    }
                    else
                    {
                        @(entity.State == 1 ? T("未发布") : entity.State == 2 ? T("正常") : entity.State == 3 ? T("管理员关闭") : entity.State == 4 ? T("失效") : entity.State == 5 ? T("已结束") : T("未知"))
                    }
                </td>
            </tr>
        <tbody>
    </table>

</div>