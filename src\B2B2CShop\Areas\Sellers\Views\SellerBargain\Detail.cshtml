@using B2B2CShop.Entity
@using Pek.Timing
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "Bargain";
    PekHtml.AppendPageCssClassParts("html-sellerbargain-page");
    PekHtml.AppendTitleParts(T("砍价订单").Text);
    Bargain entity = Model.entity;
}
@await Html.PartialAsync("_Left")

<div class="seller_main">

    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li><a href="@Url.Action("Index")">@T("砍价活动列表")</a></li>
                <li class="current"><a href="@Url.Action("Detail",new {bargainId = entity.Id})">@T("砍价订单")</a></li>
            </ul>
        </div>
        <div class="p20">
            <form method="get">
                <input name="bargainId" value="@entity.Id" type="hidden" />
                <table class="search-form">
                    <tr>
                        <td>&nbsp;</td>
                        <th>@T("状态")</th>
                        <td class="w100">
                            <select name="status">
                                <option value="">-@T("请选择")-</option>
                                <!option value="0" @(Model.status == 0 ? "selected" : "")>@T("砍价取消")</!option>
                                <!option value="1" @(Model.status == 1 ? "selected" : "")>@T("砍价中")</!option>
                                <!option value="2" @(Model.status == 2 ? "selected" : "")>@T("砍价成功")</!option>
                                <!option value="3" @(Model.status == 3 ? "selected" : "")>@T("砍价失败")</!option>
                            </select>
                        </td>
                        <td class="w70 tc">
                            <input type="submit" class="submit" value="@T("搜索")" />
                        </td>
                    </tr>
                </table>
            </form>
            <table class="dssc-default-table">
                <thead>
                    <tr>
                        <th class="w80">@T("砍价名称")</th>
                        <th class="w80">@T("发起人")</th>
                        <th class="w80">@T("需砍多少次")</th>
                        <th class="w80">@T("砍价次数")</th>
                        <th class="w80">@T("砍价后价格")</th>
                        <th class="w180">@T("砍价时间")</th>
                        <th class="w80">@T("操作")</th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model.list.Count > 0)
                    {
                        foreach (BargainOrder item in Model.list)
                        {
                            <tr class="bd-line">
                                <td>@item.Name</td>
                                <td>@item.InitiatorName</td>
                                <td>@item.TotalCuts</td>
                                <td>@item.Times</td>
                                <td>@item.CurrentPrice</td>
                                <td>@(UnixTime.ToDateTime(item.StartTime) + "-" + UnixTime.ToDateTime(item.EndTime))</td>
                                <td>
                                    <span>
                                        <a href="@Url.Action("BargainsLog", new { orderId = item.Id })"
                                           class="btn-green">
                                            <i class="iconfont">&#xe734;</i>
                                            <p>@T("砍价详情")</p>
                                        </a>
                                    </span>
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td class="norecord" colspan="20">
                                <div class="warning-option">
                                    <i class="iconfont">&#xe64c;</i><span>@T("暂无符合条件的数据记录")</span>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                </tfoot>
            </table>
            <ul class="pagination">
                @Html.Raw(Model.PageHtml)
            </ul>
        </div>
    </div>
</div>
