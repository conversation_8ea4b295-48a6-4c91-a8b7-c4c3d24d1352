﻿using B2B2CShop.Entity;

using DH.Core.Domain.Localization;
using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife.Data;

using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>
/// 国际快递物流公司
/// </summary>
[DisplayName("国际快递物流")]
[Description("用于国际快递物流公司的管理")]
[AdminArea]
[DHMenu(79, ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/InternationalExpress", CurrentMenuName = "InternationalExpressList", CurrentIcon = "&#xe69e;", LastUpdate = "20250206")]

public class InternationalExpressController : PekCubeAdminControllerX
{
    /// <summary>
    /// 国际快递物流公司列表
    /// </summary>
    /// <param name="name"></param>
    /// <param name="key"></param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("国际快递物流公司列表")]
    public IActionResult Index(string name, string key, int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();
        //var pages = new PageParameter()
        //{
        //    PageIndex = page,
        //    PageSize = limit,
        //    RetrieveTotalCount = true,
        //    Sort = "Id",
        //    Desc = true
        //};

        name = name.SafeString().Trim();
        key = key.SafeString().Trim();

        //viewModel.list = InternationalExpress.FindByNameAndCode(name, key, pages).Select(e => new InternationalExpress { Id = e.Id, Name = InternationalExpressLan.FindByEIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name,Key = e.Key });

        var exp = new WhereExpression();
        if (!key.IsNullOrWhiteSpace()) exp &= InternationalExpress._.Key == key;

        var data = InternationalExpress.FindAll(exp)
            .Select(e => new InternationalExpress { Id = e.Id, Name = InternationalExpressLan.FindByEIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name, Key = e.Key,Status=e.Status })
            .WhereIf(!name.IsNullOrWhiteSpace(), e => e.Name.Contains(name))
            .ToList();

        var list = data.Skip((page - 1) * limit).Take(limit).ToList();

        var pageCount = data.Count / limit;
        if (data.Count % limit > 0) pageCount++;

        viewModel.list = list;
        viewModel.page = page;
        viewModel.name = name;
        viewModel.code = key;

        viewModel.PageHtml = PageHelper.CreatePage(page, data.Count, pageCount, Url.Action("Index"), new Dictionary<string, string> { { "name", name }, { "key", key } });
        return View(viewModel);
    }

    /// <summary>
    /// 新增国际快递物流公司
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增国际快递物流公司")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add()
    {
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View();
    }

    /// <summary>
    /// 新增国际快递物流公司
    /// </summary>
    /// <param name="name"></param>
    /// <param name="code"></param>
    /// <param name="status"></param>
    /// <param name="letter"></param>
    /// <param name="oType"></param>
    /// <param name="url"></param>
    /// <param name="tel"></param>
    /// <param name="email"></param>
    /// <param name="twoLetterIsoCode"></param>
    /// <returns></returns>
    [DisplayName("新增国际快递物流公司")]
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    public IActionResult Add(string name, string key, string status, string letter, short oType, string url, string tel, string email, string twoLetterIsoCode)
    {

        if (name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("名称不能为空") });
        }

        var express = new InternationalExpress
        {
            Name = name,
            Key = key,
            Status = status == "on",
            Letter = letter,
            OType = oType,
            Url = url,
            //Tel = tel,
            //Email = email,
            //TwoLetterIsoCode = twoLetterIsoCode
        };

        express.Insert();

        if (LocalizationSettings.Current.IsEnable)
        {
            var languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            using (var tran1 = InternationalExpressLan.Meta.CreateTrans())
            {
                foreach (var item in languagelist)
                {
                    var ex = new InternationalExpressLan();
                    ex.EId = express.Id;
                    ex.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                    if (ex.Name.IsNullOrWhiteSpace())
                    {
                        continue;
                    }
                    ex.LId = item.Id;
                    ex.Insert();
                }
                tran1.Commit();
            }
        }


        return MessageTip(GetResource("创建成功"));
    }

    /// <summary>
    /// 修改国际快递物流公司
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("修改国际快递物流公司")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(int id)
    {
        var express = InternationalExpress.FindById(id);
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(express);
    }

    /// <summary>
    /// 修改国际快递物流公司
    /// </summary>
    /// <param name="id"></param>
    /// <param name="name"></param>
    /// <param name="key"></param>
    /// <param name="status"></param>
    /// <param name="letter"></param>
    /// <param name="oType"></param>
    /// <param name="url"></param>
    /// <param name="tel"></param>
    /// <param name="email"></param>
    /// <param name="twoLetterIsoCode"></param>
    /// <returns></returns>
    [DisplayName("修改国际快递物流公司")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult Edit(int id, string name, string key, string status, string letter, short oType, string url, string tel, string email, string twoLetterIsoCode)
    {
        var express = InternationalExpress.FindById(id);
        if (express == null) return Prompt(new PromptModel { Message = GetResource("物流公司为空") });

        express.Name = name;
        express.Key = key;
        express.Status = status == "on";
        express.Letter = letter;
        express.OType = oType;
        express.Url = url;
        //express.Tel = tel;
        //express.Email = email;
        //express.TwoLetterIsoCode = twoLetterIsoCode;

        express.Update();
        if (LocalizationSettings.Current.IsEnable)
        {
            var languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

            var List = InternationalExpressLan.FindAllByEId(express.Id);
            using (var tran1 = InternationalExpressLan.Meta.CreateTrans())
            {
                foreach (var item in languagelist)
                {
                    var ex = List.Find(x => x.LId == item.Id);
                    if (ex != null)
                    {
                        ex.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                        ex.Update();
                    }
                    else
                    {
                        ex = new InternationalExpressLan();
                        ex.EId = express.Id;
                        ex.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                        if (ex.Name.IsNullOrWhiteSpace())
                        {
                            continue;
                        }
                        ex.LId = item.Id;
                        ex.Insert();

                    }
                }
                tran1.Commit();
            }
        }
        InternationalExpress.Meta.Cache.Clear("", true);
        InternationalExpressLan.Meta.Cache.Clear("", true);

        return MessageTip(GetResource("修改成功"));
    }

    /// <summary>
    /// 删除国际快递物流公司
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [DisplayName("删除国际快递物流公司")]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult Delete(string ids)
    {
        int[] idArr = ids.SplitAsInt();
        var res = new DResult();

        InternationalExpress.DelByIds(ids);
        InternationalExpress.Meta.Cache.Clear("", true);

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
}
