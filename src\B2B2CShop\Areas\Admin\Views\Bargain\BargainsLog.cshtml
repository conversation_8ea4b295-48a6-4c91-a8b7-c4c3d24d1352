﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <!-- 列表 -->
    <form id="list_form" method="post">
        <table class="ds-default-table">
            <thead>
                <tr class="thead">
                    <th width="80">@T("砍价者用户名")</th>
                    <th width="80">@T("砍掉价格")</th>
                    <th width="80">@T("砍价时间")</th>
                </tr>
            </thead>
            <tbody id="treet1">
                @if (Model.list.Count > 0)
                {
                    foreach (BargainLog item in Model.list)
                    {
                        <tr class="hover">
                            <td>@item.MemberName</td>
                            <td>@item.Price</td>
                            <td>@(UnixTime.ToDateTime(item.AddTime))</td>
                        </tr>
                    }
                }
                else
                {
                    <tr class="no_data">
                        <td colspan="7">@T("没有符合条件的记录")</td>
                    </tr>
                }
               
            </tbody>
        </table>
    </form>

</div>
