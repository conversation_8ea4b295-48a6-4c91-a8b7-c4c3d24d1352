﻿@using Pek.IO
@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<link rel="stylesheet" href="/static/plugins/js/jquery.lightbox/css/lightbox.min.css">
<script src="/static/plugins/js/jquery.lightbox/js/lightbox.min.js"></script>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("广告管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("广告位")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("广告")</span></a></li>
                <li><a href="javascript:dsLayerOpen('@Url.Action("AddAdvertising")','@T("新增广告")','900px','700px')"><span>@T("新增广告")</span></a></li>
            </ul>
        </div>
    </div>

    <form method="get" action="@Url.Action("AdvertisingView")">
        <div class="ds-search-form">
            <dl>
                <dt>@T("名称")</dt>
                <dd><input type="text" name="key" value="@Model.key" class="txt"></dd>
            </dl>
            <dl>
                <dt>@T("广告位")</dt>
                <dd>
                    <select name="aId" class="select">
                        <option value="">@T("请选择")</option>
                        @foreach (var advPosition in Model.AdvPositions)
                        {
                            if (Model.aId == advPosition.Id)
                            {
                                <option value="@advPosition.Id" selected>@advPosition.Name</option>
                            }
                            else
                            {
                                <option value="@advPosition.Id">@advPosition.Name</option>
                            }
                        }
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <button type="submit" class="btn">@T("搜索")</button>
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr>
                <th></th>
                <th>@T("广告名称")</th>
                <th>@T("所属广告位")</th>
                <th>@T("排序")</th>
                <th>@T("点击数")</th>
                <th>@T("图片")</th>
                <th>@T("启用")</th>
                <th>@T("开始时间")</th>
                <th>@T("结束时间")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (Advertising item in Model.list)
            {
                <tr>
                    <th><input type="checkbox" class="checkitem" value="@item.Id"></th>
                    <td>@item.Title</td>
                    <td>@item.APName</td>
                    <td>@item.Sort</td>
                    <td>@item.ClickNumber</td>
                    <td>
                        @if (string.IsNullOrWhiteSpace(item.PicturePath))
                        {
                            <img src="/uploads/common/default_brand_image.gif" onerror="" />
                        }
                        else
                        {
                            <div class="picture">
                                <a data-lightbox="lightbox-image" data-title="@item.Title" href="@FileUtil.JoinPath(DHSetting.Current.CurDomainUrl,item.PicturePath)">
                                    <img id="<EMAIL>" src="@FileUtil.JoinPath(DHSetting.Current.CurDomainUrl, item.PicturePath)">
                                </a>
                            </div>
                            @* <img src="@FileUtil.JoinPath(DHSetting.Current.CurDomainUrl, $"{item.PicturePath}")" onerror="" /> *@
                        }
                    </td>
                    <td>
                        <div class="onoff" data-id="@item.Id">
                            <label for="State1" onclick="State1('@item.Id')" class="cb-enable @(item.Enabled?"selected":"")">@T("是")</label>
                            <label for="State0" onclick="State0('@item.Id')" class="cb-disable @(!item.Enabled?"selected":"")">@T("否")</label>
                            <input id="State1" name="State1" value="true" data-id="@item.Id" type="radio" @(item.Enabled ? "checked" : "")>
                            <input id="State0" name="State0" value="false" data-id="@item.Id" type="radio" @(!item.Enabled ? "checked" : "")>
                        </div>
                    </td>
                    <td>@(UnixTime.ToDateTime(item.StartTime).ToString("yyyy-MM-dd"))</td>
                    <td>@(UnixTime.ToDateTime(item.EndTime).ToString("yyyy-MM-dd"))</td>
                    <td>
                        <a href="javascript:dsLayerOpen('@Url.Action("EditAdvertising", new { Id= item.Id })','@T("编辑广告")','900px','700px')" class="dsui-btn-edit">
                            <i class="iconfont"></i>@T("编辑")
                        </a>
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("DeleteAdvertising")?ids=' + @item.Id + '','@T("您确定要删除吗?")')" class="dsui-btn-del">
                            <i class="iconfont"></i>@T("删除")
                        </a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small"
                                   onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>

<script asp-location="Footer">
    function submit_delete(items) {
        _uri = "@Url.Action("DeleteAdvertising")?ids=" + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
    }
        function State1(id) {
        changeCheckboxStatus(id,true)
    }
    function State0(id) {
        changeCheckboxStatus(id,false)
    }
                // 改变选择状态
    const changeCheckboxStatus = (Id,status)=>{
        $.post("@Url.Action("EnableAdvertising")",{Id,status},function (res) {
            if (res.success) {
                if (status) {
                        layui.layer.msg('启用成功')
                    }else{
                        layui.layer.msg('已取消')
                    }
                    return;
                }
                layui.layer.msg(res.msg)
                debugger;
            }).fail(function(err) {
                console.log('选择失败！',err);
                // 在这里处理错误请求或者代码报错
               layui.layer.msg('选择失败！')
            });
        }
</script>
<style>
    img {
        object-fit: contain;
        max-width: 100px;
        max-height: 100px;
    }
</style>