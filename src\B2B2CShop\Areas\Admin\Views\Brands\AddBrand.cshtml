﻿
<div class="page">
    <form id="brand_form" method="post" name="form1" enctype="multipart/form-data">
        <input type="hidden" name="brand_id" value="" />
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120"><label class="validation">品牌名称:</label></td>
                    <td class="vatop rowform"><input type="text" value="" name="brandName" id="brand_name" class="txt">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required"><label class="validation">名称首字母:</label></td>
                    <td class="vatop rowform"><input type="text" value="" name="brandInitial" id="brand_initial"
                            class="txt"></td>
                    <td class="vatop tips">商家发布商品快捷搜索品牌使用!</td>
                </tr>
                <tr class="noborder">
                    <td class="required">所属分类: </td>
                    <td class="vatop">
                        <div id="gcategory" class="default_select">
                            @* 分类checkbox的值（用逗号隔开） *@
                        <input type="hidden" name="subClassificationId" value="0" class="mls_id" id="subClassificationId" />
                        <input type="hidden" value="" name="brand_class" class="mls_name">
                        <span class="mr10"></span>
                        <select class="class-select" id="selectTypeId" name="rubbish" data-index="0">
                            <option value="0">请选择</option>
                        </select>
                        </div>
                       
                    </td>
                    <td class="vatop tips">选择分类，可关联大分类或更具体的下级分类。</td>
                </tr>
                <tr class="noborder">
                    <td class="required">品牌图片标识: </td>
                    <td class="vatop rowform">
                        <span class="type-file-box">
                                <input type='text' name='_pic' id='brand_pic' class='type-file-text' />
                                <input type='button' id="button" name="button" value='@T("上传")' class='type-file-button' />
                                <input name="brandPic" type="file" class="type-file-file" id="_pic" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                        </span>
                    </td>
                    <td class="vatop tips">品牌LOGO尺寸要求宽度为150像素，高度为50像素、比例为3:1的图片；支持格式gif,jpg,png</td>
                </tr>
                <tr class="noborder">
                    <td class="required">展示方式: </td>
                    <td class="vatop rowform">
                        <input id="brand_showtype_0" type="radio" checked value="0" style="margin-bottom:6px;"
                            name="showType" />
                        <label for="brand_showtype_0">图片</label>
                        <input id="brand_showtype_1" type="radio" value="1" style="margin-bottom:6px;"
                            name="showType" />
                        <label for="brand_showtype_1">文字</label>
                    </td>
                    <td class="vatop tips">
                        在&ldquo;全部品牌&rdquo;页面的展示方式，如果设置为&ldquo;图片&rdquo;则显示该品牌的&ldquo;品牌图片标识&rdquo;，如果设置为&ldquo;文字&rdquo;则显示该品牌的&ldquo;品牌名&rdquo;!
                    </td>
                </tr>
                <tr class="noborder">
                    <td class="required">是否推荐: </td>
                    <td class="vatop rowform onoff"><label for="brand_recommend1" class="cb-enable selected"
                            title="是"><span>是</span></label>
                        <label for="brand_recommend0" class="cb-disable " title="否"><span>否</span></label>
                        <input id="brand_recommend1" name="brandRecommend" checked="checked" value="1" type="radio">
                        <input id="brand_recommend0" name="brandRecommend" value="0" type="radio">
                    </td>
                    <td class="vatop tips">选择被推荐的图片将在所有品牌列表页&ldquo;推荐品牌&rdquo;位置展现。</td>
                </tr>
                <tr>

                </tr>
                <tr class="noborder">
                    <td class="required">排序: </td>
                    <td class="vatop rowform"><input type="text" value="0" name="sort" id="brand_sort"
                            class="txt"></td>
                    <td class="vatop tips">数字范围为0~255，数字越小越靠前</td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="提交" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script src="/static/plugins/mlselection.js"></script>
<script>
    $("#_pic").change(function () {
        $("#brand_pic").val($(this).val());
    });
    function call_back(picname) {
        $('#brand_pic').val(picname);
        $('#view_img').attr('src', 'http://b2b2c.h.com/uploads/home/<USER>/' + picname);
    }
    //按钮先执行验证再提交表单
    $(function () {
        // 编辑分类时清除分类信息
        $('.edit_gcategory').click(function () {
            $('input[name="subClassificationId"]').val('');
            $('input[name="brand_class"]').val('');
        });


        jQuery.validator.addMethod("initial", function (value, element) {
            return /^[A-Za-z0-9]$/i.test(value);
        }, "");
        $("#brand_form").validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                brand_name: {
                    required: true,
                    remote: {
                        url: "@Url.Action("ChangeName")?branch=check_brand_name",
                        type: 'get',
                        data: {
                            brand_name: function () {
                                return $('#brand_name').val();
                            },
                            id: ''
                        }
                    }
                },
                brand_initial: {
                    initial: true
                },
                brand_sort: {
                    number: true,
                    range: [0, 255]
                }
            },
            messages: {
                brand_name: {
                    required: '品牌名称不能为空',
                    remote: '该品牌名称已经存在了，请您换一个'
                },
                brand_initial: {
                    initial: '请填写首字母',
                },
                brand_sort: {
                    number: '排序仅可以为数字',
                    range: '数字范围为0~255，数字越小越靠前'
                }
            }
        });
    });

    initSelection('selectTypeId');
</script>