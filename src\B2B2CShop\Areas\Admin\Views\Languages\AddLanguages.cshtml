﻿@{
    // script
    PekHtml.AppendScriptParts(ResourceLocation.Footer, "/static/plugins/js/layui/layui.js");

    // css
    PekHtml.AppendCssFileParts("/static/plugins/js/layui/css/layui.css");
}
<style asp-location="true">
        .red {
            color: red
        }
        @if (Language?.UniqueSeoCode == "en")
        {
            <text>
                .layui-form-label {
                    width: 100px !important;
                }

                .layui-textarea {
                    width: 90% !important;
                }

                .layui-input-block.btndiv {
                    padding-right: 40px;
                }
            </text>
        }
        .layui-input-block.btndiv {
            text-align: center;
        }
    form#doc_form {
        padding-top: 20px !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("多语言")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>
    <form id="doc_form" method="post">
        <input type="hidden" name="document_id" value="1" />
        <div class="layui-form-item">
            <label class="layui-form-label"><span class="red">*</span>@T("翻译键")</label>
            <div class="layui-input-inline">
                <input type="text" id="LanKey" name="LanKey" autocomplete="off" class="w830" value="">
            </div>
        </div>
        @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
        {
            <div class="layui-form-item layui-form-text">
                <label for="" class="layui-form-label">@item.Name</label>
                <div class="layui-input-block">
                    <input type="hidden" name="[@item.Id].Id" value="@item.Id" />
                    <input type="hidden" name="[@item.Id].LanKey" />
                    <input type="hidden" name="[@item.Id].LanType" />
                    <textarea placeholder="@T("请输入内容")" class="layui-textarea" name="[@item.Id].LanValue"></textarea>
                </div>
                <div class="col-sm-3"></div>
            </div>
        }
        <div>
            <input class="btn" type="submit" value="@T("提交")" />
        </div>
    </form>
</div>
<script asp-location="Footer">
        $('#doc_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().prev().find('td:first'));
            },
            rules: {
                LanKey: {
                    required: true
                }
            },
            messages: {
                LanKey: {
                    required: '@T("翻译键不能为空")'
                }
            }
        });
</script>