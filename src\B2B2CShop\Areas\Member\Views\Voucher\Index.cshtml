@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/integralFilter.css");
}
<!-- 积分中心代金券 更多筛选 -->
<div class="integral">
    <div class="vinfo">
        <div class="avatarBox">
            <img class="avatar" src="/public/images/icons/morentouxiang.png" alt="">
        </div>
        <span>137*****5694</span>
        <div class="textBox">
            <div class="text">
                <p>可用积分</p>
                <p class="num">34</p>
            </div>
            <div class="text">
                <p>余额</p>
                <p class="num">0.00</p>
            </div>
        </div>
    </div>
</div>
<div class="voucherBox">

    @* 热门店铺代金券 *@
    <div class="voucher">
        <!-- 头部筛选 -->
     <div class="filter">
         
     </div>
    </div>
    <div class="threeBox">
        <div class="voucherBg">
            <div class="voucherBg-left hot">
                <img class="shop-avatar" src="/public/images/icons/morentouxiang.png" alt="">
                <div>
                    <p class="p1">￥ <span class="s1">3</span> </p>
                    <p class="p2">满30元可用</p>
                    <p>需0积分</p>
                </div>
            </div>
            <div class="voucherBg-right">
                <p>立</p>
                <p>即</p>
                <p>兑</p>
                <p>换</p>
            </div>
        </div>
        <div class="voucherBg">
            <div class="voucherBg-left hot">
                <img class="shop-avatar" src="/public/images/icons/morentouxiang.png" alt="">
                <div>
                    <p class="p1">￥ <span class="s1">3</span> </p>
                    <p class="p2">满30元可用</p>
                    <p>需0积分</p>
                </div>
            </div>
            <div class="voucherBg-right">
                <p>立</p>
                <p>即</p>
                <p>兑</p>
                <p>换</p>
            </div>
        </div>
        <div class="voucherBg">
            <div class="voucherBg-left hot">
                <img class="shop-avatar" src="/public/images/icons/morentouxiang.png" alt="">
                <div>
                    <p class="p1">￥ <span class="s1">3</span> </p>
                    <p class="p2">满30元可用</p>
                    <p>需0积分</p>
                </div>
            </div>
            <div class="voucherBg-right">
                <p>立</p>
                <p>即</p>
                <p>兑</p>
                <p>换</p>
            </div>
        </div>
        <div class="voucherBg">
            <div class="voucherBg-left hot">
                <img class="shop-avatar" src="/public/images/icons/morentouxiang.png" alt="">
                <div>
                    <p class="p1">￥ <span class="s1">3</span> </p>
                    <p class="p2">满30元可用</p>
                    <p>需0积分</p>
                </div>
            </div>
            <div class="voucherBg-right">
                <p>立</p>
                <p>即</p>
                <p>兑</p>
                <p>换</p>
            </div>
        </div>
    </div>

</div>
<div class="bug"></div>
