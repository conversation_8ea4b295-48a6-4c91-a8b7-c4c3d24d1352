﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    var localizationSettings = LocalizationSettings.Current;
    var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
}
<div class="page">
    <form id="membertag_form" method="post" enctype="multipart/form-data">
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准")</li>
                    @foreach (var item in LanguageList)
                    {
                        <li data="@item.Id">@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td class="required w120"><label class="validation" for="levelname">@T("分销员等级:")</label>
                                </td>
                                <td class="vatop rowform"><input type="text" value="" name="levelname" id="levelname"
                                        class="txt"></td>
                                <td class="vatop tips"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in LanguageList)
                    {
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td class="required w120"><label for="<EMAIL>">@T("分销员等级:")</label></td>
                                        <td class="vatop rowform"><input type="text" value="" name="[@item.Id].levelname"
                                                id="<EMAIL>" class="txt"></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
            <table class="ds-default-table">
                <tbody>
                    <tr class="noborder">
                        <td class="required"><label class="validation" for="amount">@T("佣金门槛:")</label></td>
                        <td class="vatop rowform"><input type="text" value="" name="amount" id="amount" class="txt">
                        </td>
                        <td class="vatop tips">@T("分销员历史已结算佣金达到此金额后，自动升级到对应等级")</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="2"><input class="btn" type="submit" value='@T("提交")' /></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </form>
</div>
<script>
    (function () {
        var $form = $('#membertag_form');
        if (!$form.length) return;
        $form.on('submit', function (e) {
            e.preventDefault();
            var form = this;
            var url = $(form).attr('action');
            var fd = new FormData(form);
            // disable submit button
            var $btn = $(form).find('input[type=submit]');
            $btn.prop('disabled', true);
            $.ajax({
                url: url,
                type: 'POST',
                data: fd,
                processData: false,
                contentType: false,
                success: function (res) {
                    var msg = (res && res.msg) ? res.msg : '@T("操作完成")';
                    if (res && res.success) {
                        // show success message then close parent layer and refresh parent
                        layer.msg(msg, { icon: 1, time: 1200 }, function () {
                            try {
                                if (parent && parent.layer) {
                                    var idx = parent.layer.getFrameIndex(window.name);
                                    parent.layer.close(idx);
                                    try { parent.location.reload(); } catch (e) { }
                                } else if (window.opener) {
                                    try { window.opener.location.reload(); } catch (e) { }
                                    try { window.close(); } catch (e) { }
                                } else {
                                    try { parent.location.reload(); } catch (e) { try { window.location.reload(); } catch (ee) { } }
                                }
                            } catch (e) {
                                try { window.close(); } catch (ee) { }
                            }
                        });

                    } else {
                        layer.msg(msg,{ icon: 2 });
                        $btn.prop('disabled', false);
                    }
                },
                error: function () {
                    var err = '@T("请求失败，请重试")';
                    layer.msg(err);
                    $btn.prop('disabled', false);
                }
            });
        });
    })();
</script>