﻿using Microsoft.AspNetCore.Mvc;
using Pek.DsMallUI;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    [DisplayName("批发管理")]
    [Description("商品批发管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/Wholesale", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/Wholesale", CurrentMenuName = "WholesaleList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class WholesaleController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("批发商品列表")]
        public IActionResult Index()
        {
            return View();
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("批发商品详情")]
        public IActionResult WholesaleGoodsDetail()
        {
            return View();
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("批发套餐管理")]
        public IActionResult WholesalePackage()
        {
            return View();
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("批发套餐设置")]
        public IActionResult WholesaleSetting()
        {
            return View();
        }
    }
}
