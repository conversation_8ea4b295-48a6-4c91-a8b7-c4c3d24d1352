﻿using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Log;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Models;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Webs;
using System.ComponentModel;
using System.Dynamic;
using XCode;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>商品分类</summary>
[DisplayName("商品分类")]
[Description("用于商品分类的管理，最多只有3级")]
[AdminArea]
[DHMenu(100, ParentMenuName = "Products", ParentMenuDisplayName = "商品", ParentMenuUrl = "~/{area}/ProductCategory", ParentMenuOrder = 85, CurrentMenuUrl = "~/{area}/ProductCategory", CurrentMenuName = "ProductCategoryList", CurrentIcon = "&#xe652;", LastUpdate = "20241203")]
public class ProductCategoryController : PekCubeAdminControllerX {

    /// <summary>
    /// 商品分类列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("商品分类列表")]
    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();
        var list = GoodsClass.FindAllByParentId(0);
        foreach (var item in list)
        {
            var Model = GoodsClass.FindAllByParentId(item.Id);
            if (Model.Count() != 0)
            {
                item.subset = true;
            }
        }
        viewModel.list = list;
        return View(viewModel);
    }

    /// <summary>
    /// 获取商品分类表下级数据
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取商品分类表下级数据")]
    public IActionResult GetSubordinateData(string Id)
    {
        var zList = new List<HierarchyLong>();
        if (Id.IsNullOrWhiteSpace())
        {
            return Json(new { });
        }
        var list = GoodsClass.FindAllByParentId(Id.ToLong());
        foreach (var item in list)
        {
            var model = new HierarchyLong();
            model.gc_name = item.Name;
            model.gc_id = item.Id.ToString();
            model.gc_parent_id = item.ParentId.ToString();
            var exc = GoodsClass.FindAllByParentId(item.Id);
            if (exc.Count() > 0)
            {
                model.have_child = 1;
            }
            model.gc_show = 1;
            model.gc_sort = item.Sort;
            model.deep = item.Level;
            model.commis_rate = item.CommisRate;
            model.type_name = item.GoodTypeName;
            model.gc_virtual = item.IsVirtual ? 1 : 0;
            model.gc_enable = item.Enable ? 1 : 0;
            zList.Add(model);
        }
        return Json(new { zList });
    }

    /// <summary>
    /// 修改列表字段值
    /// </summary>
    /// <param name="value">修改名称</param>
    /// <param name="Id">分类编号</param>
    /// <param name="column">字段名</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("修改列表字段值")]
    public IActionResult ChangeName(String value, string Id, String column)
    {
        if (value.IsNullOrWhiteSpace()) return Json(false);

        var Models = GoodsClass.FindById(Id.ToLong());

        if (Models == null) return Json(false);

        if (column == "gc_name")
        {
            var Model = GoodsClass.FindByName(value);
            if (Model != null && Model.Id != Id.ToLong())
            {
                return Json(false);
            }

            Models.Name = value;
        }
        else if (column == "gc_sort")
        {
            Models.Sort = value.ToDGShort();
        }
        else
        {
            return Json(false);
        }

        Models.Update();

        return Json(true);
    }

    /// <summary>
    /// 删除分类
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();

        var list = GoodsClass.FindByIds(Ids);
        var dellist = new List<GoodsClass>();

        using (var tran = GoodsClass.Meta.CreateTrans())
        {
            res = DeleteArticleCategory(res, dellist, list);

            if (!res.msg.IsNullOrWhiteSpace())
            {
                return Json(res);
            }

            if (dellist.Delete(true) > 0)
            {
                GoodsClass.Meta.Cache.Clear("");
            }

            // 删除多语言表中的数据
            foreach (var item in dellist)
            {
                GoodsClassLan.DeleteByGId(item.Id);
            }
            GoodsClassLan.Meta.Cache.Clear("");

            // 更新商品的分类状态
            UpdateProductState(dellist);
            tran.Commit();
        }

        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 循环删除多级数据
    /// </summary>
    /// <param name="res"></param>
    /// <param name="dellist"></param>
    /// <param name="list"></param>
    /// <returns></returns>
    private DResult DeleteArticleCategory(DResult res, IList<GoodsClass> dellist, IEnumerable<GoodsClass> list)
    {
        if (list.Count() > 0)
        {
            foreach (var item in list)
            {
                dellist.Add(item);
                var childlist = GoodsClass.FindAllByParentId(item.Id);
                res = DeleteArticleCategory(res, dellist, childlist);
            }
        }
        return res;
    }

    /// <summary>
    /// 更新商品的分类状态
    /// </summary>
    /// <param name="categories"></param>
    private void UpdateProductState(IEnumerable<GoodsClass> categories)
    {
        foreach (var category in categories)
        {
            //var goods = Goods.FindAllByCategoryId(category.Id);
            //foreach (var item in goods)
            //{
            //    item.State = 10;
            //    item.Update();
            //}

            //var goodsCommon = GoodsCommon.FindAllByCategoryId(category.Id);
            //foreach (var item in goodsCommon)
            //{
            //    item.State = 10;
            //    item.Stateremark = "商品分类被删除，需要重新选择分类";
            //    item.Update();
            //}
        }
    }

    /// <summary>
    /// 打开编辑页面
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("打开编辑商品分类页面")]
    public IActionResult EditProductCategory(string Id)
    {
        var Model = GoodsClass.FindById(Id.ToLong());
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }

        var List = new List<GoodsClass>();
        var live1 = GoodsClass.FindAllByLevel(0); //1级数据
        GetCategoryList(live1, List);
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        ViewBag.Plist = List;

        //获取商品类型
        ViewBag.GoodsTypeList = GoodType.FindAll();
        
        return View(Model);
    }

    /// <summary>
    /// 编辑商品分类
    /// </summary>
    /// <param name="isVirtual"></param>
    /// <param name="related1">关联到子分类</param>
    /// <param name="commisRate"></param>
    /// <param name="related2"></param>
    /// <param name="goodTypeId"></param>
    /// <param name="related3"></param>
    /// <param name="id"></param>
    /// <param name="name"></param>
    /// <param name="title"></param>
    /// <param name="keywords"></param>
    /// <param name="description"></param>
    /// <param name="parentId"></param>
    /// <param name="sort"></param>
    /// <param name="claPic"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("编辑商品分类")]
    public IActionResult EditProductCategory(string isVirtual, string related1, double commisRate, string related2, int goodTypeId, string related3, string id, string name, string title, string keywords, string description, string parentId, short sort,int isEnabled, IFormFile claPic)
    {
        if (name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("分类名称不能为空！") });
        }
        var Model = GoodsClass.FindById(id.ToLong());
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在！") });
        }
        if (Model.Level == 0 && parentId.ToLong() != 0)
        {
            return Prompt(new PromptModel { Message = GetResource("一级分类不能修改父级分类！") });
        }
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除！") });
        }
        var oldName = Model.Name;
        using (var tran1 = GoodsClassLan.Meta.CreateTrans())
        {
            var EXIT = GoodsClass.FindByName(name);
            if (EXIT != null && EXIT.Id != id.ToLong())
            {
                return Prompt(new PromptModel { Message = GetResource("分类名称已存在！") });
            }

            Model.Name = name.SafeString().Trim();
            Model.Title = title.SafeString().Trim();
            Model.Keywords = keywords.SafeString().Trim();
            Model.Description = description.SafeString().Trim();
            Model.Sort = sort;
            Model.Enable = isEnabled == 1;
            XTrace.WriteLine("parentId:" + parentId);
            XTrace.WriteLine("Model.ParentId:" + Model.ParentId);
            if (parentId.ToLong() != Model.ParentId)
            {
                if (Model.Level == 1 && (parentId.ToLong() == 0 || GoodsClass.FindById(parentId.ToLong())?.Level <= 0))
                {
                    Model.ParentId = parentId.ToLong();
                }
                else if (Model.Level == 2 && (parentId.ToLong() == 0 || GoodsClass.FindById(parentId.ToLong())?.Level <= 1))
                {
                    Model.ParentId = parentId.ToLong();
                }
                else
                {
                    return Prompt(new PromptModel { Message = GetResource("分类最多存在三级,修改失败！") });
                }
            }

            Model.IsVirtual = isVirtual == "on";
            Model.CommisRate = commisRate;
            Model.GoodTypeId = goodTypeId;
            if (related1 == "on" || related2 == "on" || related3 == "on")
            {
                var list = GoodsClass.FindAllByParentId(id.ToLong());
                foreach (var item in list)
                {
                    if (related1 == "on")
                    {
                        item.IsVirtual = isVirtual == "on";
                    }
                    if (related2 == "on")
                    {
                        item.CommisRate = commisRate;
                    }
                    if (related3 == "on")
                    {
                        item.GoodTypeId = goodTypeId;
                    }
                    item.Update();
                    var list1 = GoodsClass.FindAllByParentId(item.Id);
                    foreach (var item1 in list1)
                    {
                        if (related1 == "on")
                        {
                            item1.IsVirtual = isVirtual == "on";
                        }
                        if (related2 == "on")
                        {
                            item1.CommisRate = commisRate;
                        }
                        if (related3 == "on")
                        {
                            item1.GoodTypeId = goodTypeId;
                        }
                        item1.Update();
                    }
                }
            }
            Model.Update();

            if (claPic != null)
            {
                var bytes = claPic.OpenReadStream().ReadBytes(claPic.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }

                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic.FileName)}";
                var filepath = $"GoodsClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                saveFileName.EnsureDirectory();
                claPic.SaveAs(saveFileName);
                Model.Image = filepath.Replace("\\", "/");

                Model.Update();
            }

            var localizationSettings = LocalizationSettings.Current;
            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var filea = Request.Form.Files;
                var list = filea.Count();

                var lanlist = GoodsClassLan.FindAllByGId(Model.Id);
                foreach (var item in Languagelist)
                {
                    var aaaa = lanlist.Find(x => x.LId == item.Id);
                    if (aaaa == null)
                    {
                        aaaa = new GoodsClassLan();
                    }
                    aaaa.GId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                    aaaa.Title = (GetRequest($"[{item.Id}].title")).SafeString().Trim();
                    aaaa.Keywords = (GetRequest($"[{item.Id}].keywords")).SafeString().Trim();
                    aaaa.Description = (GetRequest($"[{item.Id}].description")).SafeString().Trim();
                    aaaa.Save();

                    var file = filea.Where(x => x.Name == $"[{item.Id}].claPic").FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                        var filepath = $"GoodsClassLan/{filename}";
                        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                        filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        aaaa.Image = filepath;

                        aaaa.Update();
                    }
                }
            }
            tran1.Commit();
            //Loger.UserLog("修改分类", $"修改产品分类名称{oldName}=>{Model.Name},父级分类=> {parentName}");
        }
        GoodsClassLan.Meta.Cache.Clear("");
        GoodsClass.Meta.Cache.Clear("");
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 获取分类集合
    /// </summary>
    /// <param name="levelList"></param>
    /// <param name="list"></param>
    private void GetCategoryList(IEnumerable<GoodsClass> levelList, IList<GoodsClass> list)
    {
        if (levelList.Count() > 0)
        {
            foreach (var item in levelList)
            {
                list.Add(item);

                var level = GoodsClass.FindAllByParentId(item.Id);
                GetCategoryList(level, list);
            }
        }
    }

    /// <summary>
    /// 查询名称是否存在
    /// </summary>
    /// <param name="Key"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("查询名称是否存在")]
    public IActionResult ExistName(string Key, string Id)
    {
        XTrace.WriteLine("获取到的打印key==" + Key);
        XTrace.WriteLine("获取到的打印Id==" + Id);
        if (Key.IsNullOrWhiteSpace())
        {
            return Json(false);
        }
        if (Id.ToLong() != 0)
        {
            var EXIST = GoodsClass.FindByName(Key);
            if (EXIST != null && EXIST.Id != Id.ToLong())
            {
                return Json(false);
            }
        }
        else
        {
            var EXIST = GoodsClass.FindByName(Key);
            if (EXIST != null)
            {
                return Json(false);
            }
        }
        return Json(true);
    }

    /// <summary>
    /// 打开新增商品分类页面
    /// </summary>
    /// <param name="parent_id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("打开新增商品分类页面")]
    public IActionResult AddProductCategory(string parent_id)
    {

        var List = new List<GoodsClass>();
        var live1 = GoodsClass.FindAllByParentId(0);
        GetCategoryList(live1, List);
        ViewBag.Plist = List;

        var DisplayOrder = 0;
        if (parent_id.ToLong() != 0)
        {
            DisplayOrder = GoodsClass.FindMax("Sort", GoodsClass._.ParentId == parent_id).ToInt();
        }
        else
        {
            DisplayOrder = GoodsClass.FindMax("Sort").ToInt();
        }

        ViewBag.DisplayOrder = DisplayOrder + 1;
        ViewBag.ParentId = parent_id.ToLong();
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

        //获取商品类型
        ViewBag.GoodsTypeList = GoodType.FindAll();

        return View();
    }

    /// <summary>
    /// 新增商品分类
    /// </summary>
    /// <param name="isVirtual"></param>
    /// <param name="commisRate"></param>
    /// <param name="goodTypeId"></param>
    /// <param name="name"></param>
    /// <param name="title"></param>
    /// <param name="keywords"></param>
    /// <param name="description"></param>
    /// <param name="parentId"></param>
    /// <param name="sort"></param>
    /// <param name="claPic"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("新增商品分类")]
    public IActionResult AddProductCategory(string isVirtual, double commisRate, int goodTypeId, string name, string title, string keywords, string description, string parentId, short sort, int isEnabled, IFormFile claPic)
    {
        XTrace.WriteLine("编辑商品分类Name==" + name);
        XTrace.WriteLine("编辑商品分类DisplayOrder==" + sort);
        XTrace.WriteLine("编辑商品分类ParentId==" + parentId);
        if (name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("分类名称不能为空！") });
        }

        var exit = GoodsClass.FindByName(name.SafeString().Trim());
        if (exit != null)
        {
            return Prompt(new PromptModel { Message = GetResource("分类名称已存在！") });
        }
        var Pmodel = GoodsClass.FindById(parentId.ToLong());
        if (Pmodel != null)
        {
            if (Pmodel.Level >= 2)
            {
                return Prompt(new PromptModel { Message = GetResource("分类最多存在三级,创建失败！") });
            }
        }
        using (var tran1 = GoodsClassLan.Meta.CreateTrans())
        {
            var Model = new GoodsClass();
            Model.Name = name.SafeString().Trim();
            Model.Title = title.SafeString().Trim();
            Model.Keywords = keywords.SafeString().Trim();
            Model.Description = description.SafeString().Trim();
            Model.ParentId = parentId.ToLong();
            Model.IsVirtual = isVirtual == "on";
            Model.CommisRate = commisRate;
            Model.GoodTypeId = goodTypeId;
            Model.Sort = sort;
            Model.Enable = isEnabled == 1;
            Model.Insert();

            if (claPic != null)
            {
                var bytes = claPic.OpenReadStream().ReadBytes(claPic.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }

                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic.FileName)}";
                var filepath = $"GoodsClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                saveFileName.EnsureDirectory();
                claPic.SaveAs(saveFileName);
                Model.Image = filepath.Replace("\\", "/");

                Model.Update();
            }

            var localizationSettings = LocalizationSettings.Current;
            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var filea = Request.Form.Files;
                var list = filea.Count();

                foreach (var item in Languagelist)
                {
                    var ex = new GoodsClassLan();
                    ex.GId = Model.Id;
                    ex.LId = item.Id;
                    ex.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                    ex.Title = (GetRequest($"[{item.Id}].title")).SafeString().Trim();
                    ex.Keywords = (GetRequest($"[{item.Id}].keywords")).SafeString().Trim();
                    ex.Description = (GetRequest($"[{item.Id}].description")).SafeString().Trim();
                    ex.Insert();

                    var file = filea.Where(e => e.Name == $"[{item.Id}].claPic").FirstOrDefault();
                    //XTrace.WriteLine("打印多语言file个数" + list);
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                        var filepath = $"GoodsClassLan/{filename}";
                        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                        filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        ex.Image = filepath.Replace("\\", "/");

                        ex.Update();
                    }
                }
            }
            tran1.Commit();
        }
        GoodsClassLan.Meta.Cache.Clear("");
        GoodsClass.Meta.Cache.Clear("");
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 设置分类别名和推广
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("设置分类别名和推广")]
    public IActionResult SetAliasAndPromotion(string id)
    {
        var Model = GoodsClass.FindById(id.ToLong());//一级实体
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }
        if(Model.ParentId != 0)
        {
            return MessageTip(GetResource("只有一级分类才能设置别名和推广"));
        }
        var list2 = GoodsClass.FindAllByParentId(id.ToLong());//二级列表
        var list3 = new List<GoodsClass>();
        var list = new Dictionary<GoodsClass, IList<GoodsClass>>();
        foreach (var item in list2)
        {
            list.Add(item, GoodsClass.FindAllByParentId(item.Id));
        }
        ViewBag.List = list;
        var brandList = Brand.FindAll();
        ViewBag.BrandList = brandList;

        return View(Model);
    }

    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("设置分类别名和推广")]
    public IActionResult SetAliasAndPromotion(string id, string aliasName, string classIds, string brandIds, IFormFile Adv1, string Adv1Link, IFormFile Adv2, string Adv2Link)
    {
        var Model = GoodsClass.FindById(id.ToLong());
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));
        }
        if (Model.ParentId != 0)
        {
            return MessageTip(GetResource("只有一级分类才能设置别名和推广"));
        }
        using (var tran1 = GoodsClassLan.Meta.CreateTrans())
        {
            Model.AliasName = aliasName;
            Model.ClassIds = classIds;
            Model.BrandIds = brandIds;

            if (Adv1 != null)
            {
                var bytes = Adv1.OpenReadStream().ReadBytes(Adv1.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }

                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(Adv1.FileName)}";
                var filepath = $"GoodsClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                saveFileName.EnsureDirectory();
                Adv1.SaveAs(saveFileName);
                Model.Adv1 = filepath.Replace("\\", "/");

                Model.Update();
            }
            Model.Adv1Link = Adv1Link;

            if (Adv2 != null)
            {
                var bytes = Adv2.OpenReadStream().ReadBytes(Adv2.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(Adv2.FileName)}";
                var filepath = $"GoodsClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                Adv2.SaveAs(saveFileName);
                Model.Adv2 = filepath.Replace("\\", "/");
                Model.Update();
            }
            Model.Adv2Link = Adv2Link;
            Model.Update();

            var localizationSettings = LocalizationSettings.Current;
            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

                var lanlist = GoodsClassLan.FindAllByGId(Model.Id);
                foreach (var item in Languagelist)
                {
                    var aaaa = lanlist.Find(x => x.LId == item.Id);
                    if (aaaa == null)
                    {
                        aaaa = new GoodsClassLan();
                    }
                    aaaa.GId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.AliasName = (GetRequest($"[{item.Id}].aliasName")).SafeString().Trim();
                    aaaa.Save();
                }
            }
            tran1.Commit();
        }
        GoodsClassLan.Meta.Cache.Clear("");
        GoodsClass.Meta.Cache.Clear("");

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 查询商品分类
    /// </summary>
    /// <param name="pid"></param>
    /// <returns></returns>
    [DisplayName("查询商品分类")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetGoodsClass(string pid)
    {
        var goodsClass = GoodsClass.FindAllByParentId(pid.ToLong());

        return Json(goodsClass.Select(gc => new { Id = gc.Id.ToString(), gc.Name }));
    }

    /// <summary>
    /// 商品分类树
    /// </summary>
    /// <returns></returns>
    public IActionResult GetGoodsClassTree()
    {
        var list = GoodsClass.FindAllByLevel(0).Where(e=>e.Enable == true).Select(x =>
        {
            var classChild = GoodsClass.FindAllByParentId(x.Id).Where(e => e.Enable == true).OrderBy(e => e.Sort).Select(e => new
            {
                Id = e.Id.SafeString(),
                Name = (GoodsClassLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.Name).IsNullOrWhiteSpace() ? e.Name : GoodsClassLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.Name,
                Sum = MerchantMaterial.GetQuantityByClassId(0, e.Id)
            }).ToList();

            var classChildIds = classChild.Select(e => e.Id).ToList();

            return new
            {
                Id = x.Id.ToString(),
                Name = (GoodsClassLan.FindByGIdAndLId(x.Id, WorkingLanguage.Id)?.Name).IsNullOrWhiteSpace() ? x.Name : GoodsClassLan.FindByGIdAndLId(x.Id, WorkingLanguage.Id)?.Name,
                Child = classChild,
                Goods = Goods.FindAllByCid1(x.Id).Where(e=> classChildIds.Contains(e.Cid2.SafeString())).Select(e => new
                {
                    Id = e.Id.SafeString(),
                    Name = GoodsLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.LanName ?? e.Name,
                    GoodsImage = AlbumPic.FindByNameAndSId(GoodsLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.LanGoodsImage ?? e.GoodsImage ?? "", e.StoreId)?.Cover,
                    GoodsStorage = MerchantMaterial.GetQuantityByWIds(e.MaterialIds??""),
                })
            };
        });
        return Json(new {data = list});
    }
    [HttpPost]
    [DisplayName("设置分类是否启用")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult SetEnable(Int64 classId)
    {
        var model = GoodsClass.FindById(classId);
        if (model == null) return Json(new DResult() { success = false,msg = GetResource("找不到记录")});

        model.Enable = model.Enable ? false : true;
        model.Update();
        GoodsClass.Meta.Cache.Clear("", true);//清除缓存
        return Json(new DResult() { success = true ,status = model.Enable?1:0 });

    }
}

public class HierarchyLong
{
    public string? gc_id { get; set; }
    public string? gc_name { get; set; }
    public string? gc_parent_id { get; set; }
    public int have_child { get; set; }
    public int gc_show { get; set; }
    public int gc_sort { get; set; }
    public int deep { get; set; }
    public string? type_name { get; set; }
    public double commis_rate { get; set; }
    public int gc_virtual { get; set; }
    public int gc_enable { get; set; }
}