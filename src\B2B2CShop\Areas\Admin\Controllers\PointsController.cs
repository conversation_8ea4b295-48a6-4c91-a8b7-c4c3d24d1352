﻿using B2B2CShop.Dto;
using B2B2CShop.Entity;

using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using MiniExcelLibs;
using MiniExcelLibs.OpenXml;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;
using System.Dynamic;

using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>积分管理</summary>
[DisplayName("积分管理")]
[Description("用于设置积分管理")]
[AdminArea]
[DHMenu(50, ParentMenuName = "Members", CurrentMenuUrl = "~/{area}/Points", CurrentMenuName = "Pointssetting", CurrentIcon = "&#xe6f5;", LastUpdate = "20240125")]
public class PointsController : PekCubeAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 50;

    /// <summary>
    /// 积分明细
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("积分明细")]
    public IActionResult Index(String mname, String stime, String etime, String stage, String aname, String description, Int32 page = 1)
    {
        dynamic viewModel = new ExpandoObject();

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };

        mname = mname.SafeString().Trim();
        stime = stime.SafeString().Trim();
        etime = etime.SafeString().Trim();
        stage = stage.SafeString().Trim();
        aname = aname.SafeString().Trim();
        description = description.SafeString().Trim();

        viewModel.mname = mname;
        viewModel.stime = stime;
        viewModel.etime = etime;
        viewModel.stage = stage;
        viewModel.aname = aname;
        viewModel.description = description;

        var logList = PointsLog.Search(mname, stime.IsNullOrWhiteSpace() ? null : stime.ToDateTime(), etime.IsNullOrWhiteSpace() ? null : etime.ToDateTime(), stage, aname, description, pages).Select(e => new { e.UName, e.AdminName, e.Points, e.CreateTime, Stage = GetStage(e.Stage), e.Desc }).ToDynamicList();

        viewModel.PointsLogList = logList;

        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "mname", mname }, { "stime", stime }, { "etime", etime }, { "stage", stage }, { "aname", aname }, { "description", description } });

        return View(viewModel);
    }

    /// <summary>
    /// 积分规则设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("积分规则设置")]
    public IActionResult Setting()
    {
        return View();
    }

    /// <summary>
    /// 积分规则设置
    /// </summary>
    /// <param name="points_reg">会员注册</param>
    /// <param name="points_login">会员每天登录</param>
    /// <param name="points_comments">订单商品评论</param>
    /// <param name="points_signin">签到</param>
    /// <param name="points_invite">邀请注册</param>
    /// <param name="points_rebate">返利比例</param>
    /// <param name="points_orderrate">消费额与赠送积分比例</param>
    /// <param name="points_ordermax">每订单最多赠送积分</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("积分规则设置")]
    public IActionResult Setting(Int32 points_reg, Int32 points_login, Int32 points_comments, Int32 points_signin, Int32 points_invite, Int32 points_rebate, Int32 points_orderrate, Int32 points_ordermax)
    {
        var set = DHSetting.Current;
        set.PointsReg = points_reg;
        set.PointsLogin = points_login;
        set.PointsComments = points_comments;
        set.PointsSignin = points_signin;
        set.PointsInvite = points_invite;
        set.PointsRebate = points_rebate;
        set.PointsOrderrate = points_orderrate;
        set.PointsOrdermax = points_ordermax;
        set.Save();

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true });
    }

    /// <summary>
    /// 积分调整
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("积分调整")]
    public IActionResult PointsChange()
    {
        return View();
    }

    /// <summary>
    /// 获取指定会员积分
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取指定会员积分")]
    public IActionResult CheckMember(String name)
    {
        var res = new DResult();

        var model = UserE.FindByName(name);
        res.data = new { id = 0 };
        if (model == null) return Json(res);

        var modelUserDetail = UserDetail.FindById(model.ID);
        res.data = new { name = model.Name, points = modelUserDetail?.Points, id = model.ID };

        return Json(res);
    }

    /// <summary>
    /// 积分调整保存
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("积分调整保存")]
    [HttpPost]
    public IActionResult PointsChange(String member_name, Int32 member_id, Int32 points_type, Int32 points_num, String points_desc)
    {
        var model = UserE.FindByID(member_id);
        if (model == null) return Prompt(new PromptModel { Message = GetResource("会员不存在") });

        var modelUserDetail = UserDetail.FindById(model.ID);
        if (modelUserDetail == null)
        {
            modelUserDetail = new UserDetail
            {
                Id = model.ID
            };
            modelUserDetail.Insert();
        }

        var modelPointsLog = new PointsLog();

        if (points_type == 1)
        {
            modelUserDetail.Points += points_num;
            modelPointsLog.Points = points_num;
        }
        else
        {
            modelUserDetail.Points -= points_num;
            modelPointsLog.Points = points_num * (-1);
        }

        using (var trans1 = UserDetail.Meta.CreateTrans())
        {
            modelUserDetail.Update();

            modelPointsLog.UId = member_id;
            modelPointsLog.UName = member_name;
            modelPointsLog.AdminId = model.ID;
            modelPointsLog.AdminName = model.Name;

            if (!points_desc.IsNullOrWhiteSpace())
            {
                modelPointsLog.Desc = points_desc;
            }
            else
            {
                modelPointsLog.Desc = "调整积分";
            }

            modelPointsLog.Stage = "system";
            modelPointsLog.Insert();

            trans1.Commit();
        }
        UserDetail.Meta.Cache.Clear("");
        PointsLog.Meta.Cache.Clear("");

        return MessageTip(GetResource("积分调整成功"));
    }

    /// <summary>
    /// 积分日志导出
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("积分日志导出")]
    public IActionResult Export(String mname, String stime, String etime, String stage, String aname, String description)
    {
        var List = PointsLog.Search(mname, stime.IsNullOrWhiteSpace() ? null : stime.ToDateTime(), etime.IsNullOrWhiteSpace() ? null : etime.ToDateTime(), stage, aname, description, null);

        var listDto = new List<PointsLogExport>();

        List.ForEach(e =>
        {
            listDto.Add(new PointsLogExport
            {
                UName = e.UName,
                AdminName = e.AdminName,
                Points = e.Points,
                Times = e.CreateTime.ToFullString(),
                Stage = GetStage(e.Stage),
                Desc = e.Desc
            });
        });

        var memoryStream = new MemoryStream();
        memoryStream.SaveAs(listDto, true, "积分明细", ExcelType.XLSX, new OpenXmlConfiguration() { AutoFilter = false });
        memoryStream.Seek(0, SeekOrigin.Begin);

        return File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{"积分明细"}-{DateTime.Now:yyyyMMddhhmm}.xlsx");
    }

    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("积分签到设置")]
    public IActionResult SigninSetting()
    {
        dynamic viewModel = new ExpandoObject();
        viewModel.SignInGetPointsSwitch = OperationConfig.GetValueByCode("SignInGetPointsSwitch").ToInt();//签到送积分开关
        viewModel.SignInGetPoints = OperationConfig.GetValueByCode("SignInGetPoints").ToInt();//签到积分数
        viewModel.ContinuousSignInCycle = OperationConfig.GetValueByCode("ContinuousSignInCycle").ToInt();//连续签到周期
        viewModel.ContinuousSignInReward = OperationConfig.GetValueByCode("ContinuousSignInReward").ToInt();//连续签到奖励
        return View(viewModel);
    }

    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("积分签到设置")]
    [HttpPost]
    public IActionResult SigninSetting(bool isenable, int getpoints, int cycle, int reward)
    {
        if (getpoints < 0)
            return Json(new DResult() { success = false, msg = GetResource("签到积分不能为负数") });
        if (cycle < 0)
            return Json(new DResult() { success = false, msg = GetResource("连续签到周期不能为负数") });
        if (reward < 0)
            return Json(new DResult() { success = false, msg = GetResource("连续签到奖励不能为负数") });

        // 开关统一以 1/0 持久化，便于前端按数值判断
        var switchVal = isenable ? "1" : "0";
        var flag = OperationConfig.UpdateValueByCode("SignInGetPointsSwitch", switchVal,"签到送积分开关");
        if (!flag)
            return Json(new DResult() { success = false, msg = GetResource("签到送积分开关保存失败") });

        flag = OperationConfig.UpdateValueByCode("SignInGetPoints", getpoints.ToString(), "签到积分数");
        if (!flag)
            return Json(new DResult() { success = false, msg = GetResource("签到积分数保存失败") });
        flag = OperationConfig.UpdateValueByCode("ContinuousSignInCycle", cycle.ToString(), "连续签到周期");
        if (!flag)
            return Json(new DResult() { success = false, msg = GetResource("连续签到周期保存失败") });

        flag = OperationConfig.UpdateValueByCode("ContinuousSignInReward", reward.ToString(), "连续签到奖励");
        if (!flag)
            return Json(new DResult() { success = false, msg = GetResource("连续签到奖励保存失败") });

        // 刷新配置缓存
        OperationConfig.Meta.Cache.Clear("", true);

        return Json(new DResult() { success = true, msg = GetResource("设置成功") });
    }

    private String GetStage(String? stage)
    {
        switch (stage)
        {
            case "regist":
                return "注册";

            case "login":
                return "登录";

            case "comments":
                return "商品评论";

            case "order":
                return "订单消费";

            case "system":
                return "系统调整";

            case "pointorder":
                return "礼品兑换";

            case "exchange":
                return "积分兑换";

            case "signin":
                return "签到";

            case "inviter":
                return "推荐注册";

            case "rebate":
                return "推荐返利";
        }

        return "";
    }
}
