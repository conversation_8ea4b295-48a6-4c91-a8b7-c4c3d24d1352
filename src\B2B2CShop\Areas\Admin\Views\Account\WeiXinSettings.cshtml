﻿@model OAuthConfig
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("账号同步")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("设置")</span></a></li>
                <li><a href="@Url.Action("SSOSettings")"><span>@T("SSO登录")</span></a></li>
                <li><a href="@Url.Action("QQSettings")"><span>@T("QQ 互联")</span></a></li>
                <li><a href="@Url.Action("WeiXinSettings")" class="current"><span>@T("微信登录")</span></a></li>
                <li><a href="@Url.Action("OpenWeixinSettings")"><span>@T("微信开放平台")</span></a></li>
                <li><a href="@Url.Action("QyWeiXinSettings")"><span>@T("企业微信")</span></a></li>
                <li><a href="@Url.Action("DingSettings")"><span>@T("钉钉登录")</span></a></li>
                <li><a href="@Url.Action("GitHubSettings")"><span>@T("GitHub登录")</span></a></li>
                <li><a href="@Url.Action("BaiduSettings")"><span>@T("百度登录")</span></a></li>
                <li><a href="@Url.Action("MicrosoftSettings")"><span>@T("微软登录")</span></a></li>
            </ul>
        </div>
    </div>

    @using (Html.BeginForm("UpdateWeiXinSetting", "Account", FormMethod.Post, new { id = "weixin_form" }))
    {
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w150">@T("启用公众号登录功能")</td>
                    <td class="vatop rowform">
                        <div class="onoff">
                            <label for="IsEnabled1" class="cb-enable @(Model.Enable ? "selected" : "")">@T("是")</label>
                            <label for="IsEnabled0" class="cb-disable @(!Model.Enable ? "selected" : "")">@T("否")</label>
                            <input id="IsEnabled1" name="IsEnabled" value="1" type="radio" @(Model.Enable ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                            <input id="IsEnabled0" name="IsEnabled" value="0" type="radio" @(!Model.Enable ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        </div>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w150">@T("启用微信扫码登录功能")</td>
                    <td class="vatop rowform">
                        <div class="onoff">
                            <label for="WeiXinQRCodeEnabled1" class="cb-enable @(DHSetting.Current.WeiXinQRCodeEnabled ? "selected" : "")">@T("是")</label>
                            <label for="WeiXinQRCodeEnabled0" class="cb-disable @(!DHSetting.Current.WeiXinQRCodeEnabled ? "selected" : "")">@T("否")</label>
                            <input id="WeiXinQRCodeEnabled1" name="WeiXinQRCodeEnabled" value="1" type="radio" @(DHSetting.Current.WeiXinQRCodeEnabled ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                            <input id="WeiXinQRCodeEnabled0" name="WeiXinQRCodeEnabled" value="0" type="radio" @(!DHSetting.Current.WeiXinQRCodeEnabled ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        </div>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("启用调试")</td>
                    <td class="vatop rowform">
                        <div class="onoff">
                            <label for="Debug1" class="cb-enable @(Model.Debug ? "selected" : "")">@T("是")</label>
                            <label for="Debug0" class="cb-disable @(!Model.Debug ? "selected" : "")">@T("否")</label>
                            <input id="Debug1" name="Debug" value="1" type="radio" @(Model.Debug ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                            <input id="Debug0" name="Debug" value="0" type="radio" @(!Model.Debug ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        </div>
                    </td>
                    <td class="vatop tips">启用调试时输出详细日志</td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("是否可见")</td>
                    <td class="vatop rowform">
                        <div class="onoff">
                            <label for="Visible1" class="cb-enable @(Model.Visible ? "selected" : "")">@T("是")</label>
                            <label for="Visible0" class="cb-disable @(!Model.Visible ? "selected" : "")">@T("否")</label>
                            <input id="Visible1" name="Visible" value="1" type="radio" @(Model.Visible ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                            <input id="Visible0" name="Visible" value="0" type="radio" @(!Model.Visible ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        </div>
                    </td>
                    <td class="vatop tips">是否在登录页面可见，不可见的提供者只能使用应用内自动登录，例如微信公众号</td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("自动注册")</td>
                    <td class="vatop rowform">
                        <div class="onoff">
                            <label for="AutoRegister1" class="cb-enable @(Model.AutoRegister ? "selected" : "")">@T("是")</label>
                            <label for="AutoRegister0" class="cb-disable @(!Model.AutoRegister ? "selected" : "")">@T("否")</label>
                            <input id="AutoRegister1" name="AutoRegister" value="1" type="radio" @(Model.AutoRegister ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                            <input id="AutoRegister0" name="AutoRegister" value="0" type="radio" @(!Model.AutoRegister ? Html.Raw("checked=\"checked\"") : Html.Raw(""))>
                        </div>
                    </td>
                    <td class="vatop tips">SSO登录后，如果本地没有匹配用户，自动注册新用户，否则跳到登录页，在登录后绑定</td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("应用标识(appid)")</td>
                    <td class="vatop rowform"><input type="text" name="weixin_appid" id="weixin_appid" value="@Model.AppId" class="w300" /></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("应用密钥")</td>
                    <td class="vatop rowform">
                        <input type="text" name="weixin_secret" id="weixin_secret" value="@Model.Secret" class="w300" />
                    </td>
                    <td class="vatop tips">
                        <a href="https://mp.weixin.qq.com/" target="_blank">@T("立即申请")</a>
                    </td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("授权范围")</td>
                    <td class="vatop rowform">
                        <input type="text" name="weixin_scope" id="weixin_scope" value="@Model.Scope" class="w300" />
                    </td>
                    <td class="vatop tips">
                        @T("snsapi_userinfo, 用户手动授权/snsapi_base, 微信内静默授权")
                    </td>
                </tr>
                <tr class="noborder">
                    <td class="required">@T("排序")</td>
                    <td class="vatop rowform">
                        <input type="text" name="Sort" id="Sort" value="@Model.Sort" class="w300" />
                    </td>
                    <td class="vatop tips">较大者在前面</td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td></td>
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    }
</div>

<script type="text/javascript">
    $(function() {
        $('#weixin_form').validate({
            errorPlacement: function(error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                weixin_appid : {
                    required: true,
                },
                weixin_secret : {
                    required: true,
                },
                weixin_scope: {
                    required: true,
                }
            },
            messages: {
                weixin_appid : {
                    required: '@T("请添加应用标识")',
                },
                weixin_secret : {
                    required: '@T("请添加应用密钥")',
                },
                weixin_scope: {
                    required: '@T("请添加授权范围")',
                },
            }
        });
    });
</script>