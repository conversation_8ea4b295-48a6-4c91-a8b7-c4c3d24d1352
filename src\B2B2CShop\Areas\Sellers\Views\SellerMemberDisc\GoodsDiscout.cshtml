@using B2B2CShop.Entity
@{
    ViewBag.LeftMenu = "Operation";
    ViewBag.LeftChileMenu = "MemberDisc";
    PekHtml.AppendPageCssClassParts("html-sellermemberdisc-page");
    PekHtml.AppendTitleParts(T("设置商品会员等级折扣").Text);
}
@await Html.PartialAsync("_Left")

<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li><a href="@Url.Action("MemberDiscout")">@T("设置会员等级折扣")</a></li>
                <li class="current"><a href="@Url.Action("GoodsDiscout")">@T("设置商品会员等级折扣")</a></li>
            </ul>
            <a class="dssc-btn dssc-btn-green" href="@Url.Action("AddGoodsDiscout")"><i class="iconfont">&#xe6db;</i>@T("新增商品会员折扣")</a>
        </div>
        <div class="p20">
            <table class="dssc-default-table">
                <thead>
                    <tr>
                        <th class="w180">@T("商品名称")</th>
                        <th class="w180">@T("折扣率")</th>
                        <th class="w150">@T("操作")</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (Goods item in Model.list)
                    {
                        <tr class="bd-line">
                            <td>@item.Name</td>
                            <td>
                                @foreach (var diccount in item.MgDiscountDto)
                                {
                                    @(diccount.GradeName +"/" +diccount.Discount + T("折")) <br/>
                                }
                            </td>
                            <td class="dscs-table-handle tc">
                                <span>
                                    <a href="@Url.Action("EditGoodsDiscout",new{id = item.Id})" class="btn-green">
                                        <i class="iconfont">&#xe734;</i>
                                        <p>@T("编辑")</p>
                                    </a>
                                </span>
                                <span>
                                    <a href="javascript:;" dstype="btn_del_mgdiscount" data_goods_id="@item.Id" class="btn-red">
                                        <i class="iconfont">&#xe725;</i>
                                        <p>@T("删除")</p>
                                    </a>
                                </span>
                            </td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="20">
                            <div class="pagination"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <form id="submit_form" action="" method="post">
                <input type="hidden" id="goodsId" name="goodsId" value="">
            </form>

            <script type="text/javascript">
                $(document).ready(function () {
                    $('[dstype="btn_del_mgdiscount"]').on('click', function () {
                        var action = "@Url.Action("DeleteDiscount")";
                        var goods_id = $(this).attr('data_goods_id');
                        layer.confirm('您确定要删除吗?', {
                            btn: ['确定', '取消'],
                            title: false,
                        }, function () {
                            $.post(action, { goodsId: goods_id }, function (res) {
                                if (res && res.success) {
                                    layer.msg('@T("删除成功")', { time: 800,icon:1}, function () {
                                        // 刷新当前页面
                                        location.reload();
                                    });
                                } else {
                                    layer.msg(res?.msg || '@T("删除失败，请重试")',{icon:2});
                                }
                            }, 'json').fail(function () {
                                layer.alert('@T("请求失败，请重试")');
                            });
                        });
                    });
                });
            </script>

        </div>
    </div>
</div>
