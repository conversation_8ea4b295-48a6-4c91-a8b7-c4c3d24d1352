﻿using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;
using System.Dynamic;
using System.Net.WebSockets;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>仓库管理</summary>
[DisplayName("仓库管理")]
[Description("多仓库管理，包含国内仓和海外仓")]
[AdminArea]
[DHMenu(100, ParentMenuName = "WareHouse", ParentMenuDisplayName = "仓库", ParentMenuUrl = "~/{area}/WareHouse", ParentMenuOrder = 55, CurrentMenuUrl = "~/{area}/WareHouse", CurrentMenuName = "WareHousesList", CurrentIcon = "&#xe652;", LastUpdate = "20241219")]
public class WareHouseController : PekCubeAdminControllerX {

    /// <summary>
    /// 仓库管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("仓库管理")]
    public IActionResult Index(string name, int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();
        name = name.SafeString().Trim();
        var data = WareHouse.FindAll().Select(e => new WareHouse { Id = e.Id, Name = WareHouseLan.FindByWIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name, Code = e.Code, Enabled = e.Enabled })
            .WhereIf(name.IsNotNullAndWhiteSpace(), e => e.Name.Contains(name))
            .OrderByDescending(e => e.Id)
            .ToList();
        var list = data.Skip((page - 1) * limit).Take(limit).ToList();
        var pageCount = data.Count / limit;
        if (data.Count % limit > 0) pageCount++;
        viewModel.list = list;
        viewModel.name = name;
        viewModel.PageHtml = PageHelper.CreatePage(page, data.Count, pageCount, Url.Action("Index"), new Dictionary<string, string> { { "name", name } });
        return View(viewModel);
    }
    /// <summary>
    /// 新增仓库
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增仓库")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add()
    {
        var list = Country.FindAll(Country._.IsEnabled == true).Select(e => new Country { Id = e.Id, Name = CountryLan.FindByCIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name }).ToList();
        ViewBag.CountryList = list; //获取全部国家
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View();
    }
    /// <summary>
    /// 新增仓库
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增仓库")]
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    public IActionResult Add(string name,string code,int countryId,string address,string uName,string uMobile)
    {
        if (name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("仓库名称不能为空") });
        }
        var exit = WareHouse.FindByName(name);
        if(exit != null)
        {
            return Prompt(new PromptModel { Message = GetResource("仓库名称已存在") });
        }
        var country = Country.FindById(countryId);
        if (countryId == 0 || country == null)
        {
            return Prompt(new PromptModel { Message = GetResource("所属国家不能为空") });
        }
        var entity = new WareHouse()
        {
            Name = name,
            Code = code,
            CountryId = country.Id,
            CountryCode = country.TwoLetterIsoCode,
            Enabled = true,
            Address = address,
            UName = uName,
            UMobile = uMobile,
        };
        entity.Insert();
        if (LocalizationSettings.Current.IsEnable)
        {
            var languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            using (var tran1 = WareHouseLan.Meta.CreateTrans())
            {
                foreach (var item in languagelist)
                {
                    var ex = new WareHouseLan();
                    ex.WId = entity.Id;
                    ex.Name = (GetRequest($"[{item.Id}].name")).SafeString().Trim();
                    ex.Address = (GetRequest($"[{item.Id}].address")).SafeString().Trim();
                    if (ex.Name.IsNullOrWhiteSpace())
                    {
                        continue;
                    }
                    ex.LId = item.Id;
                    ex.Insert();
                }
                tran1.Commit();
            }
        }
        return MessageTip(GetResource("添加成功"));
    }
    /// <summary>
    /// 删除仓库
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除仓库")]
    public IActionResult Delete(String Ids)
    {
        var res = new DResult();
        foreach (var item in Ids.Split(","))
        {
            var sum = WareHouseMaterial.FindAll(WareHouseMaterial._.WareHouseId == item).Sum(e => e.Quantity);
            if(sum > 0)
            {
                res.success = true;
                res.code = 10000;
                res.msg = GetResource("仓库下还有物料 无法删除");
                return Json(res);
            }
        }

        WareHouse.Delete(WareHouse._.Id.In(Ids.Trim(',')));
        WareHouse.Meta.Cache.Clear("");

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
    /// <summary>
    /// 修改状态
    /// </summary>
    /// <returns></returns>
    [DisplayName("修改状态")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult ModifyState(Int32 Id, Boolean Status)
    {
        var result = new DResult();

        var model = WareHouse.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("状态调整出错");
            return Json(result);
        }

        model.Enabled = Status;
        model.Update();

        result.success = true;
        result.msg = GetResource("状态调整成功");

        return Json(result);
    }
    /// <summary>
    /// 编辑仓库
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [DisplayName("编辑仓库")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int32 Id)
    {
        var model = WareHouse.FindById(Id);

        if (model == null)
        {
            return Content(GetResource("仓库不存在"));
        }
        var list = Country.FindAll(Country._.IsEnabled == true).Select(e => new Country { Id = e.Id, Name = CountryLan.FindByCIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name }).ToList();
        ViewBag.CountryList = list; //获取全部国家
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(model);
    }
    /// <summary>
    /// 编辑仓库
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [DisplayName("编辑仓库")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult Edit(int Id, string name, string code, int countryId, string address, string uName, string uMobile)
    {
        var model = WareHouse.FindById(Id) ?? new WareHouse();
        if (name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("仓库名称不能为空") });
        }
        var country = Country.FindById(countryId);
        if (countryId == 0 || country == null)
        {
            return Prompt(new PromptModel { Message = GetResource("所属国家不能为空") });
        }
        model.Name = name;
        model.Code = code;
        model.CountryId = country.Id;
        model.CountryCode = country.TwoLetterIsoCode;
        model.Address = address;
        model.UName = uName;
        model.UMobile = uMobile;
        model.Update();
        return MessageTip(GetResource("编辑成功"));
    }

    /// <summary>
    /// 仓库物料
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [DisplayName("仓库物料")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult WareHouseMaterialList(Int32 Id,int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };
        var list = WareHouseMaterial.FindAll(WareHouseMaterial._.WareHouseId == Id & WareHouseMaterial._.Quantity > 0, pages);
         viewModel.list = list;
        viewModel.page = page;
        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("WareHouseMaterialList"), new Dictionary<string, string> { });
        return View(viewModel);
    }
}
