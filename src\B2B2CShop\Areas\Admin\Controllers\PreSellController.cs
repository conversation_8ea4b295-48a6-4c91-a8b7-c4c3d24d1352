﻿using B2B2CShop.Common;
using B2B2CShop.Entity;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Timing;
using SixLabors.ImageSharp;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{

    [DisplayName("预售活动")]
    [Description("预售活动管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Marketing", ParentMenuDisplayName = "营销", ParentMenuUrl = "~/{area}/PreSell", ParentMenuOrder = 65, CurrentMenuUrl = "~/{area}/PreSell", CurrentMenuName = "PreSellList", CurrentIcon = "&#xe734;", LastUpdate = "20241203", CurrentVisible = false)]
    public class PreSellController : PekCubeAdminControllerX
    {

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("预售活动列表")]
        public IActionResult Index(string name, long storeId, int status = -1, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };

            viewModel.list = Presell.Search(name, storeId, status, pages);
            viewModel.storelist = Store.FindAll();
            viewModel.name = name;
            viewModel.storeId = storeId;
            viewModel.status = status;
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {
                { "name", name },
                { "storeId", storeId.SafeString() },
                { "status", status.SafeString() },
            });
            return View(viewModel);
        }

        

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("取消活动")]
        [HttpPost]
        public async Task<IActionResult> CancelPresell(long id)
        {
            var entity = Presell.FindById(id);
            if (entity == null || entity.Id <= 0)
                return Json(new DResult() { success = false, msg = GetResource("找不到预售活动记录") });
            if (entity.PresellState != 1 && entity.PresellState != 2)
                return Json(new DResult() { success = false, msg = GetResource("只有未开始和进行中的活动才能取消") });
            using (var tran = Presell.Meta.CreateTrans())
            {
                try
                {
                    #region 取消参与活动的订单
                    var orderids = OrderGoods.GetOrderIdByPromotionsId(id, 10);
                    if (orderids.Count > 0)
                    {
                        var orderlist = Order.FindAll(Order._.Id.In(orderids));
                        foreach (var order in orderlist)//批量取消
                        {
                            if (order.OrderState == 15 || order.OrderState == 20)
                            {//进行退款操作
                                RefundReturn refundReturn = new();
                                refundReturn.OrderId = order.Id;
                                refundReturn.OrderSn = order.OrderSn;
                                refundReturn.RefundSn = Guid.NewGuid().SafeString();
                                refundReturn.StoreId = order.StoreId;
                                refundReturn.StoreName = order.StoreName;
                                refundReturn.BuyerId = order.BuyerId;
                                refundReturn.UName = order.BuyerName;
                                refundReturn.GoodsId = 0;
                                refundReturn.OrderGoodsId = 0;
                                refundReturn.CurrencyCode = order.CurrencyCode;
                                refundReturn.CurrencyRate = order.CurrencyRate;
                                refundReturn.SymbolLeft = order.SymbolLeft;
                                refundReturn.SymbolRight = order.SymbolRight;
                                refundReturn.RefundAmount = order.CurOrderAmount;
                                refundReturn.RefundreturnSellerState = 4;
                                refundReturn.RefundreturnSellerTime = UnixTime.ToTimestamp();
                                refundReturn.RefundreturnAdminState = 3;
                                refundReturn.RefundreturnAdminTime = UnixTime.ToTimestamp();
                                refundReturn.RefundType = 1;
                                refundReturn.ReturnType = 2;
                                var orderPay = OrderPay.Find(OrderPay._.PaySn == order.PaySn);
                                if (orderPay == null)
                                {
                                    return Json(new DResult() { success = false, msg = GetResource("支付订单不存在") });
                                }
                                if (orderPay.PayOkSn.IsNullOrWhiteSpace())
                                {
                                    return Json(new DResult() { success = false, msg = GetResource("订单未支付成功") });
                                }
                                if (order.PaymentCode == "PayPal")
                                {
                                    var response = await PayPalHelper.OrderRefund(orderPay.PayOkSn);
                                    XTrace.WriteLine($"PayPal退款：{response.ToJson()}");
                                }
                                else if (order.PaymentCode == "ZhiFuBao")
                                {

                                    var rate = Currencies.FindByCode("CNY")?.ExchangeRate ?? 1;

                                    var amount = order.OrderAmount * rate;

                                    var response = AliPayHelper.AliTradeRefund(orderPay.PayOkSn, "预售活动取消", amount.ToString("N2"));
                                    XTrace.WriteLine($"支付宝退款：{response.ToJson()}");
                                    if (response.Code != "10000")
                                    {
                                        return Json(new DResult() { success = false, msg = GetResource("退款失败") });
                                    }
                                }
                                else if (order.PaymentCode == "WeiXin")
                                {

                                    var rate = Currencies.FindByCode("CNY")?.ExchangeRate ?? 1;

                                    var amount = order.OrderAmount * rate * 100;

                                    //var response = WxPayHelper.RefundPay(orderPay.PayOkSn, amount.ToString("N2"));
                                    var response = WxPayHelper.RefundPay(orderPay.PayOkSn, amount.ToInt()).Result;

                                    XTrace.WriteLine($"微信退款：{response.ToJson()}");
                                    if (response.Status != "SUCCESS" && response.Status != "PROCESSING")
                                    {
                                        return Json(new DResult() { success = false, msg = GetResource("退款失败") });
                                    }
                                }
                                MerchantMaterial.UpdateTemporaryQuantity(order.Id);//去除暂扣库存
                                TemporaryInventory.DeleteByOrderId(order.Id);//删除暂扣库存
                                refundReturn.RefundreturnAddTime = UnixTime.ToTimestamp();
                                refundReturn.RefundreturnBuyerMessage = "预售活动取消";
                                refundReturn.Insert();
                            }
                            //添加订单日志
                            var orderlog = new OrderLog()
                            {
                                OrderId = order.Id,
                                LogMsg = "预售活动取消，管理员取消活动",
                                LogTime = UnixTime.ToTimestamp(),
                                LogRole = ManageProvider.User?.RoleName,
                                LogUser = ManageProvider.User?.Name,
                            };
                            orderlog.Insert();
                            order.RefundState = 2;
                            order.OrderState = 0;
                            order.Update();
                        }
                    }
                    #endregion

                    entity.PresellState = 0;
                    entity.Update();
                    tran.Commit();
                }
                catch (Exception ex)
                {
                    return Json(new DResult() { success = true, msg = GetResource("异常：")+ ex.Message });
                }
            }
            
            return Json(new DResult() { success = true, msg = GetResource("取消预售活动成功") });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除活动")]
        public IActionResult DelPresell(long id)
        {
            var entity = Presell.FindById(id);
            if (entity == null)
            {
                return Json(new DResult() { success = false, msg = GetResource("找不到活动记录") });
            }
            if (entity.PresellState == 1 || entity.PresellState == 2)
            {
                return Json(new DResult() { success = false, msg = GetResource("活动正在进行，无法删除") });
            }
            entity.Delete();
            Presell.Meta.Cache.Clear("删除活动", true);
            return Json(new DResult() { success = true, msg = GetResource("操作成功") });

        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐管理")]
        public IActionResult PresellPackage(long storeId, int page = 1, int limit = 10)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "CreateTime",
                Desc = true
            };
            viewModel.storelist = Store.FindAll();
            viewModel.storeId = storeId;
            viewModel.list = PresellQuota.Search(storeId, pages);
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("PresellPackage"), new Dictionary<string, string> {
                { "storeId", storeId.SafeString() },
            });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        public IActionResult PresellSetting()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.price = OperationConfig.GetValueByCode("PresellPackagePrice").ToDecimal();//套餐价格
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("套餐设置")]
        [HttpPost]
        public IActionResult PackageSet(decimal price)
        {
            // 参数校验
            if (price < 0)
            {
                return Json(new DResult() { success = false, msg = GetResource("价格不能为负数") });
            }
            var flag = OperationConfig.UpdateValueByCode("PresellPackagePrice", price.SafeString(), "预售套餐价格");
            if (!flag)
            {
                return Json(new DResult() { success = false, msg = GetResource("未找到预售套餐价格配置") });
            }

            OperationConfig.Meta.Cache.Clear("", true);

            return Json(new DResult() { success = true, msg = GetResource("设置成功") });
        }
    }
}
