﻿<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("规格分类")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="/Biz/GoodsSpec" class="current"><span>@T("管理")</span></a></li>
                <li><a href="/Biz/GoodsSpec/AddSpec"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>
    @* <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <div class="btn_group">
                <a href="@Url.Action("AdditemSetting")" class="btn" title="@T("新增")">@T("新增")</a>
            </div>
        </div>
    </form> *@

    <table class="ds-default-table">
        <thead>
            <tr>
                <th></th>
                <th>@T("规格ID")</th>
                <th>@T("规格名称")</th>
                <th>@T("分类名称")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (GoodSpec item in Model.list)
            {
                <tr>
                    <th><input type="checkbox" class="checkitem" value="@item.Id"></th>
                    <td>@item.Id</td>
                    <td>@item.Name</td>
                    <td>@item.GoodsClassName</td>
                    <td>
                        <a href="@Url.Action("EditSpec", new { Id= item.Id })" class="dsui-btn-edit"><i
                                class="iconfont"></i>@T("编辑")</a>
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete")?ids=' + @item.Id + '','@T("您确定要删除吗?")')" class="dsui-btn-del">
                                <i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small"
                        onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @* @Html.Raw(Model.PageHtml) *@
    </ul>
</div>
@* <div>@T("规格ID")</div>
<div>@item.Id</div>
<div>@T("规格名称")</div>
<div>@item.Name</div>
<div>@T("分类名称")</div>
<div>@item.GoodsClassName</div> *@

<script asp-location="Footer">
    function submit_delete(items) {
        _uri = "@Url.Action("Delete")?ids=" + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
    }
</script>
