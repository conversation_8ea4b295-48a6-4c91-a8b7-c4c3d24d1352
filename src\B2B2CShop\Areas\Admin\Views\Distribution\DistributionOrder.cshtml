﻿@using Pek.Timing
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("分销设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("DistributionSetting")"><span>@T("分销设置")</span></a></li>
                <li><a href="@Url.Action("DistributionGoods")"><span>@T("分销商品")</span></a></li>
                <li><a href="@Url.Action("DistributionMember")"><span>@T("分销员管理")</span></a></li>
                <li><a href="@Url.Action("DistributionMemberLevel")"><span>@T("分销员等级")</span></a></li>
                <li><a href="@Url.Action("DistributionOrder")" class="current"><span>@T("分销员订单")</span></a></li>
            </ul>
        </div>
    </div>

    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("店铺名称")</dt>
                <dd>
                    <select id="storeId" name="storeId" class="form-control" style="width:200px;">
                        <!option value="">@T("全部")</!option>
                        @foreach (var store in Model.storelist)
                        {
                            <!option value="@store.Id" @(Model.storeId == store.Id ? "selected" : "")>@store.Name</!option>
                        }
                    </select>
                </dd>
                <dd>
                    <input type="text" name="searchkey" class="txt" placeholder='@T("用户名/订单号")' value="@Model.searchkey">
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value='@T("搜索")'>
            </div>
        </div>
    </form>
    <table class="ds-default-table">
        <thead>
            <tr class="thead">
                <th class="align-center">@T("分销员用户名")</th>
                <th class="align-center">@T("分销佣金")</th>
                <th class="align-center">@T("分销详情")</th>
                <th class="align-center">@T("订单编号")</th>
                <th class="align-center">@T("店铺名称")</th>
                <th class="align-center">@T("是否有效")</th>
                <th class="align-center">@T("添加时间")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (OrderInviter item in Model.list)
            {
                <tr class="hover member">

                    <td class="align-center">@item.MemberName</td>
                    <td class="align-center">@item.Money</td>
                    <td class="align-center">@T("获得分销员返佣，佣金比例")@(item.Ratio)%，@T("订单号")@(item.OrderSn)</td>
                    <td class="align-center">@item.OrderSn</td>
                    <td class="align-center">@item.StoreName</td>
                    <td class="align-center">@(item.State == 0 ? T("待结算") : item.State == 1 ? T("已结算") : item.State == 2 ? T("无效") : T("未知"))</td>
                    <td class="align-center">@(UnixTime.ToDateTime(item.AddTime))</td>

                </tr>
            }
        </tbody>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>
<script type="text/javascript">
    $(function() {
        $('#storeId').select2({
            placeholder: '@T("请选择店铺")',
            allowClear: true,
        });
    });
</script>