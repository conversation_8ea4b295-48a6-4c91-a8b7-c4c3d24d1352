@{
    // script
    PekHtml.AppendScriptParts(ResourceLocation.Head, "/static/plugins/js/jquery-file-upload/jquery.fileupload.js");

    // css
    PekHtml.AppendScriptParts(ResourceLocation.Footer, "/static/plugins/ueditor/lang/zh-cn/zh-cn.js");
    PekHtml.AppendScriptParts(ResourceLocation.Footer, "/static/plugins/ueditor/ueditor.all.min.js");
    PekHtml.AppendScriptParts(ResourceLocation.Footer, "/static/plugins/ueditor/ueditor.config.js");

    PekHtml.AppendScriptParts(ResourceLocation.Footer, "~/layui/layui.js");
}
<style asp-location="true">
    .page {
        min-height: 430px;
    }

    .html {
        background-color: #FFF;
    }
    .backgroundColor{
        background: #FAFAFA none repeat scroll 0 0;
        border-width: 1px;
        border:1px solid #ccc;
    }
    .width300{
        min-width:300px;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("编辑-定时任务")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("Edit",new { Id = Model!.Id})" class="current"><span>@T("修改")</span></a></li>
            </ul>
        </div>
    </div>

    @using (Html.BeginForm("Edit", "CronJob", FormMethod.Post, new { id = "form2", enctype = "multipart/form-data" }))
    {
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td class="required w120">@T("名称")</td>
                    <td class="vatop rowform"><input type="text" name="name" value="@Model.Name" class="width300" /></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("显示名")</td>
                    <td class="vatop rowform"><input type="text" name="displayName" value="@Model.DisplayName" class="width300" /></td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("Cron表达式")</td>
                    <td class="vatop rowform"><input type="text" name="Cron" value="@Model.Cron" class="width300" /></td>
                    <td class="vatop tips"></td>
                </tr>

                <tr class="noborder">
                    <td class="required w120">@T("命令")</td>
                    <td class="vatop rowform"><input type="text" name="Method" value="@Model.Method" class="width300" /></td>
                    <td class="vatop tips"></td>
                </tr>

                <tr class="noborder">
                    <td class="required">@T("是否启用")</td>
                    <td class="vatop rowform onoff">
                        <label for="article_show1" class="cb-enable @(Model.Enable ? "selected" : "")"><span>@T("是")</span></label>
                        <label for="article_show2" class="cb-disable @(Model.Enable ? "" : "selected")"><span>@T("否")</span></label>
                        <input id="article_show1" name="Enable" @(Model.Enable ? "checked" : "") value="true" type="radio">
                        <input id="article_show2" name="Enable" @(Model.Enable ? "" : "checked") value="false" type="radio">
                    </td>
                    <td class="vatop tips"></td>
                </tr>

                <tr class="noborder">
                    <td class="required w120">@T("参数")</td>
                    <td class="vatop rowform">
                        <textarea row="3" name="Argument" value="@Model.Argument" class="layui-textarea backgroundColor width300">@Model.Argument</textarea>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("内容")</td>
                    <td class="vatop rowform">
                        <textarea row="3"  name="Remark" value="@Model.Remark" class="layui-textarea backgroundColor width300">@Model.Remark</textarea>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" style="min-width:100px;margin-left:150px;" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
        
    }
</div>
<script asp-location="Footer">
        $(function () {
        $('#form2').validate({
            errorPlacement: function (error, element) {
                // console.log();
                error.appendTo(element.parent().parent().find('td:last'));
            },
             rules: {
                 name : {
                    required: true
                 },
                 displayName: {
                     required: true
                 },
                 Cron: {
                     required: true
                 },
                 Method: {
                     required: true
                 }
            },
            messages: {
                name : {
                    required: '@T("名称不能为空")'
                },
                displayName: {
                    required: '@T("显示名称不能为空")'
                },
                Cron: {
                   required: '@T("Cron表达式不能为空")'
                },
                Method:{
                   required: '@T("命令不能为空")'
                },
            }
        });
    })
</script>
