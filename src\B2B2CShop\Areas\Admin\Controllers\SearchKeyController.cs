﻿using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife.Data;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;
using XCode;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers
{
    /// <summary>搜索关键词</summary>
    [DisplayName("搜索关键词")]
    [Description("用于搜索关键词的管理")]
    [AdminArea]
    [DHMenu(46, ParentMenuName = "Sites", CurrentMenuUrl = "~/{area}/SearchKey", CurrentMenuName = "SearchKeyList", CurrentIcon = "&#xe652;", LastUpdate = "20250325")]
    public class SearchKeyController : PekCubeAdminControllerX
    {
        public IActionResult Index(String key,Int32 page = 1,Int32 limit = 5)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Id",
                Desc = true
            };
            var exp = new WhereExpression();
            if (key.IsNotNullAndWhiteSpace()) exp &= SearchKey._.Name.Contains(key);
            viewModel.list = SearchKey.FindAll(exp, pages).Select(e => new SearchKey
            {
                Id = e.Id,
                Name = (SearchKeyLan.FindBySIdAndLId(e.Id, WorkingLanguage.Id)?.Name).IsNullOrWhiteSpace() ? e.Name : SearchKeyLan.FindBySIdAndLId(e.Id, WorkingLanguage.Id)?.Name,
                Enabled = e.Enabled,
                Color = e.Color,
            });
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> { { "key", key } });
            return View(viewModel);
        }

        /// <summary>
        /// 添加页面
        /// </summary>
        /// <returns></returns>
        public IActionResult Add()
        {
            ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            return View();
        }

        /// <summary>
        /// 添加方法
        /// </summary>
        /// <param name="Name"></param>
        /// <param name="Color"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult Add(String Name, String Color)
        {
            if (Name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("关键字不能为空！") });
            }
            if (Color.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("颜色不能为空！") });
            }
            var NameEx = SearchKey.Find(SearchKey._.Name == Name);
            if (NameEx != null)
            {
                return Prompt(new PromptModel { Message = GetResource("分类属性名称已经存在！") });
            }

            using (var tran = SearchKey.Meta.CreateTrans())
            {
                var modal = new SearchKey()
                {
                    Name = Name,
                    Color = Color,
                    Enabled = true,
                };
                modal.Insert();

                var localizationSettings = LocalizationSettings.Current;
                if (localizationSettings.IsEnable)
                {
                    var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                    foreach (var item in Languagelist)
                    {
                        var ex = new SearchKeyLan();
                        ex.SId = modal.Id;
                        ex.LId = item.Id;
                        ex.Name = (GetRequest($"[{item.Id}].Name")).SafeString().Trim();
                        ex.Insert();
                    }
                }
                tran.Commit();
            }
            return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }

        /// <summary>
        /// 编辑页面
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public IActionResult Edit(Int32 Id)
        {
            var modal = SearchKey.FindById(Id);
            ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            return View(modal);
        }

        /// <summary>
        /// 编辑方法
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="Name"></param>
        /// <param name="Color"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult Edit(Int32 Id, String Name, String Color)
        {
            var modal = SearchKey.FindById(Id);
            if (modal == null)
            {
                return Prompt(new PromptModel { Message = GetResource("关键词不存在！") });
            }
            if (Name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("关键词不能为空！") });
            }
            if (Color.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("颜色不能为空！") });
            }
            var NameEx = SearchKey.Find( SearchKey._.Name == Name & SearchKey._.Id != modal.Id);
            if (NameEx != null)
            {
                return Prompt(new PromptModel { Message = GetResource("关键词已经存在！") });
            }
            using (var tran = SearchKey.Meta.CreateTrans())
            {
                modal.Name = Name;
                modal.Color = Color;
                modal.Update();
                var localizationSettings = LocalizationSettings.Current;
                if (localizationSettings.IsEnable)
                {
                    var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                    foreach (var item in Languagelist)
                    {
                        var ex = SearchKeyLan.FindBySIdAndLId(modal.Id, item.Id);
                        if (ex == null)
                        {
                            ex = new SearchKeyLan();
                        }
                        ex.SId = modal.Id;
                        ex.LId = item.Id;
                        ex.Name = (GetRequest($"[{item.Id}].Name")).SafeString().Trim();
                        ex.Save();
                    }
                }
                tran.Commit();
            }
            return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        public IActionResult Delete(String Ids)
        {
            var res = new DResult();
            if (Ids.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("删除失败");
                return Json(res);
            }

            SearchKey.Delete(SearchKey._.Id.In(Ids.Split(",")));
            SearchKeyLan.Delete(SearchKeyLan._.SId.In(Ids.Split(",")));

            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);
        }
        /// <summary>
        /// 修改状态
        /// </summary>
        /// <returns></returns>
        [DisplayName("修改状态")]
        [HttpPost]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult ModifyState(Int32 Id, Boolean Status)
        {
            var result = new DResult();
            var model = SearchKey.FindById(Id);
            if (model == null)
            {
                result.msg = GetResource("状态调整出错");
                return Json(result);
            }

            model.Enabled = Status;
            model.Update();

            result.success = true;
            result.msg = GetResource("状态调整成功");

            return Json(result);
        }

    }
}
