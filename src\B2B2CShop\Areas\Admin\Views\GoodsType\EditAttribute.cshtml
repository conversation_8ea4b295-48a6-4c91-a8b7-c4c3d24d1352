﻿@{
    GoodAttribute goodAttribute = Model;
    IList<GoodAttributeValue> goodAttributeValue = ViewBag.GoodAttributeValue;
}
@* <div>属性名称</div>
<div>@goodAttribute.Name</div>
<div>排序</div>
<div>@goodAttribute.Sort</div>
<div>是否显示</div>
<div>@(goodAttribute.Show ? "是" : "否")</div>
@foreach (GoodAttributeValue item in goodAttributeValue)
{
<div>属性值ID</div>
<div>@item.Id</div>
<div>排序</div>
<div>@item.Sort</div>
<div>属性可选值</div>
<div>@item.GoodAttributeName</div>
} *@
<div class="page">
    <form class="" method="post">
        <div class="ncap-form-default">
            <dl>
                <dt>@T("属性名称")</dt>
                <dd>
                    <input type="text" name="attrName" id="attr_name" value="@goodAttribute.Name" />
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl>
                <dt>@T("排序")</dt>
                <dd>
                    <input type="text" name="sort" id="attr_sort" value="@goodAttribute.Sort" />
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl>
                <dt>@T("是否显示")</dt>
                <dd class="onoff">
                <label for="attr_show1" class="cb-enable @(goodAttribute.Show ? "selected" : "")"
                ><span>@T("是")</span></label>
                <label for="attr_show2" class="cb-disable @(goodAttribute.Show ? "" : "selected")"
                ><span>@T("否")</span></label>
                <input id="attr_show1" name="show" @(goodAttribute.Show ? "selected" : "")" value="1" type="radio">
                <input id="attr_show2" name="show" @(goodAttribute.Show ? "" : "selected")" value="0" type="radio">
                </dd>
            </dl>
        </div>
        <table class="ds-default-table">
            <thead>
                <tr>
                    <th width="200">@T("删除")</th>
                    <th width="100">@T("排序")</th>
                    <th>@T("属性可选值")</th>
                    <th width="100">@T("操作")</th>
                </tr>
            </thead>
            <tbody id="tr_model">
                @foreach (GoodAttributeValue item in goodAttributeValue)
                {
                    <tr data-attr="new">
                        <td>
                            <input type="checkbox" data-attr="del" name="ed_value[@item.Id][del]" value="on" checked disabled style="display: none;"/>
                            <input type="checkbox" onclick="remove_tr($(this))"/>
                        </td>
                        <td><input type="text" value="@item.Sort" name="ed_value[@item.Id][sort]" /> </td>
                        <td>
                            <input type="text" value="@item.Name " name="ed_value[@item.Id][name]" />
                        </td>
                        <td>
                            @* <a
                                href="javascript:dsLayerOpen('@Url.Action("EditAttribute", new { id = item.Id })','@T("编辑")-@item.Name')">@T("编辑")</a>
                            <a onclick="remove_tr($(this));" href="JavaScript:void(0);">@T("删除")</a> *@
                        </td>
                    </tr>
                }
            </tbody>
            <tr>
                <td colspan="20">
                    <a id="add_type" class="btn-add-nofloat marginleft" href="JavaScript:void(0);">
                        <span>@T("添加一个属性")</span> </a>
                </td>
            </tr>
            <tr>
                <td colspan="20">
                    <input class="btn" type="submit" value="提交" />
                </td>
            </tr>
        </table>

    </form>
</div>
<!--载入-->
<script>
    $(function () {
        var i = 100;
        var tr_model = 
        `<tr>
            <td> 
                <input type="checkbox" data-attr="del" name="at_value[${i}][del]" value="on" checked disabled style="display: none;"/>
                <input type="checkbox" onclick="remove_tr($(this))"/>
            </td>
            <td><input type="text" class="form-control" name="at_value[${i}][sort]" value="0" /></td>
            <td><input type="text" class="form-control" name="at_value[${i}][name]" value="" /></td>
            <td></td>
        </tr>`;
        $("#add_type").click(function () {
            $('#tr_model > tr:last').after(tr_model.replace(/key/g, 's_' + i));
            i++;
        });
    });
    /** 删除 */
    function remove_tr(e) {
        const delDom = e.parents('tr:first').children('td').children('input[data-attr="del"]');
        if(e.get(0).checked){
            delDom.prop('checked',true);
            delDom.removeAttr('disabled');
        }else{
            delDom.removeAttr('checked');
            delDom.attr('disabled','disabled');
        }
    }
</script>
